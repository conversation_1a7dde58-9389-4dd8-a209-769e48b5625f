import { headers } from "next/headers";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_BLOGS
} from "@/constants";
import axios from "axios";
import {getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";

const getBlogDetails = async (enterpriseCode, blogCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?code=${blogCode}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = [] } = data || {};
      const { seoTitle = "", seoDescription = "" } = result[0] || {};
      return {
        title: seoTitle || "Blog",
        description: seoDescription || "",
      };
    }
  } catch (error) {
    console.log(error);
  }
};

export const getEnterpriseCode = async () => {
    const domainName = getWebsiteHost();
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true`;
    try {
    const res = await fetch(url, {
      cache: "no-store"
    });
    const jsonRes = await res.json();
    const { result = {} } = jsonRes || {};
    const { enterprise_code: enterpriseCode = null } = result || {};
    return enterpriseCode;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
}

export const generateMetadata = async ({ params }) => {
  const { blogCode = null } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode();
    const metaData = await getBlogDetails(enterpriseCode, blogCode);
    return metaData;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
