import { getEnterpriseCode } from "../blogs/location/[locationCode]/[blogCode]/layout";
import { getLabCategories, getLabCategoryByCode, getLabTests, getLabPackages } from "@/api/harbor.service";
import TestsPageClient from './client-new';

export const dynamic = 'force-dynamic';

async function fetchData(categorySlug = null, includeType = null, page = 1) {
  try {
    const enterpriseCode = await getEnterpriseCode();

    // Fetch all categories first - using perPage=100 to ensure we get all categories
    const categoriesData = await getLabCategories(enterpriseCode, { perPage: 100 });

    // If a specific category is requested
    if (categorySlug) {
      // Extract the category code if it's in the format "name-code"
      const slugParts = categorySlug.split('-');
      const categoryCode = slugParts.length > 1 ? slugParts[slugParts.length - 1] : categorySlug;

      // Fetch tests and packages for this category
      const categoryData = await getLabCategoryByCode(enterpriseCode, categorySlug);

      // Extract tests and packages from the category data
      let testsData = [];
      let packagesData = [];

      if (categoryData && categoryData.data && categoryData.data.length > 0) {
        const categoryItem = categoryData.data[0];

        if (categoryItem.labTests && categoryItem.labTests.length > 0) {
          testsData = { data: categoryItem.labTests };
        }

        if (categoryItem.labPackages && categoryItem.labPackages.length > 0) {
          packagesData = { data: categoryItem.labPackages };
        }
      }

      // Combine the results
      return {
        categoryCode,
        categorySlug,
        categories: categoriesData,
        tests: testsData,
        packages: packagesData
      };
    } else {
      // Fetch tests with pagination
      const testsData = await getLabTests(enterpriseCode, { page, perPage: 12 });

      // Fetch packages with pagination
      const packagesData = await getLabPackages(enterpriseCode, { page, perPage: 12 });

      // Return all data
      return {
        categories: categoriesData,
        tests: testsData,
        packages: packagesData
      };
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return {
      categories: { data: [] },
      tests: { data: [] },
      packages: { data: [] }
    };
  }
}

export default async function TestsListingPage({ searchParams }) {
  // Get params from search params
  const categorySlug = searchParams?.category || null;
  const includeType = searchParams?.type || null;
  const pageNumber = searchParams?.page ? parseInt(searchParams.page) : 1;



  // Fetch data based on the category, type, and page
  const data = await fetchData(categorySlug, includeType, pageNumber);


  // Pass the data to the client component
  return <TestsPageClient
    data={data}
    initialCategory={categorySlug}
    initialType={includeType}
    initialPage={pageNumber}
  />;
}
