"use client";

import { AppContext } from "@/app/AppContextLayout";
import { Box } from "@mui/material";
import { getHealthTips } from "@/api/healthtip.service";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";

import { useContext, useEffect, useState } from "react";
import SectionHeading from "../components/sectionHeading";
import HealthTipCard from "../components/home/<USER>/healthTipCard";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

export default function HealthTips (){
  const { websiteData } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const [healthTipsList, setHealthTipsList] = useState([]);

  const fetchHealthTips = async () => {
    try {
      const data = await getHealthTips(enterpriseCode, { list: true });

      console.log(JSON.stringify(data));
      if (data.code === 200) {
        const { result } = data || [];
        console.log("check here " + JSON.stringify(result));
        setHealthTipsList(result);
      }
    } catch (error) {
      console.log("Error while fetching healthtips " + error);
    }
  };

  useEffect(() => {
    fetchHealthTips();
  }, [enterpriseCode]);

  return (
    <>
      {healthTipsList.length > 0 && (
        <SectionLayoutSingleSpecialitySingleHospital>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "48px",
              paddingTop:"120px"
            }}
          >
            <SectionHeading align="left">Health Tips</SectionHeading>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "repeat(2, 1fr)",
                  md: "repeat(2, 1fr)",
                },
                gap: "24px",
              }}
            >
              {healthTipsList.map((healthTipItem, index) => {
                const { code = null } = healthTipItem || {};
                return (
                  <Box key={code} id={`healthTip${index}`}>
                    <HealthTipCard healthTipItem={healthTipItem} />
                  </Box>
                );
              })}
            </Box>
          </Box>
        </SectionLayoutSingleSpecialitySingleHospital>
      )}
    </>
  );

}