import { getHomeComponentsData, getHomeSectionHeadings } from "@/api/harbor.service";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";
import Services from "./Homepage/Services";

const ServicesWrapper = async () => {
    try {
      const domainName = getWebsiteHost();
      
      const servicesData = await getHomeComponentsData(
        { domainName },
        HOME_WIDGET_TYPE.WEBSITE_SERVICE
      );
  
      const headingsData = await getHomeSectionHeadings(
        { domainName },
        HOME_SECTION_HEADING_TYPE.WEBSITE_SERVICE
      );
      if(!servicesData?.result?.websiteServices || !headingsData) {
        return null;
      }
      return <Services services={servicesData?.result?.websiteServices || []} headings={headingsData || []} />;
    } catch (error) {
      console.error("Error fetching services:", error);
      return <div>Failed to load services.</div>;
    }
  };
  
  export default ServicesWrapper;
  