import { getEnterpriseCode } from "@/app/oasis/blogs/[blogCode]/layout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PAGE,
} from "@/constants";
import axios from "axios";

const getCustomPageDetails = async (
  enterpriseCode,
  pageCode,
  isCallForMetaData = false
) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PAGE}?code=${pageCode}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = [] } = data || {};
      const { seoTitle = "", seoDescription = "" } = result[0] || {};
      if (isCallForMetaData) {
        return {
          title: seoTitle || "",
          description: seoDescription || "",
        };
      } else {
        return result[0];
      }
    }
  } catch (error) {
    console.log(error);
  }
};

export const generateMetadata = async ({ params }) => {
  const { pageCode = null } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode();
    const metaData = await getCustomPageDetails(enterpriseCode, pageCode, true);
    return metaData;
  } catch (error) {
    console.log(error);
  }
};

export default function CustomPageLayout({ children }) {
  return <>{children}</>;
}
