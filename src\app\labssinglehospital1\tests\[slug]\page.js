import React from "react";
import { getEnterpriseCode } from "../../blogs/location/[locationCode]/[blogCode]/layout";
import { getLabTestByCode, getLabPackageByCode } from "@/api/harbor.service";
import TestDetailPage from "./client";
import TestStructureDataScript from "../../components/testStructureData";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
} from "@/constants";


async function fetchFAQs(categoryCode) {
  try {
    if (!categoryCode) return [];

    const domainName = getWebsiteHost();

    const faqUrl = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}/website/website-data/faq?domainName=${domainName}&categoryCode=${categoryCode}`;

    const faqResponse = await axios.get(faqUrl, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    const faqData = faqResponse.data?.result?.faqs || [];

    return faqData;
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    return [];
  }
}

async function fetchTestDetail(slug) {
  try {
    const enterpriseCode = await getEnterpriseCode();

    // Extract the code from the slug if it's in the format "name-code"
    const slugParts = slug.split('-');
    const code = slugParts[slugParts.length - 1];

    // Try to fetch as a test first
    const testData = await getLabTestByCode(enterpriseCode, code);

    if (testData && testData.data && testData.data.length > 0) {
      // Fetch FAQs for this test
      const faqs = await fetchFAQs(code);

      return {
        itemData: testData.data[0],
        itemType: 'test',
        faqs: faqs
      };
    }

    // If not found as a test, try as a package
    const packageData = await getLabPackageByCode(enterpriseCode, code);

    if (packageData && packageData.data && packageData.data.length > 0) {
      // Fetch FAQs for this package
      const faqs = await fetchFAQs(code);

      return {
        itemData: packageData.data[0],
        itemType: 'package',
        faqs: faqs
      };
    }

    // If neither found, return null
    return null;
  } catch (error) {
    console.error("Error fetching test/package detail:", error);
    return null;
  }
}

export default async function TestDetailPageWrapper({ params }) {
  const { slug } = params;



  // Fetch data based on the slug
  const data = await fetchTestDetail(slug);

  // If no data found, return a simple message
  if (!data) {
    return (
      <div>
        <h1>Test or Package Not Found</h1>
        <p>The requested test or package could not be found.</p>
      </div>
    );
  }

  // Pass the data to the client component
  return (
    <>
      {/* Add structured data for the test/package */}
      <TestStructureDataScript
        testData={data.itemData}
        itemType={data.itemType}
      />
      <TestDetailPage data={data} />
    </>
  );
}
