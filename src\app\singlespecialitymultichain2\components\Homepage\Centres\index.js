import { Box, Typography,  Container } from "@mui/material"
import { getHomeComponentsData, getHomeSectionHeadings } from "@/api/harbor.service"
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants"
import CentresSlider from "../../CentresSlider"
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import CentresHeadings from "./CentresHeadings";

const fetchCentres = async () => {
      try {
        const domainName = getWebsiteHost();
        const data = await getHomeComponentsData({ domainName }, HOME_WIDGET_TYPE.CENTERS);
        return  data?.result?.centers || [];
      } catch (error) {
        console.error("Error fetching centres:", error);
        return [];
      }
    };

    const getCentresHeadings = async () => {
      try {
          const domainName = getWebsiteHost();
          const data = await getHomeSectionHeadings(
              {domainName: domainName},
              HOME_SECTION_HEADING_TYPE.CENTERS
          );
          if (data.code === 200) {
            return data?.result || []
          } else
              return []
      } catch (error) {
          console.error("Error fetching centers data:", error);
          return [];
      }
    };



export default async function HospitalNetwork() {
  const hospitals = await fetchCentres()
  const headings = await getCentresHeadings()
  const heading = headings[0]?.heading
   const subHeading = headings[0]?.subHeading
   if (hospitals.length <= 0 ){
    return (
        <></>
    )
}
 
  return (
    <Container maxWidth="xl" sx={{ pb: 5, pt:4 }}>
      <Box
        sx={{
          position: "relative",
          mt: 2,
          // overflow: "hidden",
          height: { xs: "700px", md: "400px" },
        }}
      >
        {/* Left side content */}
        <CentresHeadings heading={heading} subHeading={subHeading}/>
        <Box
          sx={{
            position: "absolute",
            right: 0,
            top: 0,
            bottom: 0,
            width: { xs: "100%", md: "70%" },
            pt: { xs: "280px", md: 0 }, 
          }}
        >
          <CentresSlider hospitals={hospitals}/>
        </Box>
      </Box>
    </Container>
  )
}

