import React, { useContext, useEffect, useState } from "react";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Drawer,
  IconButton,
  Typography,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import { useRouter } from "next/navigation";

import Link from "next/link";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_NAVBAR,
} from "@/constants";
import NavbarDropdown from "./components/navbarDropdown";
import { useTheme } from "@emotion/react";
import CloseIcon from "@mui/icons-material/Close";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import useStyles from "@/app/singlespecialitymultichain1/styles";
import { AppContext } from "@/app/AppContextLayout";
import CallUsButton from "./components/callUsButton";

const Navbar = () => {
  const classes = useStyles();
  const router = useRouter();
  const { desktopView, websiteData } = useContext(AppContext);
  const theme = useTheme();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [navbarItemsList, setNavbarItemsList] = useState([]);
  const {
    logo_url: logoUrl = null,
    domainName = "",
    doctors = [],
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};

  const handleCloseMenu = () => {
    setIsMenuOpen(false);
  };

  const getNavbarItems = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_NAVBAR}${enterpriseCode}/`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        // sortByPriority(result);
        setNavbarItemsList(result);
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    if (enterpriseCode) getNavbarItems();
  }, [enterpriseCode]);

  return (
    <Box className={classes.navbar}>
      <Box
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {logoUrl ? (
          <Link href="/">
            <Image
              alt="logo"
              src={getThumborUrl(logoUrl)}
              width={210}
              height={70}
              style={{ cursor: "pointer" }} // Make it look clickable
            />
          </Link>
        ) : (
          <Link href="/">
            <Typography
              variant="h4"
              className={classes.navbarLogoText}
              style={{ cursor: "pointer" }} // Make it look clickable
            >
              {"Logo"}
            </Typography>
          </Link>
        )}
        {desktopView ? (
          <Box>
            <ul className={classes.navbarList}>
              {(navbarItemsList[0]?.sections || []).map((item, index) => {
                const {
                  displayName = "",
                  redirection = {},
                  sections = null,
                  type = 1,
                } = item || {};
                const { redirectionUrl = "" } = redirection || {};
                if (type === 2)
                  return (
                    <Box
                      id={`navbarSection0Item${index}`}
                      sx={{
                        "&:hover": { color: "primary.main" },
                        color: "#333333",
                      }}
                    >
                      <Link href={redirectionUrl} target="_blank">
                        <Typography fontSize="16px">
                          {displayName || ""}
                        </Typography>
                      </Link>
                    </Box>
                  );
                else if (sections)
                  return (
                    <NavbarDropdown
                      style={{
                        color: "#333333",
                      }}
                      key={`${displayName}${index}`}
                      navbarItem={item}
                      id={`navbarSection0Item${index}`}
                    />
                  );
                else
                  return (
                    <Box
                      id={`navbarSection0Item${index}`}
                      sx={{
                        "&:hover": { color: "primary.main" },
                        color: "#333333",
                      }}
                    >
                      <Link href={redirectionUrl}>
                        <Typography fontSize="16px">
                          {displayName || ""}
                        </Typography>
                      </Link>
                    </Box>
                  );
              })}
              <Button
                variant="contained"
                color="primary"
                style={{ fontWeight: "400", textTransform: "none" }}
                sx={{ color: "#ffffff" }}
                // onClick={() => router("/contact-us")}
              >
                <Link
                  // className={classes.navbarListItem}
                  href="/contact-us"
                  //   href={doctors.length === 1
                  //       ? `/doctors/${doctors[0]?.doctorDetails?.seoSlug}`  // Redirect to single doctor's profile
                  //       : "/doctors"}
                >
                  Book an Appointment
                </Link>
              </Button>
            </ul>
          </Box>
        ) : (
          <Box sx={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <CallUsButton />
              <Divider
                orientation="vertical"
                flexItem
                sx={{ display: { xs: "inline-block", color: "#333333", md: "none" } }}
              />
            </Box>

            <Box>
            <IconButton onClick={() => setIsMenuOpen((prev) => !prev)}>
              <MenuIcon />
            </IconButton>
            <Drawer
              anchor="right"
              open={isMenuOpen}
              onClose={() => setIsMenuOpen(false)}
            >
              <CloseIcon
                sx={{ cursor: "pointer", margin: "8px", color: "#333333" }}
                onClick={() => setIsMenuOpen(false)}
              />
              <Box style={{ width: "15rem" }}>
                <ul
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "16px",
                    padding: "16px",
                  }}
                >
                  {(navbarItemsList[0]?.sections || []).map((item, index) => {
                    const {
                      displayName = "",
                      redirection = {},
                      sections = null,
                      type = 1,
                    } = item || {};
                    const { redirectionUrl = "" } = redirection || {};
                    if (type === 2)
                      return (
                        <Box
                          id={`navbarSection0Item${index}`}
                          sx={{
                            "&:hover": { color: "primary.main" },
                            color: "#333333",
                            padding: "8px 0",
                          }}
                        >
                          <Link href={redirectionUrl} target="_blank">
                            <Typography fontSize="16px">
                              {displayName || ""}
                            </Typography>
                          </Link>
                        </Box>
                      );
                    else if (sections)
                      return (
                        <NavbarDropdown
                          key={`${displayName}${index}`}
                          navbarItem={item}
                          id={`navbarSection0Item${index}`}
                          sx={{ padding: "8px 0" }}
                        />
                      );
                    else
                      return (
                        <Box
                          id={`navbarSection0Item${index}`}
                          sx={{
                            "&:hover": { color: "primary.main" },
                            color: "#333333",
                            padding: "8px 0",
                          }}
                        >
                          <Link href={redirectionUrl}>
                            <Typography fontSize="16px">
                              {displayName || ""}
                            </Typography>
                          </Link>
                        </Box>
                      );
                  })}
                  <Button
                    variant="contained"
                    color="primary"
                    style={{ fontWeight: "400", textTransform: "none" }}
                    sx={{ color: "#ffffff" }}
                  >
                    <Link smooth href={"/contact-us"} sx={{ color: "#333333" }}>
                      Book an Appointment
                    </Link>
                  </Button>
                </ul>
              </Box>
            </Drawer>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default Navbar;
