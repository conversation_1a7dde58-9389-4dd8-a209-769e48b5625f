import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import Box from "@mui/material/Box";

const ckeditorWidget = ({value = [], theme = {}, title = ""}) => {

    const ckeditorWidget = value.slice(0, 1);
    return (

        <Box
            sx={{
                maxWidth: "1400px",
                mx: "auto",
                color: "#333",
                paddingBottom: {xs: "16px", md: "24px", lg: "48px"},
                paddingTop: {xs: "0px", md: "24px", lg: "48px"},
                px: { xs: 2, md: 4 },
            }}>
            <div
                className="ck-content"
                dangerouslySetInnerHTML={{__html: ckeditorWidget.length > 0 ? ckeditorWidget[0].content : ""}}/>
        </Box>
    );
};

export default ckeditorWidget;
