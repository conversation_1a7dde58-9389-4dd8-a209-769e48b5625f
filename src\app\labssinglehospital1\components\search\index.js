'use client'
import { Box, CircularProgress } from '@mui/material';
import { useState, useMemo, lazy, Suspense } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addToCart, removeFromCart } from '../../redux/slices/cartSlice';

// Dynamically import components
const SearchResultsList = lazy(() => import('./SearchResultsList'));
const PopularItemsList = lazy(() => import('./PopularItemsList'));
const NotificationSnackbar = lazy(() => import('./NotificationSnackbar'));

const SearchResults = ({
  searchQuery,
  searchResults,
  popularPackages = [],
  popularTests = [],
  isLoadingPopular = false,
  isLoadingSearch = false,
  onCloseSearch
}) => {
  const dispatch = useDispatch();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Get all cart items once for the entire component
  const cartItems = useSelector(state => state.cart.items);

  // Create a memoized lookup map for cart items to improve performance
  const cartItemsMap = useMemo(() => {
    const map = {};
    cartItems.forEach(item => {
      map[`${item.id}-${item.itemType}`] = true;
    });
    return map;
  }, [cartItems]);

  // Function to check if an item is in the cart - optimized with map lookup
  const isItemInCart = (id, itemType) => {
    return !!cartItemsMap[`${id}-${itemType}`];
  };

  const handleAddToCart = (result) => {
    // Determine if this is a test or package based on some property
    const itemType = result.isPackage ? 'package' : 'test';

    dispatch(addToCart({
      ...result,
      itemType
    }));
    setSnackbarMessage(`${result.title} added to cart`);
    setSnackbarOpen(true);
  };

  const handleRemoveFromCart = (result) => {
    // Determine if this is a test or package based on some property
    const itemType = result.isPackage ? 'package' : 'test';

    dispatch(removeFromCart({
      id: result.id,
      itemType
    }));
    setSnackbarMessage(`${result.title} removed from cart`);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = (_, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  return (
    <>
      <Box sx={{
        maxWidth: '800px',
        margin: '0 auto',
        px: { xs: 1, sm: 2, md: 0 },
        display: 'flex',
        flexDirection: 'column',
        flex: 1,
        overflow: 'hidden'
      }}>
        <Suspense fallback={
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <CircularProgress />
          </Box>
        }>
          {searchQuery.length >= 3 ? (
            <SearchResultsList
              searchQuery={searchQuery}
              searchResults={searchResults}
              isLoading={isLoadingSearch}
              isItemInCart={isItemInCart}
              onAddToCart={handleAddToCart}
              onRemoveFromCart={handleRemoveFromCart}
              onNavigate={onCloseSearch}
            />
          ) : (
            <PopularItemsList
              popularPackages={popularPackages}
              popularTests={popularTests}
              isLoading={isLoadingPopular}
              isItemInCart={isItemInCart}
              onAddToCart={handleAddToCart}
              onRemoveFromCart={handleRemoveFromCart}
              onNavigate={onCloseSearch}
            />
          )}
        </Suspense>
      </Box>

      <Suspense fallback={null}>
        <NotificationSnackbar
          open={snackbarOpen}
          message={snackbarMessage}
          onClose={handleCloseSnackbar}
        />
      </Suspense>
    </>
  );
};

export default SearchResults;
