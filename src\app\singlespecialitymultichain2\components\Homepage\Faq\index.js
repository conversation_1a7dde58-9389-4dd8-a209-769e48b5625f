import {
  Box,
  Container,
  Typography,
} from "@mui/material";
import FaqClient from "../../FaqClient";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import { getHomeComponentsData, getHomeSectionHeadings } from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";

const getFaqHeadings = async () => {
  try {
      const domainName = getWebsiteHost();
      console.log(domainName)
      const data = await getHomeSectionHeadings(
          {domainName: domainName},
          HOME_SECTION_HEADING_TYPE.FAQ
      );

      if (data.code === 200) {
        return data?.result || []
      } else
          return []
  } catch (error) {
      console.error("Error fetching faq data:", error);
      return [];
  }
};

const fetchFaqs = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData({ domainName }, HOME_WIDGET_TYPE.FAQ);
    return data?.code === 200 ? data?.result?.faqs : [];
  } catch (error) {
    console.error("Error fetching FAQ data:", error);
    return [];
  }
};
export default async function FAQSection() {
  const faqs = await fetchFaqs();
  const headings = await getFaqHeadings()

   const heading = headings[0]?.heading
   const iconUrl = headings[0]?.iconUrl
   if (!faqs || faqs.length === 0) {
    return null;
   }

  return (
    <Container maxWidth="lg" sx={{ py:"32px" }}>
      <Box
        component="section"
        sx={{
          position: "relative",
          maxWidth: "2150px",
          mx: "auto",
          width: "100%",
          height: { xs: "auto", md: "auto" }, // Fixed height for desktop
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Box
          sx={{
            textAlign: "center",
            mb: { xs: 4, sm: 5, md: 3 },
            maxWidth: "800px",
            mx: "auto",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              color: "#1a1a1a",
              mb: 2,
              fontSize: {
                xs: '24px',
                md: '36px', 
              },
              color:"text.black",
              lineHeight: 1.2,
            }}
          >
            {heading || "Health FAQs"}
            
          </Typography>
        </Box>
        <FaqClient faqs={faqs} iconUrl={iconUrl}/>
      </Box>
    </Container>
  );
}
