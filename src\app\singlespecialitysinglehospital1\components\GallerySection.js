"use client";
import { useContext, useState } from "react";
import {
  <PERSON>,
  Dialog,
  <PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  Typography,
  Modal,
  IconButton,
  useMediaQuery,
} from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import { styled } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";
import { useRouter } from "next/navigation";
import { AppContext } from "@/app/AppContextLayout";

export default function GallerySection({ multiMedia = [] }) {
  const router = useRouter();
  // Filter out items with video_url
  const photos = multiMedia.filter((item) => item.video_url === null);

  const [openModal, setOpenModal] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(null);

  const handleOpenModal = (index) => {
    setCurrentImageIndex(index);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === photos.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handlePrevImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? photos.length - 1 : prevIndex - 1
    );
  };
  return (
    <>
      <SectionLayoutSingleSpecialitySingleHospital>
        <Box
          sx={{
            textAlign: "center",
            mb: { xs: 4, sm: 5, md: 3 },
            mx: "auto",
          }}
        >
          <Typography
            variant="h5"
            sx={{
              color: "primary.main",
              mb: 1,
              display: "block",
              fontSize: { xs: "1.125rem", md: "1.25rem" },
            }}
          >
            OUR GALLERY
          </Typography>
          <Typography
            variant="h3"
            sx={{
              color: "#1a1a1a",
              mb: 2,
              fontSize: { xs: "1.875rem", sm: "2.25rem", md: "2.5rem" },
              lineHeight: 1.2,
            }}
          >
            Gallery of Equipments & Procedures
          </Typography>

          <Swiper
            modules={[Autoplay, Pagination]}
            loop={true}
            pagination={{
              dynamicBullets: true,
              clickable: true,
              bulletActiveClass: "swiper-pagination-bullet-active-main",
              bulletClass: "swiper-pagination-bullet",
            }}
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
            }}
            spaceBetween={24}
            slidesPerView={1}
            breakpoints={{
              768: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
            }}
            style={{
              padding: "12px",
              paddingBottom: "40px",
              margin: "10px 0",
            }}
            className="photo-carousel"
          >
            {photos?.map((photo, index) => (
              <SwiperSlide key={photo.id} className="photo-slide">
                <Box
                  sx={{
                    height: "100%",
                    boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
                    cursor: "pointer",
                    borderRadius: "12px",
                    overflow: "hidden",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-5px) scale(1.01)",
                      boxShadow: "0 10px 25px rgba(0,0,0,0.2)",
                    },
                    position: "relative",
                  }}
                  onClick={() => handleOpenModal(index)}
                >
                  <Box
                    component="img"
                    src={photo.image_url}
                    alt={`Photo ${index + 1}`}
                    sx={{
                      width: "100%",
                      height: 300,
                      objectFit: "cover",
                      display: "block",
                      transition: "transform 0.5s ease-in-out",
                      "&:hover": {
                        transform: "scale(1.05)",
                      },
                    }}
                  />
                  <Box
                    sx={{
                      position: "absolute",
                      bottom: 0,
                      left: 0,
                      right: 0,
                      background:
                        "linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)",
                      height: "80px",
                      borderBottomLeftRadius: "12px",
                      borderBottomRightRadius: "12px",
                      display: "flex",
                      alignItems: "flex-end",
                      padding: "12px",
                      opacity: 0.8,
                      transition: "opacity 0.3s ease",
                      "&:hover": {
                        opacity: 1,
                      },
                    }}
                  ></Box>
                </Box>
              </SwiperSlide>
            ))}
          </Swiper>
          {/* <Swiper
            style={{ padding: "5px 5px", paddingBottom: "50px" }}
            modules={[Autoplay, Pagination]}
            loop={true}
            pagination={{ dynamicBullets: true, clickable: true }}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            spaceBetween={18}
            slidesPerView={1}
            breakpoints={{
              768: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
            }}
          >
            {photos?.map((photo, index) => (
              <SwiperSlide key={photo.id}>
                <Box
                  sx={{
                    height: "100%",
                    boxShadow: 2,
                    cursor: "pointer",
                    borderRadius: "10px",
                    overflow: "hidden",
                  }}
                  onClick={() => handleOpenModal(index)}
                >
                  <Box
                    component="img"
                    src={photo.image_url}
                    sx={{
                      width: "100%",
                      height: "100%",
                      display: "block",
                      overflow: "hidden",
                      transition: "transform 0.3s ease-in-out",
                      "&:hover": { transform: "scale(1.02)" }, // Scale on hover
                      height: 300,
                      objectFit: "cover",
                    }}
                  />
                </Box>
              </SwiperSlide>
            ))}
          </Swiper> */}
          <Dialog
            open={openModal}
            onClose={handleCloseModal}
            maxWidth="xl"
            fullScreen
            PaperProps={{
              sx: {
                backgroundColor: "rgba(0, 0, 0, 0.49)",
                backgroundImage:
                  "linear-gradient(rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.13))",
              },
            }}
          >
            {/* Close button */}
            <IconButton
              onClick={handleCloseModal}
              sx={{
                position: "absolute",
                right: "20px",
                top: "20px",
                zIndex: 2,
                color: "white",
                backgroundColor: "rgba(255, 255, 255, 0.1)",
                backdropFilter: "blur(4px)",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.2)",
                },
              }}
            >
              <CloseIcon />
            </IconButton>

            {/* Image container */}
            <DialogContent
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: { xs: 2, sm: 4 },
                height: "100vh",
                overflow: "hidden",
              }}
            >
              <Box
                sx={{
                  position: "relative",
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                {/* Main Image */}
                <Box
                  component="img"
                  src={photos[currentImageIndex]?.image_url || ""}
                  alt={`Image ${currentImageIndex + 1}`}
                  sx={{
                    maxWidth: "90%",
                    maxHeight: "85vh",
                    objectFit: "contain",
                    borderRadius: "8px",
                    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
                    transition: "transform 0.3s ease-in-out",
                  }}
                />

                {/* Image counter */}
                <Typography
                  variant="body2"
                  sx={{
                    position: "absolute",
                    bottom: "20px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    color: "white",
                    backgroundColor: "rgba(0, 0, 0, 0.6)",
                    padding: "8px 16px",
                    borderRadius: "20px",
                    backdropFilter: "blur(4px)",
                  }}
                >
                  {currentImageIndex + 1} / {photos.length}
                </Typography>
              </Box>
            </DialogContent>

            {/* Navigation buttons */}
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: 0,
                right: 0,
                transform: "translateY(-50%)",
                display: "flex",
                justifyContent: "space-between",
                px: { xs: 2, sm: 4, md: 6 },
                pointerEvents: "none",
              }}
            >
              <IconButton
                onClick={handlePrevImage}
                sx={{
                  color: "white",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(4px)",
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                  },
                  pointerEvents: "auto",
                }}
              >
                <ArrowBackIcon sx={{ fontSize: 30 }} />
              </IconButton>

              <IconButton
                onClick={handleNextImage}
                sx={{
                  color: "white",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(4px)",
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                  },
                  pointerEvents: "auto",
                }}
              >
                <ArrowForwardIcon sx={{ fontSize: 30 }} />
              </IconButton>
            </Box>
          </Dialog>
          <Box sx={{ display: "flex", mt: 3, justifyContent: "center" }}>
            <Button
              onClick={() => router.push("/gallery")}
              color="primary"
              sx={{
                borderRadius: "100px",
                backgroundColor: "primary.main",
                transform: "scale(1)",
                color: "text.primary",
                textTransform: "capitalize",
                fontWeight: "normal",
                fontSize: "14px",
                padding: "10px 20px",
                transition: "transform 0.3s ease",
                "&:hover": {
                  backgroundColor: "primary.main",
                },
              }}
            >
              View Gallery
            </Button>
          </Box>
        </Box>
      </SectionLayoutSingleSpecialitySingleHospital>
    </>
  );
}
