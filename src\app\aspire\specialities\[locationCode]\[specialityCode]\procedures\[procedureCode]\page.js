"use client";

import QuickEnquiry from "@/app/commoncomponents/quickEnquiry";
import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PROCEDURE,
  API_SECTION_WEBSITE,
  LEAD_SOURCES,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";
import parse from "html-react-parser";
import FaqsSection from "@/app/oasis/components/faqsSection";
import { getParsedHTML } from "@/app/utils/getParsedHTML";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import { alpha } from "@mui/material";
import SectionLayoutAspire from "@/app/aspire/styledComponents/SectionLayoutAspire";
import RelatedLinks from "@/app/oasis/components/RelatedLinks";
import { getWebsiteHost } from "@/app/utils/clientOnly/clientUtils";

export default function ProcedureDetail({ params }) {
  const router = useRouter();
  const {
    procedureCode = null,
    specialityCode = null,
    locationCode = null,
  } = params || {};
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const [procedureDetails, setProcedureDetails] = useState({});
  const [enterpriseCode, setEnterpriseCode] = useState(null);
  const theme = useTheme();
  const {
    displayName: procedureName = "",
    shortDescription = "",
    description = "",
    bannerUrl = "",
    otherProcedures = [],
    faqs = [],
    relatedLinks = [],
  } = procedureDetails || {};
  const getEnterpriseCode = async () => {
    const domainName = getWebsiteHost();
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true&domainSlug=${locationCode}`;
    try {
      const res = await fetch(url, {
        cache: "no-store",
      });
      const jsonRes = await res.json();
      const { result = {} } = jsonRes || {};
      const { enterprise_code: enterpriseCode = null } = result || {};
      setEnterpriseCode(enterpriseCode);
    } catch (error) {
      console.log("getWebsiteData", error);
    }
  };

  const getProcedureDetails = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PROCEDURE}?code=${procedureCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { procedures = [] } = result || {};
        const { speciality = {}, seoSlug: procedureSeoSlug = "" } =
          procedures[0];
        const { seoSlug: specialitySeoSlug = "" } = speciality || {};
        if (
          specialityCode !== specialitySeoSlug ||
          procedureCode !== procedureSeoSlug
        ) {
          router.push(
            `/specialities/${locationCode}/${specialitySeoSlug}/procedures/${procedureSeoSlug}`
          );
        }
        setProcedureDetails(procedures[0] || {});
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    if (enterpriseCode) getProcedureDetails();
  }, [enterpriseCode]);

  useEffect(() => {
    getEnterpriseCode();
  }, []);

  // Preparing data for related links
  const formattedRelatedLinks = relatedLinks
    ? relatedLinks.map((link) => ({
        name: link.displayName || "",
        slug: link.seoSlug || "",
      }))
    : [];

  return (
    <Box>
      <Box
        sx={{
          position: "relative",
          minHeight: "280px",
          bgcolor: alpha(theme.palette.primary.main, 0.7),
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <SectionLayoutAspire>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              alignItems: "center",
            }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{
                color: "#fff",
                fontSize: { xs: "2rem", sm: "2.5rem", md: "40px" },
              }}
            >
              {procedureName || ""}
            </Typography>
            {/* <PrimaryButton style={{ textTransform: "none" }}>
              Find a doctor
            </PrimaryButton> */}
            <Typography
              variant="subtitle1"
              align="center"
              sx={{ color: "#fff", fontSize: "14px" }}
            >
              {shortDescription || ""}
            </Typography>
          </Box>
        </SectionLayoutAspire>
      </Box>
      <SectionLayoutAspire>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", lg: "row" }, // Stack on small screens, side-by-side on large screens
            gap: { xs: "24px", lg: "24px" },
          }}
        >
          <Box
            sx={{
              flex: { lg: "7", xs: "1" }, // 70% width for large screens, full width for small screens
              flexBasis: { lg: "70%", xs: "100%" }, // Explicitly set the percentage width
              maxWidth: { lg: "70%", xs: "100%" }, // Prevent overflow on small screens
            }}
          >
            <div
              className="ck-content"
              dangerouslySetInnerHTML={{ __html: description }} // Render HTML safely
            />
          </Box>
          <Box
            sx={{
              flex: { lg: "3", xs: "1" }, // 30% width for large screens, full width for small screens
              flexBasis: { lg: "30%", xs: "100%" }, // Explicitly set the percentage width
              maxWidth: { lg: "30%", xs: "100%" }, // Prevent overflow on small screens
              position: "sticky", // Sticky positioning for larger screens
              top: "200px", // Adjust how far from the top it sticks
              alignSelf: { xs: "center", lg: "flex-start" }, // Center on small screens, top align on large
              flexShrink: 0, // Prevent shrinking of the box
            }}
          >
            <QuickEnquiry
              pageData={{
                leadSource: LEAD_SOURCES.PROCEDURE_PAGE,
                productCode: procedureCode,
                pageTitle: procedureName,
                specialityCode: specialityCode,
                enterpriseCode: enterpriseCode,
              }}
            />
          </Box>
        </Box>
        {/*<Box>*/}
        {/*  <QuickEnquiry leadSource={4} productCode={procedureCode} />*/}
        {/*</Box>*/}
        <Box>
          {/* Include Related Links component */}
          {formattedRelatedLinks.length > 0 && (
            <RelatedLinks
              links={formattedRelatedLinks}
              basePath={`/specialities/${locationCode}/${specialityCode}/procedures`}
              title="Related Procedures"
            />
          )}
        </Box>
      </SectionLayoutAspire>
      {faqs.length > 0 && <FaqsSection faqs={faqs} />}
    </Box>
  );
}
