"use client";

import { useState } from "react";
import Image from "next/image";
import {
  Box,
  Container,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper,
  Grid,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { ArrowDownward } from "@mui/icons-material";
import { useTheme, useMediaQuery } from "@mui/material";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  border: "none",
  boxShadow: "none",
  "&:before": {
    display: "none",
  },
  "&:not(:last-child)": {
    borderBottom: "1px solid rgba(0, 0, 0, 0.08)",
  },
  backgroundColor: "transparent",
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  padding: theme.spacing(1, 0),
  "& .MuiAccordionSummary-content": {
    margin: theme.spacing(0),
  },
  "& .MuiAccordionSummary-expandIconWrapper": {
    color: theme.palette.primary.main,
  },
}));

const StyledAccordionDetails = styled(AccordionDetails)(({ theme }) => ({
  padding: theme.spacing(0, 3, 3, 0),
}));

const ImageWrapper = styled(Box)(({ theme }) => ({
  position: "relative",
  width: "100%",
  height: "100%",
  minHeight: "400px",
  aspectRatio:"auto",
  borderRadius: theme.spacing(2),
  overflow: "hidden",
  boxShadow: "0 10px 40px -10px rgba(0,0,0,0.1)",
}));
// const ScrollableContainer = styled(Box)(({ theme,height }) => ({
//   height: height,
//   overflowY: "auto",
//   "&::-webkit-scrollbar": {
//     width: "6px",
//   },
//   "&::-webkit-scrollbar-track": {
//     // background: "#f1f1f1",
//     borderRadius: "3px",
//   },
//   "&::-webkit-scrollbar-thumb": {
//     // background: theme.palette.primary.main,
//     borderRadius: "3px",
//     "&:hover": {
//       background: theme.palette.primary.dark,
//     },
//   },
// }));  

const faqs = [
  {
    question: "How frequently should someone visit a dentist?",
    answer:
      "It's recommended to visit a dentist every 6 months for regular check-ups and professional cleaning. However, some patients may need more frequent visits based on their oral health needs.",
  },
  {
    question: "What dental services are provided here?",
    answer:
      "We offer a comprehensive range of dental services including routine check-ups, professional cleaning, fillings, root canals, crowns, bridges, orthodontics, and cosmetic dentistry procedures.",
  },
  {
    question: "Is it safe to visit dentists during pregnancy?",
    answer:
      "Yes, it's safe and actually recommended to visit the dentist during pregnancy. Dental care is essential during pregnancy as hormonal changes can affect oral health. Always inform your dentist about your pregnancy.",
  },
  {
    question: "What can I expect in my first visit?",
    answer:
      "Your first visit will typically include a comprehensive oral examination, medical history review, x-rays if necessary, professional cleaning, and a discussion about your oral health goals and treatment plan.",
  },
  {
    question: "How Frequently Should I Get My Teeth Cleaned?",
    answer:
      "Most dental professionals recommend getting your teeth professionally cleaned every 6 months. However, if you have specific oral health concerns, your dentist might recommend more frequent cleanings.",
  },
];

export default function FAQSection({faqs}) {
  const [expanded, setExpanded] = useState();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <Box sx={{background: `
    //   linear-gradient(
    //     180deg,
    //     ${theme.palette.primary.main}20 0%,
    //       rgba(255, 255, 255, 1) 50%,
    //       ${theme.palette.secondary.main}20 100%
    //   )
    // `,
        backgroundColor:"#f8f8f8",
        padding:0}}>
    <SectionLayoutSingleSpecialitySingleHospital>
      <Box
      component="section"
      sx={{
        position: "relative",
        py: { xs: 4, md: 0 },
        px: { xs: 2, md: 0 },
        maxWidth: "2150px",
        mx: "auto",
        width: "100%",
        height: { xs: "auto", md: "auto" }, // Fixed height for desktop
        display: "flex",
        flexDirection: "column",
      }}
    >
        {/* <Box sx={{ textAlign: "center", mb: 6 }}>
          <Typography
            variant="h5"
            component="h3"
            sx={{
              color: "primary.main",
              mb: 1,
            }}
          >
            FAQ's
          </Typography>

          <Typography
            variant="h3"
            component="h2"
            sx={{
              color: "#1a1a1a",
            }}
          >
            Get Informed:
            <br />
            Health FAQs
          </Typography>
        </Box> */}
 <Box 
            sx={{ 
              textAlign: "center", 
              mb: { xs: 4, sm: 5, md: 3 },
              maxWidth: "800px",
              mx: "auto"
            }}
          >
            <Typography
              variant="h5"
              sx={{
                color: "primary.main",
                mb: 1,
                display: "block",
                fontSize: { xs: "1.125rem", md: "1.25rem" },
              }}
            >
               FAQ's
            </Typography>
            <Typography
              variant="h3"
              sx={{
                color: "#1a1a1a",
                mb: 2,
                fontSize: { xs: "1.875rem", sm: "2.25rem", md: "2.5rem" },
                lineHeight: 1.2,
              }}
            >
             Get Informed:
            <br />
            Health FAQs
            </Typography>
          </Box>
        <Grid container spacing={4} alignItems="flex-start" sx={{ height: "calc(100% - 150px)" }}>
          <Grid item xs={12} md={6} sx={{ height: { md: "100%" }, display:{xs:"none",md:"block"} }}>
            <ImageWrapper>
              <Image
                src="/faq.jpg"
                alt="FAQ Section Image"
                fill
                style={{ objectFit: "cover" }}
                priority
              />
            </ImageWrapper>
          </Grid>
          
          <Grid item xs={12} md={6} sx={{ height: { md: "100%" } }}>
            <Paper
              elevation={0}
              sx={{
                px: 4,
                py:3,
                backgroundColor: "rgb(255,255,255)",
                borderRadius: 2,
                boxShadow: "0 4px 20px rgba(0,0,0,0.05)",
                height: "80%",
                display: "flex",
                flexDirection: "column",
              }}
            >
              {/* <ScrollableContainer height={isMobile ? "325px" : "500px"}> */}
              {faqs.map((faq, index) => (
                <StyledAccordion
                  key={index}
                  expanded={expanded === `panel${index}`}
                  onChange={handleChange(`panel${index}`)}
                >
                  <StyledAccordionSummary
                    expandIcon={<ArrowDownward fontSize="small" />}
                    aria-controls={`panel${index}-content`}
                    id={`panel${index}-header`}
                  >
                    <Typography
                      sx={{
                        color:
                          expanded === `panel${index}`
                            ? "primary.main"
                            : "#1a1a1a",
                      }}
                    >
                      {faq.question}
                    </Typography>
                  </StyledAccordionSummary>
                  <StyledAccordionDetails>
                    <Typography
                      sx={{
                        color: "#666",
                        lineHeight: 1.3,
                      }}
                    >
                      {faq.answer}
                    </Typography>
                  </StyledAccordionDetails>
                </StyledAccordion>
              ))}
              {/* </ScrollableContainer> */}
            </Paper>
          </Grid>
        </Grid>
    </Box>
    </SectionLayoutSingleSpecialitySingleHospital>
    </Box>
  );
}