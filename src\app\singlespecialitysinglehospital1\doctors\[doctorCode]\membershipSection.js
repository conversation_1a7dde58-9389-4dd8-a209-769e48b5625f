import { Box, Typography } from "@mui/material";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";

const MembershipSection = ({memberships = []}) => {
  return (
    memberships.length > 0 && (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          fontFamily: "inherit",
          color: "inherit",
        }}
      >
        <Typography variant="h5">
          <Box
            sx={{
              display: "inline-block",
              "&::after": {
                content: "''",
                width: "50%",
                borderBottom: `3px solid`,
                borderColor: "primary.main",
                display: "block",
                marginBottom: "-1px",
                left: "50%",
                right: "50%",
              },
            }}
          >
            Memberships
          </Box>
        </Typography>
        {memberships.map((details, index) => {
          const { membershipName = "" } = details || {};
          return (
            <Box
              key={index}
              sx={{ display: "flex", alignItems: "start", gap: "8px" }}
            >
              <CheckCircleOutlineOutlinedIcon sx={{ color: "#373A40" }} />
              <Typography
                variant="subtitle1"
                sx={{
                  lineHeight: "1.25",
                  fontWeight: 300,
                  fontSize: "15px",
                }}
              >{`${membershipName || ""}`}</Typography>
            </Box>
          );
        })}
      </Box>
    )
  );
};

export default MembershipSection;
