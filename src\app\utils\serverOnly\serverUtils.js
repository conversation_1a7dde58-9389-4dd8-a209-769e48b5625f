import "server-only"
import {headers} from "next/headers";
export const getWebsiteHost = () => {
    if (process.env.DEVELOPMENT === 'true') {
        let host;
        if (process.env.WEBSITE_HOST === "healthcare-staging" ){
            host = "healthcare-staging.mydocsite.com" //oasis
        }else if(process.env.WEBSITE_HOST === "labs-staging"){
            host = "labs-staging.mydocsite.com" //Aspire
        } else {
            host = "hospital-chain-staging.mydocsite.com" //Aspire
        }
        return host
    } else {
        return headers().get("host")
    }
}

export const getHostForServerComponent = () => {
    if (process.env.DEVELOPMENT === 'true') {
        let host;
        if (process.env.WEBSITE_HOST === "healthcare-staging" ){
            host = "healthcare-staging.mydocsite.com" //oasis
        }else if(process.env.WEBSITE_HOST === "labs-staging"){
            host = "labs-staging.mydocsite.com" //Aspire
        } else {
            host = "hospital-chain-staging.mydocsite.com" //Aspire
        }
        return host
    } else {
        return headers().get("host")
    }
}