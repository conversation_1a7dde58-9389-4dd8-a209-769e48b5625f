"use client";
import React from "react";
import { Box, useMediaQuery } from "@mui/material";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
// import "slick-carousel/slick/slick.css";
// import "swiper/css";
// import "swiper/css/navigation";
// import {
//   NextArrow,
//   PrevArrow,
// } from "@/app/singlespecialitymultichain1/components/arrows";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
// import Slider from "react-slick";

const HomeCarouselWidget = ({ banners }) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const desktopBanners = banners.filter(
    (banner) => banner.isMobileCompatible === 0
  );
  const mobileBanners = banners.filter(
    (banner) => banner.isMobileCompatible === 1
  );
  // const mobileSwiperSettings = {
  //     spaceBetween: 20,
  //     slidesPerView: 1,
  //     navigation: false,
  //     arrows: false,
  //     loop: true,
  // };
  // const desktopSwiperSettings = {
  //     dots: false,
  //     infinite: true,
  //     adaptiveHeight: true,
  //     lazyLoad: false,
  //     slidesToShow: 1,
  //     slidesToScroll: 1,
  //     autoplay: true,
  //     speed: 500,
  //     autoplaySpeed: 3000,
  //     cssEase: "linear",
  //     pauseOnHover: true,
  //     arrows: false,
  //     nextArrow: <NextArrow />,
  //     prevArrow: <PrevArrow />,
  // };

  return (
    <Box key={isMobile ? "mobile" : "desktop"} sx={{ position: "relative" }}>
      {/* Show banners based on screen size */}
      {!isMobile ? (
        desktopBanners.length === 1 ? (
          <Swiper
            modules={[Autoplay, Pagination, Navigation]}
            spaceBetween={18}
            slidesPerView={1}
            loop={true}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            pagination={{ dynamicBullets: true, clickable: true }}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
            className="mySwiper"
          >
            {desktopBanners.map((banner, index) => {
              const { image_url: imgUrl = "" } = banner || {};
              return (
                <SwiperSlide key={index}>
                  <Box sx={{ position: "relative", width: "100%" }}>
                    <Image
                      alt="desktop-banner"
                      src={getThumborUrl(imgUrl)}
                      layout="responsive"
                      width={1920}
                      height={1080}
                      objectFit="cover"
                      priority
                    />
                  </Box>
                </SwiperSlide>
              );
            })}
          </Swiper>
        ) : (
          <Swiper
            modules={[Autoplay, Pagination, Navigation]}
            slidesPerView={1}
            spaceBetween={10}
            loop={true}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            pagination={{ dynamicBullets: true, clickable: true }}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
            // {...desktopSwiperSettings}
            // style={{ width: "100%", height: "100%" }}
          >
            {desktopBanners.map((banner, index) => {
              const { image_url: imgUrl = "" } = banner || {};
              return (
                <SwiperSlide key={index}>
                  <Box key={index} sx={{ position: "relative", width: "100%" }}>
                    <Image
                      alt="desktop-slider"
                      src={getThumborUrl(imgUrl)}
                      layout="responsive"
                      width={1920}
                      height={1080}
                      objectFit="cover"
                      priority
                    />
                  </Box>
                </SwiperSlide>
              );
            })}
          </Swiper>
        )
      ) : mobileBanners.length === 1 ? (
        <Swiper
          modules={[Autoplay, Pagination, Navigation]}
          slidesPerView={1}
          spaceBetween={10}
          loop={true}
          autoplay={{ delay: 3000, disableOnInteraction: false }}
          pagination={{ dynamicBullets: true, clickable: true }}
          navigation={{
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          }}
          className="mobileSwiper"
        >
          {mobileBanners.map((banner, index) => {
            const { image_url: imgUrl = "" } = banner || {};
            return (
              <SwiperSlide key={index}>
                <Box
                  sx={{
                    marginTop: "16px",
                    position: "relative",
                    width: "100%",
                    overflow: "hidden",
                    paddingX: "16px",
                  }}
                >
                  <Box
                    sx={{
                      width: "100%",
                      height: "100%",
                      borderRadius: "12px",
                      overflow: "hidden",
                    }}
                  >
                    <Image
                      alt="mobile-banner"
                      src={getThumborUrl(imgUrl)}
                      layout="intrinsic"
                      width={780}
                      height={1000}
                      objectFit="cover"
                      priority
                    />
                  </Box>
                </Box>
              </SwiperSlide>
            );
          })}
        </Swiper>
      ) : (
        <Swiper
          //   {...mobileSwiperSettings}
          modules={[Autoplay, Pagination, Navigation]}
          slidesPerView={1}
          spaceBetween={10}
          loop={true}
          autoplay={{ delay: 3000, disableOnInteraction: false }}
          pagination={{ dynamicBullets: true, clickable: true }}
          navigation={{
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          }}
          //   style={{ width: "100%", height: "100%" }}
        >
          {mobileBanners.map((banner, index) => {
            const { image_url: imgUrl = "" } = banner || {};
            return (
              <SwiperSlide key={index}>
                <Box
                  key={index}
                  sx={{
                    marginTop: "16px",
                    position: "relative",
                    width: "100%",
                    overflow: "hidden",
                    paddingX: "16px",
                  }}
                >
                  <Box
                    sx={{
                      width: "100%",
                      height: "100%",
                      borderRadius: "12px",
                      overflow: "hidden",
                    }}
                  >
                    <Image
                      alt="mobile-slider"
                      src={getThumborUrl(imgUrl)}
                      layout="intrinsic"
                      width={780} // Adjust image width to account for padding
                      height={1000} // Adjust height for portrait mode
                      objectFit="cover"
                      priority
                    />
                  </Box>
                </Box>
              </SwiperSlide>
            );
          })}
        </Swiper>
      )}
    </Box>
  );
};

export default React.memo(HomeCarouselWidget);
