"use client";

import { Box, Typography } from "@mui/material";
import SectionLayoutAspire from "../styledComponents/SectionLayoutChainTemp2";
import { useTheme } from "@emotion/react";
import useStyles from "../styles";
import OutlinedButton from "../styledComponents/OutlinedButton";
import { NextArrow, PrevArrow } from "./photosSection";
import Slider from "react-slick";
import BlogCard from "./blogCard";
import SectionHeading from "./sectionHeading";
import { useRouter } from "next/navigation";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination"
import React, {useContext} from "react";
import {AppContext} from "@/app/AppContextLayout";

const BlogsSection = () => {
    const {websiteData} = useContext(AppContext);
    const {
        blogs = [],
    } = websiteData || {};
  const classes = useStyles();
  const theme = useTheme();
  const router = useRouter();

  const handleViewAllBlogs = () => {
    router.push("/blogs");
  };

    if (blogs.length <= 0){
        return (
            <></>
        )
    }

  return (
    <SectionLayoutAspire>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "48px",
        }}
      >
        <SectionHeading align="right">Explore Our Blogs</SectionHeading>
        {blogs.length > 3 ? (
          <Box sx={{marginBottom: "16px"}}>
            {/*<Slider {...settings} style={{ height: "auto" }}>
              {blogs.map((blog) => {
                const { code = null } = blog || {};
                return (
                  <Box key={code} sx={{ padding: "16px 24px" }}>
                    <BlogCard blog={blog} />
                  </Box>
                );
              })}
            </Slider>*/}
            <Swiper
                spaceBetween={36}
                breakpoints={{
                  0: {
                    slidesPerView: 1,
                  },
                  600: {
                    slidesPerView: 2,
                  },
                  1200: {
                    slidesPerView: 4,
                  },
                }}
                modules={[ Autoplay, Pagination]}
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                }}
                pagination={{
                  clickable: true, // Enables clickable dots
                  dynamicBullets: true, // Optional: Makes dots dynamic based on slides
                }}
                className="mySwiper"
                style={{
                  "--swiper-navigation-color": "#333",
                  "--swiper-navigation-size": "24px",
                  "--swiper-pagination-color": "#333",
                }}
            >
              {blogs.map((blog) => {
                const { code = null } = blog || {};
                return (
                    <SwiperSlide key={code} style={{ paddingBottom: "30px" }}>
                      <BlogCard blog={blog} />
                    </SwiperSlide>
                );
              })}
            </Swiper>

          </Box>
        ) : (
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "1fr",
                sm: "repeat(3, 1fr)",
                md: "repeat(4, 1fr)",
              },
            }}
          >
            {blogs.slice(0, 6).map((blog, index) => {
              const { code = null } = blog || {};
              return (
                <Box key={code} id={`blog${index}`} sx={{ padding: "16px 24px" }}>
                  <BlogCard blog={blog} />
                </Box>
              );
            })}
          </Box>
        )}
        <Box sx={{ textAlign: "center" }}>
          <OutlinedButton
            id="viewAllBlogs"
            style={{ width: "fit-content" }}
            onClick={handleViewAllBlogs}
          >
            View all
          </OutlinedButton>
        </Box>
      </Box>
    </SectionLayoutAspire>
  );
};

export default BlogsSection;
