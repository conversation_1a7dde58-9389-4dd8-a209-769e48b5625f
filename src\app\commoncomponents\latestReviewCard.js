import { getInitials } from "@/app/utils/getInitials";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { Box, Rating, Typography } from "@mui/material";
import { format } from "date-fns";
import Image from "next/image";
import RatingBasedIcon from "./RatingBasedIcon";

const LatestReviewCard = ({ testimonial = {}, setViewReviewsDrawer }) => {
  const {
    title = "",
    rating = "",
    content = "",
    givenByEntity = "",
    postingDate = "",
    ratingType = 2,
    reviewSource = {},
  } = testimonial || {};
  const { icon = "" } = reviewSource || {};

  return (
    <Box
      id="review_card"
      onClick={() => setViewReviewsDrawer(true)}
      sx={{
        padding: "16px",
        border: `1px solid #ededed`,
        borderRadius: "8px",
        display: "flex",
        flexDirection: "column",
        gap: "24px",
        height: "200px",
        cursor: 'pointer'
      }}
    >
      <Box sx={{ display: "flex", gap: "16px" }}>
        <Box
          sx={{
            height: "48px",
            width: "48px",
            borderRadius: "100px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            fontSize: "1.5rem",
            bgcolor: "primary.main",
            color: "#ffffff",
          }}
        >
          {getInitials(givenByEntity || "Anonymous")}
        </Box>
        <Box sx={{ flex: 1 }}>
          <Box
            sx={{
              display: "flex",
              gap: "16px",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontSize: "16px",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: "1",
                WebkitBoxOrient: "vertical",
              }}
            >
              {givenByEntity || "Anonymous"}
            </Typography>
            {postingDate && (
              <Typography variant="body2" sx={{ fontSize: "12px", flex: 1 }}>
                {format(new Date(postingDate || ""), "MMM yyyy")}
              </Typography>
            )}
          </Box>
          <Box
            sx={{ display: "flex", gap: "8px", alignItems: "center", mt: 1 }}
          >
            {icon && (
              <Image
                src={getThumborUrl(icon || "")}
                alt="review icon"
                height={16}
                width={40}
                style={{ objectFit: "contain" }}
              />
            )}
            <RatingBasedIcon ratingType={ratingType} rating={rating} />
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          height: "100%",
        }}
      >
        <Typography
          variant="body1"
          sx={{
            fontSize: "14px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: "3",
            WebkitBoxOrient: "vertical",
          }}
        >
          {content || ""}
        </Typography>
        <Typography
          id="show_more"
          variant="subtitle2"
          align="right"
          sx={{ color: "primary.main", cursor: "pointer" }}
          onClick={() => setViewReviewsDrawer(true)}
        >
          show more
        </Typography>
      </Box>
    </Box>
  );
};

export default LatestReviewCard;
