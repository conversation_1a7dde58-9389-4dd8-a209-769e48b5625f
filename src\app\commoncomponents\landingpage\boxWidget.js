"use client";

import { useState, useEffect, useRef } from "react";
import { Box, Typography, Container, Modal, IconButton } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';

const BoxWidget = ({ value = [], title = "" }) => {
  const [openModal, setOpenModal] = useState(false);
  const [selectedDescription, setSelectedDescription] = useState("");
  const [selectedTitle, setSelectedTitle] = useState("");
  const [expandableItems, setExpandableItems] = useState([]);
  const descriptionRefs = useRef([]);

  // Function to check which descriptions exceed three lines
  const checkExpandableItems = () => {
    const newExpandableItems = [];
    descriptionRefs.current.forEach((ref, index) => {
      if (ref && ref.scrollHeight > ref.clientHeight) {
        newExpandableItems.push(index);
      }
    });
    setExpandableItems(newExpandableItems);
  };

  useEffect(() => {
    // Reset refs array when value changes
    descriptionRefs.current = descriptionRefs.current.slice(0, value.length);

    // Run after a short delay to ensure refs are populated
    const timer = setTimeout(checkExpandableItems, 100);

    // Add resize listener to recalculate on window resize
    window.addEventListener('resize', checkExpandableItems);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', checkExpandableItems);
    };
  }, [value]);

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedDescription("");
  };

  return (
    <Box
      sx={{
        py: { xs: 4, md: 4 },
        backgroundColor: 'primary.main',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <Box sx={{
        maxWidth: '1400px',
        mx: 'auto',
        px: { xs: 2, md: 4 },
        position: 'relative',
        zIndex: 1,
      }} >
        {title && (
          <Box sx={{ textAlign: 'center', mb: { xs: 5, md: 6 } }}>
            <Typography
              variant="h3"
              sx={{
                fontSize: { xs: "1.75rem", sm: "2.25rem", md: "2.5rem" },
                fontWeight: '400',
                color: '#fff',
                mb: 1,
              }}
            >
              {title}
            </Typography>
            <Box
              sx={{
                width: '80px',
                height: '3px',
                background: 'white',
                mx: 'auto',
                mb: 3,
                borderRadius: '3px',
              }}
            />
          </Box>
        )}

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)' },
            gap: { xs: 3, md: 4 },
          }}
        >
          {value.map((item, index) => {
            const { title = "", description = "", position = null } = item || {};

            return (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <Box
                  id={`boxWidget${index}Pos${position}`}
                  onClick={(e) => {
                    // Only open modal on box click if not clicking the Read More button
                    if (!e.target.closest('p[role="button"]') && expandableItems.includes(index)) {
                      setSelectedDescription(description || "");
                      setSelectedTitle(title || "");
                      setOpenModal(true);
                    }
                  }}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    cursor:"pointer",
                    backgroundColor: '#f8f8f8',
                    borderRadius: '10px',
                    padding: '24px',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
                    overflow: 'hidden',
                    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 16px 32px rgba(0, 0, 0, 0.16)',
                      '& .card-title': {
                        color: 'primary.main',
                        '&::after': {
                          width: '60px',
                        }
                      }
                    }
                  }}
                >
                  <Typography
                    className="card-title"
                    variant="h5"
                    sx={{
                      fontSize: { xs: '1.25rem', md: '1.4rem' },
                      fontWeight: '400',
                      color: '#333',
                      mb: 3,
                      transition: 'color 0.3s ease',
                      position: 'relative',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: -10,
                        left: 0,
                        width: '40px',
                        height: '3px',
                        backgroundColor: 'primary.main',
                        transition: 'width 0.3s ease',
                      }
                    }}
                  >
                    {title || ""}
                  </Typography>

                  <Box sx={{ position: 'relative' }}>
                    <Typography
                      ref={el => descriptionRefs.current[index] = el}
                      variant="body1"
                      sx={{
                        fontSize: { xs: '0.95rem', md: '1rem' },
                        color: 'rgba(0, 0, 0, 0.7)',
                        lineHeight: 1.6,
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {description || ""}
                    </Typography>
                    {expandableItems.includes(index) && (
                      <Typography
                        variant="body2"
                        role="button"
                        sx={{
                          color: 'primary.main',
                          fontWeight: 500,
                          mt: 1,
                          cursor: 'pointer',
                          display: 'inline-block',
                          '&:hover': {
                            textDecoration: 'underline'
                          }
                        }}
                        onClick={() => {
                          setSelectedDescription(description || "");
                          setSelectedTitle(title || "");
                          setOpenModal(true);
                        }}
                      >
                        Read More
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Box>

        {/* Modal for displaying full content */}
        <Modal open={openModal} onClose={handleCloseModal}>
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: { xs: '90%', sm: '60%', md: '40%' },
              bgcolor: 'background.paper',
              boxShadow: 24,
              p: 2.5,
              borderRadius: '10px',
              outline: 'none',
            }}
          >
            <IconButton
              onClick={handleCloseModal}
              sx={{
                position: 'absolute',
                top: 15,
                right: 8,
                color: 'rgba(0, 0, 0, 0.7)',
              }}
            >
              <CloseIcon />
            </IconButton>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: '500', color: 'primary.main' }}>
              {selectedTitle || ""}
            </Typography>
            <Box
              sx={{
                width: '100%',
                height: '1px',
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                mb: 2,
              }}
            />
            <Typography variant="body1" sx={{ color: 'rgba(0, 0, 0, 0.8)' }}>
              {selectedDescription}
            </Typography>
          </Box>
        </Modal>
      </Box>
    </Box>
  );
};

export default BoxWidget;
