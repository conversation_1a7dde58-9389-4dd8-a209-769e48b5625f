import { Box } from "@mui/material";
import Doctor<PERSON><PERSON> from "./doctorCard";

const DoctorsList = ({ doctors = [], selectedLocation = null }) => {
  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: {
          xs: "repeat(1, 1fr)",
          sm: "repeat(2, 1fr)",
          md: "repeat(3, 1fr)",
          lg: "repeat(4, 1fr)"
        },
        gap: { xs: "24px", md: "32px" },
        width: "100%",
        maxWidth: "1300px",
        margin: "0 auto"
      }}
    >
      {doctors.map((doctor, index) => {
        const { doctorDetails = {} } = doctor || {};
        const { code = null } = doctorDetails || {};
        return (
          <DoctorCard
            id={`doctor${index}`}
            key={code}
            doctorDetails={doctorDetails}
            selectedLocation={selectedLocation}
          />
        );
      })}
    </Box>
  );
};

export default DoctorsList;
