{"name": "harbor-hospital", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "start:dev": "env-cmd -f .env.dev next dev", "start:staging": "env-cmd -f .env.staging next start -p 3060", "start:prod": "env-cmd -f .env.prod next start", "build": "next build", "build:dev": "env-cmd -f .env.dev next build", "build:staging": "env-cmd -f .env.staging next build", "build:prod": "env-cmd -f .env.prod next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^43.0.0", "@ckeditor/ckeditor5-react": "^9.0.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@mui/material-nextjs": "^5.15.11", "@mui/styles": "^5.15.11", "@next/bundle-analyzer": "^14.2.15", "@next/third-parties": "^14.2.3", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.6.8", "axios-retry": "^4.5.0", "client-only": "^0.0.1", "date-fns": "^3.6.0", "env-cmd": "^10.1.0", "flowise-embed": "^1.3.7", "flowise-embed-react": "^3.0.0", "html-react-parser": "^5.1.9", "mui-one-time-password-input": "^2.0.2", "next": "14.1.2", "react": "^18", "react-dom": "^18", "react-redux": "^9.2.0", "react-slick": "^0.30.2", "server-only": "^0.0.1", "sharp": "^0.33.4", "slick-carousel": "^1.8.1", "swiper": "^11.1.14"}}