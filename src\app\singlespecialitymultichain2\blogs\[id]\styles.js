import { alpha } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useBlogPageStyles = makeStyles((theme) => ({
  blogHeadingBox: {
    width: "50%",
    position: "absolute",
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
    // display: "flex",
    flexDirection: "column",
    gap: "32px",
    display: "-webkit-box",
    boxOrient: "vertical",
    lineClamp: 3,
    wordBreak: "break-all",
    overflow: "hidden",
    [theme.breakpoints.down("md")]: {
      width: "75%",
    },
    [theme.breakpoints.down("sm")]: {
      width: "90%",
    },
    [theme.breakpoints.down("xs")]: {
      width: "100%",
      padding: "0 24px",
    },
  },
  blogHeadingTitle: {
    color: "white",
    fontWeight: "300",
    [theme.breakpoints.down("sm")]: {
      fontSize: "32px",
    },
    // [theme.breakpoints.down("xs")]: {
    //   width: "100%",
    //   padding: "0 24px",
    // },
  },
  blogHeadingSubtitle: {
    color: "white",
    marginTop: '32px',
    [theme.breakpoints.down("sm")]: {
        fontSize: "16px",
      },
  },
}));

export default useBlogPageStyles;
