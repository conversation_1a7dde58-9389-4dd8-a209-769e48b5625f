// "use client";
import Box from "@mui/material/Box";
import {
  Typography,
} from "@mui/material";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_REVIEWS,
} from "@/constants";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
import {getEnterpriseCode} from "@/app/oasis/blogs/[blogCode]/layout";
import ReviewCard from "@/app/aspire/components/reviewCard";

const getReviews = async (enterpriseCode) => {
  if (!enterpriseCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}?list=true`;
  try {
    const response = await fetch(url, {
      cache: "no-store"
    });
    const jsonResult = await response.json();
    const { result: reviews = {} } = jsonResult || {};
    return reviews;
  } catch (error) {
    console.log("getReviewsError", error);
  }
};

export default async function Reviews() {
  const enterpriseCode = await getEnterpriseCode();
  const reviews = await getReviews(enterpriseCode);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayout>
        <Typography variant="h4">Patient Reviews</Typography>
        <Box
          sx={{ display: "flex", flexDirection: "column", gap: "48px", mt: 2 }}
        >
          {/* <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <FormControl>
              <Select
                displayEmpty
                input={
                  <InputBase
                    sx={{
                      border: "1px solid #d3d4db",
                      padding: "8px 12px",
                      borderRadius: "6px",
                    }}
                  />
                }
                value="0"
                inputProps={{ "aria-label": "Without label" }}
              >
                <MenuItem disabled value="0">
                  Filter by speciality
                </MenuItem>
                <MenuItem value={"Cardiology"}>Cardiology</MenuItem>
              </Select>
            </FormControl>
          </Box> */}
          <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  lg: "repeat(3, 1fr)",
                },
                columnGap: "32px",
                rowGap: "64px",
              }}
            >
              {reviews.map((testimonial, index) => {
                return <ReviewCard key={index} testimonial={testimonial} />;
              })}
            </Box>
            {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Pagination count={10} variant="outlined" shape="rounded" />
            </Box> */}
          </Box>
        </Box>
      </SectionLayout>
      {/* <FaqsSection /> */}
    </Box>
  );
}
