"use client";

import React, { useState } from "react";
import { Box, Typography, IconButton } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import "swiper/css";
import "swiper/css/navigation";
import { useRouter } from "next/navigation";
import { Navigation } from "swiper/modules";

const HospitalCard = ({ image, name, areaName, locationCode }) => {
  const router = useRouter();
  const defaultImage = "https://cdn.docfyn.com/com.harbor/documents/2025/03/25/facd4f0546b45b33ac6f05b110f2e3c75c0a7043/photo-1597807037496-c56a1d8bc29a.webp";

  const handleCardClick = () => {
    if (locationCode) {
      router.push(`/locations/${locationCode}`);
    } else {
      console.error("Missing locationCode for hospital:", name);
    }
  };

  return (
    <Box
      sx={{
        bgcolor: "white",
        width: "100%",
        borderRadius: "8px",
        overflow: "hidden",
        boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
        height: "100%",
        display: "flex",
        marginBottom: "10px",
        flexDirection: "column",
        cursor: "pointer",
        transition: "transform 0.2s",
        "&:hover": {
          transform: "scale(1.02)",
        },
      }}
      onClick={handleCardClick}
    >
      <Box sx={{ position: "relative", width: "100%", height: "300px" }}>
        <Image
          src={image || defaultImage}
          alt={name}
          fill
          style={{ objectFit: "cover" }}
        />
      </Box>
      <Box sx={{ p: 2 }}>
        <Typography
          variant="h6"
          sx={{ 
            fontWeight: 500, 
            fontSize: "16px",
            color: "text.black",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          }}
        >
          {name}, {areaName}
        </Typography>
      </Box>
    </Box>
  );
};

const CentresSlider = ({ hospitals = [] }) => {
  const [swiper, setSwiper] = useState(null);

  const handlePrev = () => {
    if (swiper) {
      swiper.slidePrev();
    }
  };

  const handleNext = () => {
    if (swiper) {
      swiper.slideNext();
    }
  };

  return (
    <Box sx={{ position: "relative", px: 2, py: 3 }}>
      <Box
        sx={{
          position: "absolute",
          top: {xs: "10px", md: "-10px"},
          right: "10px",
          display: "flex",
          gap: "8px",
          zIndex: 10,
        }}
      >
        <IconButton
          onClick={handlePrev}
          sx={{
            width: 40,
            height: 40,
            bgcolor: "rgba(255, 255, 255, 0.9)",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            "&:hover": {
              bgcolor: "white",
            },
            "&:disabled": {
              opacity: 0.5,
              cursor: "not-allowed",
            },
          }}
        >
          <Box
            component="span"
            sx={{
              width: 24,
              height: 24,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&::before": {
                content: '"❮"',
                fontSize: "20px",
                color: "#333",
              },
            }}
          />
        </IconButton>
        <IconButton
          onClick={handleNext}
          sx={{
            width: 40,
            height: 40,
            bgcolor: "rgba(255, 255, 255, 0.9)",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            "&:hover": {
              bgcolor: "white",
            },
            "&:disabled": {
              opacity: 0.5,
              cursor: "not-allowed",
            },
          }}
        >
          <Box
            component="span"
            sx={{
              width: 24,
              height: 24,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&::before": {
                content: '"❯"',
                fontSize: "20px",
                color: "#333",
              },
            }}
          />
        </IconButton>
      </Box>

      <Swiper
      style={{padding: "10px 10px"}}
        modules={[Navigation]}
        spaceBetween={20}
        slidesPerView={4}
        loop={true}
        onSwiper={setSwiper}
        breakpoints={{
          290: {
            slidesPerView: 1,
            spaceBetween: 12,
          },
          325: {
            slidesPerView: 1,
            spaceBetween: 12,
          },
          400: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          640: {
            slidesPerView: 3,
            spaceBetween: 15,
          },
          768: {
            slidesPerView: 3,
            spaceBetween: 20,
          },
          1024: {
            slidesPerView: 2.5,
            spaceBetween: 20,
          },
          1280: {
            slidesPerView: 4,
            spaceBetween: 20,
          },
        }}
      >
        {hospitals.map((hospital) => (
          <SwiperSlide key={hospital.id}>
            <HospitalCard
              image={hospital?.multiMedia?.[0]?.imageURL}
              name={hospital?.name}
              areaName={hospital?.area?.name}
              locationCode={hospital?.domain_slug}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </Box>
  );
};

export default CentresSlider;