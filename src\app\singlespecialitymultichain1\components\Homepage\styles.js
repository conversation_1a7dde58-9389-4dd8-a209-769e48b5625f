import { alpha } from "@mui/material";
import { makeStyles } from "@mui/styles";
import {
  THEME_WEBSITE_BG_COLOR,
  THEME_WEBSITE_SECONDARY_BG_COLOR,
} from "../../constantsSingleSplMultiChain1";

const homepageStyles = makeStyles((theme) => ({
  homepageSectionHeading: {
    fontSize: "0.8rem",
    textTransform: "uppercase",
    letterSpacing: "1px",
    fontWeight: "500",
    color: theme.palette.primary.main,
  },
  homepageSectionSubHeading: {
    fontSize: "2.5rem",
    color: "#333333",
    [theme.breakpoints.down("sm")]: {
      fontSize: "2rem",
    },
  },
  servicesSectionGridBox: {
    display: "grid",
    gridTemplateColumns: "repeat(2, 1fr)",
    gap: "64px",
    flex: 1,
    alignContent: "start",
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "repeat(1, 1fr)",
    },
  },
  servicesSectionBox: {
    width: "30%",
    [theme.breakpoints.down("sm")]: {
      width: "100%",
    },
  },
  servicesSection: {
    display: "flex",
    gap: "4rem",
    alignItems: "center",
    [theme.breakpoints.down("sm")]: {
      flexDirection: "column",
    },
  },
  servicesGridItem: {
    padding: "1rem",
    backgroundColor: "#FFFFFF",
    display: "flex",
    gap: "16px",
    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.05)}`,
    borderRadius: "16px",
    flex: 1,
  },
  servicesGridOneItem: {
    padding: "1rem .5rem",
    [theme.breakpoints.down("sm")]: {
      padding: "1rem",
    },
  },
  servicesGridItemHeading: {
    fontSize: "1.2rem",
    fontWeight: "500",
    lineHeight: "150%",
    marginBottom: "4px",
  },
  servicesGridItemSubHeading: {
    fontSize: "14px",
    color: "#828282",
    display: "-webkit-box",
    boxOrient: "vertical",
    lineClamp: 3,
    wordBreak: "break-word",
    overflow: "hidden",
  },
  servicesGridItemImageBox: {
    height: "80px",
    width: "80px",
    borderRadius: "4px",
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  gallerySection: {
    backgroundColor: alpha(theme.palette.primary.main, 0.03),
    display: "flex",
    flexDirection: "column",
    gap: "48px",
    position: "relative",
    overflow: "hidden",
  },
  gallerySectionImagesFlexBox: {
    display: "grid",
    gap: "64px",
    justifyContent: "normal",
    gridTemplateColumns: "repeat(3, 1fr)",
    [theme.breakpoints.down("md")]: {
      gridTemplateColumns: "repeat(2, 1fr)",
    },
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "repeat(1, 1fr)",
    },
  },
  gallerySectionOneImageFlexBox: {
    justifyContent: "center",
    gridTemplateColumns: "repeat(1, .5fr)",
  },
  gallerySectionImgBox: {
    borderRadius: "8px",
    overflow: "hidden",
    // flex: 1,
    height: "400px",
    objectFit: "cover",
    objectPosition: "center",
  },
  gallerySectionImg: {
    height: "100%",
    width: "100%",
    objectFit: "cover",
  },
  gallerySelectionSvg: {
    "& path": {
      fill: "#D7D6ED",
    },
  },
  sectionHeading: {
    // background: `-webkit-linear-gradient(45deg, ${alpha(
    //   theme.palette.primary.main,
    //   0.1
    // )} 30%, ${theme.palette.primary.main} 90%)`,
    // WebkitBackgroundClip: "text",
    // WebkitTextFillColor: "transparent",
  },
  doctorsSection: {
    display: "flex",
    gap: "4rem",
    alignItems: "center",
    [theme.breakpoints.down("sm")]: {
      flexDirection: "column-reverse",
    },
  },
  doctorsSectionDoctorsBox: {
    display: "grid",
    gridTemplateColumns: "repeat(2, 1fr)",
    columnGap: "8rem",
    rowGap: "4rem",
    alignContent: "start",
    flex: 1,
    justifyContent: "normal",
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "repeat(1, 1fr)",
    },
  },
  doctorsSectionOneDoctorBox: {
    gridTemplateColumns: ".5fr",
    justifyContent: "center",
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "repeat(1, 1fr)",
    },
  },
  doctorsSectionGridItem: {
    padding: "1.5rem",
    backgroundColor: "#FFFFFF",
    display: "flex",
    gap: "16px",
    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.05)}`,
    borderRadius: "16px",
    cursor: "pointer",
    flex: 1,
    [theme.breakpoints.down("sm")]: {
      padding: "1rem",
    },
  },
  doctorsSectionGridOneItem: {
    padding: "3rem",
    [theme.breakpoints.down("sm")]: {
      padding: "1rem",
    },
  },
  doctorSectionHeadingBox: {
    width: "30%",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    [theme.breakpoints.down("sm")]: {
      width: "100%",
    },
  },
  doctorsSectionDoctorImgBox: {
    height: "90px",
    width: "90px",
    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
    backgroundColor: "#F1F0FF",
    borderRadius: "100px",
    position: "absolute",
    left: "-45px",
    top: "50%",
    transform: "translateY(-50%)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
    [theme.breakpoints.down("sm")]: {
      position: "static",
      left: "0",
      transform: "none",
    },
  },
  doctorsSectionGridItemContent: {
    display: "flex",
    flexDirection: "column",
    rowGap: "14px",
    flex: 1,
    marginLeft: "53px",
    [theme.breakpoints.down("sm")]: {
      marginLeft: 0,
    },
  },
  blogsSection: {
    backgroundColor: alpha(theme.palette.primary.main, 0.03),
  },
  blogsSectionCarousal: {
    display: "grid",
    gridTemplateColumns: "repeat(3, 1fr)",
    gap: "4rem",
    width: "100%",
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "repeat(2, 1fr)",
      gap: "2rem",
    },
  },
  blogsSectionItem: {
    height: "500px",
    backgroundPosition: "center",
    backgroundSize: "cover",
    backgroundRepeat: "no-repeat",
    flex: 1,
    position: "relative",
    borderRadius: "1rem",
    overflow: "hidden",
    [theme.breakpoints.down("sm")]: {
      height: "200px",
    },
    "&:hover": {
      "& $blogSectionItemContentBox": {
        width: "100%",
        top: "50%",
        transform: "translateY(-50%)",
        "& $blogSectionItemContent": {
          display: "-webkit-box",
          boxOrient: "vertical",
          lineClamp: 3,
          wordBreak: "break-all",
          overflow: "hidden",
        },
        "& $blogSectionItemReadMore": {
          display: "block",
          visibility: "visible",
        },
      },
      "& $blogsSectionItemGradient": {
        background: `linear-gradient(180.05deg,${alpha(
          theme.palette.primary.main,
          0.05
        )} .04%,#2d1100 124.36%)`,
      },
    },
  },
  blogsSectionCarousalItem: {
    fontSize: "1.4rem",
    wordBreak: "break-word",
    fontWeight: "500",
    display: "-webkit-box",
    boxOrient: "vertical",
    lineClamp: 2,
    overflow: "hidden",
    [theme.breakpoints.down("sm")]: {
      fontSize: "1rem",
    },
  },
  blogSectionItemContentBox: {
    color: THEME_WEBSITE_BG_COLOR,
    position: "absolute",
    // bottom: "1rem",
    // top: "50%",
    padding: "2rem",
    // transform: "translateY(-50%)",
    top: "calc(100% - 8rem)",
    display: "flex",
    flexDirection: "column",
    gap: "2rem",
    transition: "all 0.3s ease-in",
    width: "100%",
  },
  blogsSectionItemGradient: {
    position: "absolute",
    height: "100%",
    width: "100%",
    background:
      "linear-gradient(180.05deg,rgba(42, 40, 97, .1) .04%,#2d1100 124.36%)",
  },
  blogSectionItemDarkGradient: {
    background: `linear-gradient(180.05deg,${alpha(theme.palette.primary.main, .05)} .04%,#2d1100 124.36%)`,
  },
  blogSectionItemReadMore: {
    display: "none",
    color: `${theme.palette.text.white} !important`,
    border: `1px solid ${theme.palette.text.white} !important`,
    width: "160px",
    visibility: "hidden",
    transition: "all .3s ease-in",
    "&:hover": {
      backgroundColor: `${theme.palette.text.white} !important`,
      color: `${theme.palette.text.primary} !important`,
    },
  },
  blogSectionItemContent: {
    display: "none",
    transition: "all .3s ease-in",
  },
  reviewsSectionHeadingBox: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    width: "50%",
    [theme.breakpoints.down("sm")]: {
      flex: 1,
    },
  },
  reviewsSectionButton: {
    alignSelf: "start",
    transition: "all .3s",
    "&:hover": {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.text.white,
    },
  },
  reviewsSectionContentBox: {
    padding: "8px",
    paddingBottom: "24px",
    border: `1px solid #ededed`,
    backgroundColor: alpha(theme.palette.primary.main, .03),
    borderRadius: "8px",
    position: "relative",
    "&:before": {
      content: "''",
      position: "absolute",
      height: "30px",
      width: "30px",
      border: `2px solid #ededed`,
      borderLeftColor: "transparent",
      borderTopColor: "transparent",
      boxSizing: "border-box",
      bottom: "-15px",
      left: "40px",
      transform: "rotate(45deg)",
      zIndex: 0,
      backgroundColor: THEME_WEBSITE_SECONDARY_BG_COLOR,
    },
  },
  reviewsSectionReviewBox: {
    padding: "8px",
    minHeight: "10rem",
    maxHeight: "10rem",
    overflowY: "auto",
    // backgroundColor: THEME_WEBSITE_TERNARY_BG_COLOR,
  },
  reviewsSectionReviewBoxText: {
    color: "#333333",
    fontSize: "14px",
  },
  reviewsSectionReviewTime: {
    color: theme.palette.text.secondary,
    fontSize: "12px",
  },
  reviewsSectionRating: {
    verticalAlign: "middle",
    fontSize: "1.4rem",
    marginRight: "16px",
    [theme.breakpoints.down("sm")]: {
      fontSize: "1rem",
    },
  },
  reviewsSectionRatingIconFilled: {
    color: theme.palette.primary.main
  },
  reviewsSectionProfileBox: {
    display: "flex",
    gap: "16px",
    padding: "0 8px",
    alignItems: "center",
  },
  reviewsSectionProfileImg: {
    height: "48px",
    width: "48px",
    borderRadius: "100px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    fontSize: "1.5rem",
    backgroundColor: "#1097A7",
    color: "#ffffff",
    [theme.breakpoints.down("sm")]: {
      height: "32px",
      width: "32px",
      fontSize: "1rem",
    },
  },
  reviewsSectionProfileDetailsBox: {
    display: "flex",
    flexDirection: "column",
    gap: "4px",
  },
  reviewsSectionProfileName: {
    fontSize: "1.2rem",
    [theme.breakpoints.down("sm")]: {
      fontSize: "1rem",
    },
  },
  reviewsSectionCarousalBox: {
    display: "grid",
    gridTemplateColumns: "repeat(3, 1fr)",
    columnGap: "4rem",
    height: "fit-content",
    padding: "1rem 0",
    [theme.breakpoints.down("sm")]: {
      columnGap: "2rem",
    },
  },
  reviewsSectionCarousalBoxItem: {
    display: "flex",
    flexDirection: "column",
    gap: "2rem",
    transition: "all .3s",
    "&:hover": {
      transform: "translate(10px, -10px)",
    },
  },
  reviewsSectionContentBoxComma: {
    // content: "\201C",
    // fontSize: '4rem',
    alignSelf: "start",
  },
  faqSectionExpandIcon: {
    transition: "all 0.3s",
  },
  faqSectionExpandIconActive: {
    transform: "rotate(180deg)",
  },
  blogsSectionWaves: {
    "& path": {
      fill: alpha(theme.palette.primary.main, .05),
    },
  },
  faqsSecionFlexBox: {
    display: "flex",
    gap: "2rem",
    alignItems: "center",
    justifyContent: "space-between",
    [theme.breakpoints.down("sm")]: {
      flexDirection: "column-reverse",
    },
  },
  faqSectionFaq: {
    flex: 1,
    display: "grid",
    gridTemplateColumns: "repeat(2, 1fr)",
    gap: "1rem",
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "repeat(1, 1fr)",
      width: "100%",
    },
  },
  faqsSectionHeadingBox: {
    width: "33%",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    [theme.breakpoints.down("sm")]: {
      width: "100%",
    },
  },
  faqSectionGridItem: {
    backgroundColor: "#fff",
    padding: "16px",
    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, .05)}`,
    borderRadius: "8px",
    alignSelf: "start",
  },
  servicesSectionImg: {
    backgroundColor: theme.palette.primary.main,
    mask: "url(/services.svg) no-repeat center / contain",
    // width: "100%",
    height: "200px",
    marginTop: "32px",
  },
}));

export default homepageStyles;
