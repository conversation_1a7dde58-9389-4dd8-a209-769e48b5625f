"use client";
import { useState, useCallback, useEffect, Suspense, lazy } from "react";
import { searchLabs, getLabTests, getLabPackages } from "@/api/harbor.service";
import {
  Box,
  Container,
  Typography,
  InputBase,
  Button,
  useMediaQ<PERSON>y,
  useTheme,
  IconButton,
  Badge,
  CircularProgress
} from "@mui/material";
import { useSelector, useDispatch } from 'react-redux';
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import PhoneIcon from "@mui/icons-material/Phone";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import { selectCartTotalQuantity } from '../redux/slices/cartSlice';
import { setCartOpen } from '../redux/slices/uiSlice';
import { useRouter } from "next/navigation";
import debounce from "lodash/debounce";
import Link from "next/link";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import SearchPlaceholder from "./SearchPlaceholder";

// Dynamically import components that aren't needed on initial page load
const SearchResults = lazy(() => import("./search"));
const NavbarCart = lazy(() => import("./NavbarCart"));

// Search results will come from API

const TopNavbar = ({ websiteData }) => {
  const {
    logo_url: logoUrl = null,
    phoneNumbers = [],
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};
  const primaryPhone = phoneNumbers?.[0]?.phone || "";
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isSearchAnimating, setIsSearchAnimating] = useState(false);
  const [popularTests, setPopularTests] = useState([]);
  const [popularPackages, setPopularPackages] = useState([]);
  const [isLoadingPopular, setIsLoadingPopular] = useState(false);
  const [isLoadingSearch, setIsLoadingSearch] = useState(false);

  // Get cart quantity from Redux store
  const cartQuantity = useSelector(selectCartTotalQuantity);

  // Format cart count for display (10+ if more than 10)
  const formatCartCount = (count) => {
    return count > 10 ? "10+" : count.toString();
  };

  useEffect(() => {
    if (isSearchOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isSearchOpen]);

  // Function to perform the actual search
  const performSearch = useCallback(async (query) => {
    if (!query || query.length < 3 || !enterpriseCode) {
      setSearchResults([]);
      setIsSearching(false);
      setIsLoadingSearch(false);
      return;
    }

    setIsSearching(true);
    setIsLoadingSearch(true);

    try {
      // Use the API function from harbor.service
      const searchData = await searchLabs(enterpriseCode, query);
      const searchItems = searchData?.data || [];

      // Map the API response to the format expected by the SearchResults component
      const results = searchItems.map(item => ({
        id: item.code,
        title: item.name,
        iconUrl: item.iconUrl, // Pass the actual icon URL
        originalPrice: item.originalPrice,
        discountedPrice: item.discountedPrice,
        discount: `${item.discountPercentage}% off`,
        description: item.shortDescription || item.alternativeNames || '',
        seoSlug: item.seoSlug,
        isPackage: item.type === 'package',
        // Include testsIncluded if totalTestsIncluded is 1 or more
        ...(item.totalTestsIncluded >= 1 && {
          testsIncluded: `${item.totalTestsIncluded} ${item.totalTestsIncluded === 1 ? 'Test' : 'Tests'} included`
        })
      }));

      // Always update the results
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
      setIsLoadingSearch(false);
    }
  }, [enterpriseCode]); // Remove searchQuery from dependencies to prevent infinite loops

  // Create a debounced search function using lodash
  const debouncedSearch = useCallback(
    debounce((query) => {
      if (!query || query.length < 3) return;
      performSearch(query);
    }, 300),
    [performSearch]
  );

  // Simplified search handler
  const handleSearch = useCallback((query) => {
    // If query is empty or too short, clear results immediately
    if (!query || query.length < 3) {
      setSearchResults([]);
      setIsSearching(false);
      setIsLoadingSearch(false);
      return;
    }

    // Set loading state immediately
    setIsLoadingSearch(true);
    setIsSearching(true);

    // Call the debounced search function
    debouncedSearch(query);
  }, [debouncedSearch]);

  // Fetch popular tests and packages - optimized with useMemo
  const fetchPopularItems = useCallback(async () => {
    // Only fetch if we don't already have the data and we have an enterprise code
    if (popularTests.length > 0 || popularPackages.length > 0 || !enterpriseCode) return;

    setIsLoadingPopular(true);
    try {
      // Use the API functions from harbor.service
      const [packagesData, testsData] = await Promise.all([
        getLabPackages(enterpriseCode),
        getLabTests(enterpriseCode)
      ]);

      const packages = packagesData?.data || [];
      const tests = testsData?.data || [];

      // Format packages data
      const formattedPackages = packages.map(item => ({
        id: item.code,
        title: item.name,
        iconUrl: item.iconUrl,
        originalPrice: item.originalPrice,
        discountedPrice: item.discountedPrice,
        discount: `${item.discountPercentage}% off`,
        description: item.shortDescription || item.alternativeNames || '',
        seoSlug: item.seoSlug,
        isPackage: true,
        ...(item.totalTestsIncluded >= 1 && {
          testsIncluded: `${item.totalTestsIncluded} ${item.totalTestsIncluded === 1 ? 'Test' : 'Tests'} included`
        })
      }));

      // Format tests data
      const formattedTests = tests.map(item => ({
        id: item.code,
        title: item.name,
        iconUrl: item.iconUrl,
        originalPrice: item.originalPrice,
        discountedPrice: item.discountedPrice,
        discount: `${item.discountPercentage}% off`,
        description: item.shortDescription || item.alternativeNames || '',
        seoSlug: item.seoSlug,
        isPackage: false,
        // Include testsIncluded if totalTestsIncluded is 1 or more
        ...(item.totalTestsIncluded >= 1 && {
          testsIncluded: `${item.totalTestsIncluded} ${item.totalTestsIncluded === 1 ? 'Test' : 'Tests'} included`
        })
      }));

      setPopularPackages(formattedPackages);
      setPopularTests(formattedTests);
    } catch (error) {
      console.error('Error fetching popular items:', error);
    } finally {
      setIsLoadingPopular(false);
    }
  }, [enterpriseCode, popularTests.length, popularPackages.length]);

  const handleSearchClick = () => {
    setIsSearchAnimating(true);
    setIsSearchOpen(true);

    // Only fetch popular items when search is opened and we don't have them yet
    if (popularTests.length === 0 && popularPackages.length === 0) {
      // Use setTimeout to delay the fetch until after the search modal is open
      // This prevents the UI from freezing during the animation
      setTimeout(() => {
        fetchPopularItems();
      }, 300); // Delay by 300ms to allow the animation to complete
    }
  };

  const handleCloseSearch = () => {
    setIsSearchAnimating(false);
    setTimeout(() => {
      setIsSearchOpen(false);
      setSearchQuery("");
      setSearchResults([]);
    }, 300);
  };

  const handleInputChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    handleSearch(query);
  };

  const handleClearSearch = () => {
    setSearchQuery("");
    setSearchResults([]);
    setIsSearching(false);
    setIsLoadingSearch(false);
    // Cancel any pending debounced searches
    debouncedSearch.cancel();
  };

  const dispatch = useDispatch();

  const handleCartToggle = () => {
    const newCartState = !isCartOpen;
    setIsCartOpen(newCartState);
    dispatch(setCartOpen(newCartState));
  };

  const handleCartClose = () => {
    setIsCartOpen(false);
    dispatch(setCartOpen(false));
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          borderBottom: "1px solid #e0e0e0",
          position: "sticky",
          top: 0,
          zIndex: 1000,
          width: "100%",
        }}
      >
        <Container
          maxWidth="xl"
          sx={{
            position: "relative",
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "space-between", md: "flex-start" },
            gap: { xs: 1, sm: 2, md: 4 },
            py: { xs: 1, md: 0 },
            px: { xs: 2, sm: 3, md: 4 },
            flexWrap: { xs: "wrap", md: "nowrap" },
          }}
        >
          {/* Logo */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mr: { xs: 0, md: 2 },
            }}
          >
            {logoUrl ? (
              <Link href="/">
                <Image
                  alt="logo"
                  src={getThumborUrl(logoUrl)}
                  width={isSmallMobile ? 160 : 210}
                  height={isSmallMobile ? 55 : 70}
                  style={{ cursor: "pointer" }}
                />
              </Link>
            ) : (
              <Link href="/">
                <Typography
                  variant={isSmallMobile ? "h6" : "h5"}
                  component="div"
                  sx={{
                    fontWeight: "bold",
                    color: theme.palette.primary.main,
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  LOGO
                  <span
                    style={{
                      fontSize: "0.8em",
                      marginLeft: "4px",
                      color: "#ff6b00",
                    }}
                  >
                    24/7
                  </span>
                </Typography>
              </Link>
            )}
          </Box>

          {/* Phone Number - display on all devices */}
          <Box
            sx={{
              display: { xs: "flex", md: "flex" },
              alignItems: "center",
              gap: 1,
              minWidth: { xs: "auto", md: 200 },
              order: { xs: 3, md: 2 },
              width: { xs: "auto", md: "auto" },
              mt: { xs: 0, md: 0 },
              justifyContent: { xs: "flex-start", md: "flex-start" },
            }}
          >
            {isMobile && (
              <IconButton
                onClick={handleCartToggle}
                sx={{
                  transition: "color 0.2s ease",
                  p: { xs: 0.5, md: 1 },
                  "&:hover": { color: theme.palette.primary.main },
                }}
              >
                <Badge
                  badgeContent={cartQuantity > 0 ? formatCartCount(cartQuantity) : 0}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      fontSize: '0.7rem',
                      fontWeight: 'bold',
                      minWidth: '18px',
                      height: '18px',
                      padding: '0 4px',
                    }
                  }}
                >
                  <ShoppingCartIcon />
                </Badge>
              </IconButton>
            )}

            {!isMobile ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PhoneIcon color="primary" sx={{ fontSize: 24, mr: 1 }} />
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Call us on
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 500,
                      color: "text.black",
                      cursor: "pointer",
                      '&:hover': {
                        color: "primary.main"
                      }
                    }}
                    onClick={() => window.location.href = `tel:${primaryPhone}`}
                  >
                    {primaryPhone}
                  </Typography>
                </Box>
              </Box>
            ) : (
              <IconButton
                onClick={() => window.location.href = `tel:${primaryPhone}`}
                sx={{
                  p: 0.5,
                  color: "primary.main"
                }}
              >
                <PhoneIcon sx={{ fontSize: 22 }} />
              </IconButton>
            )}
          </Box>

          {/* Search Bar - desktop only */}
          {!isMobile && (
            <Box
              onClick={handleSearchClick}
              sx={{
                flex: { xs: "1 1 auto", md: 1 },
                display: { xs: "none", md: "flex" },
                alignItems: "center",
                border: "1px solid #e0e0e0",
                borderRadius: "8px",
                padding: { xs: "6px 12px", md: "8px 16px" },
                cursor: "pointer",
                transition: "all 0.2s ease",
                order: { xs: 2, md: 3 },
                mr: { xs: 1, md: 0 },
                "&:hover": {
                  borderColor: theme.palette.primary.main,
                  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
                },
              }}
            >
              <SearchIcon sx={{ color: "text.secondary", mr: 1 }} />
              <SearchPlaceholder />
            </Box>
          )}

          {/* Right Section */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: { xs: 1, sm: 2, md: 3 },
              order: { xs: 1, md: 4 },
              ml: { xs: 0, md: "auto" },
            }}
          >
            {!isMobile && (
              <IconButton
                onClick={handleCartToggle}
                sx={{
                  transition: "color 0.2s ease",
                  p: { xs: 0.5, md: 1 },
                  "&:hover": { color: theme.palette.primary.main },
                }}
              >
                <Badge
                  badgeContent={cartQuantity > 0 ? formatCartCount(cartQuantity) : 0}
                  color="primary"
                  sx={{
                    '& .MuiBadge-badge': {
                      fontSize: '0.7rem',
                      fontWeight: 'bold',
                      minWidth: '18px',
                      height: '18px',
                      padding: '0 4px',
                    }
                  }}
                >
                  <ShoppingCartIcon />
                </Badge>
              </IconButton>
            )}

            {!isMobile && (
              <Button
                color="primary"
                variant="contained"
                onClick={() => router.push("/tests")}
                sx={{
                  borderRadius: "8px",
                  textTransform: "none",
                  px: { xs: 2, md: 3 },
                  py: { xs: 0.5, md: 1 },
                  fontSize: { xs: "0.8rem", md: "1rem" },
                  transition: "all 0.2s ease",
                  whiteSpace: "nowrap",
                  "&:hover": {
                    transform: "translateY(-2px)",
                    boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                  },
                }}
              >
                <Typography sx={{ fontWeight: 500, color: theme.palette.text.primary }}>
                Book Test
                </Typography>
              </Button>
            )}
          </Box>
        </Container>
      </Box>

      {/* Full Screen Search */}
      {isSearchOpen && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(255,255,255,0.98)",
            backdropFilter: "blur(10px)",
            zIndex: 1100,
            display: "flex",
            flexDirection: "column",
            opacity: isSearchAnimating ? 1 : 0,
            transform: isSearchAnimating
              ? "translateY(0)"
              : "translateY(-20px)",
            transition: "all 0.3s ease",
          }}
        >
          {/* ... existing code ... <search modal contents> */}
          <Container maxWidth="lg" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ py: { xs: 2, md: 4 }, flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
              {/* Search Header */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  mb: { xs: 2, md: 4 },
                  position: "relative",
                }}
              >
                <Box
                  sx={{
                    flex: 1,
                    maxWidth: "600px",
                    margin: "0 auto",
                    position: "relative",
                    backgroundColor: "white",
                    borderRadius: "12px",
                    boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
                    padding: "4px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <SearchIcon
                    sx={{
                      color: "primary.main",
                      fontSize: { xs: 24, md: 28 },
                      ml: { xs: 1, md: 2 },
                    }}
                  />
                  <InputBase
                    autoFocus
                    placeholder="Search for lab tests"
                    value={searchQuery}
                    onChange={handleInputChange}
                    sx={{
                      flex: 1,
                      color: "text.black",
                      mx: 2,
                      fontSize: { xs: "1rem", md: "1.1rem" },
                      "& input": {
                        padding: { xs: "8px 0", md: "12px 0" },
                      },
                    }}
                  />
                  {searchQuery && (
                    <CloseIcon
                      onClick={handleClearSearch}
                      sx={{
                        cursor: "pointer",
                        mr: { xs: 1, md: 2 },
                        transition: "all 0.2s ease",
                        "&:hover": {
                          color: "primary.main",
                          transform: "rotate(90deg)",
                        },
                      }}
                    />
                  )}
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <IconButton
                    onClick={handleCartToggle}
                    sx={{
                      transition: "color 0.2s ease",
                      p: { xs: 0.5, md: 1 },
                      "&:hover": { color: theme.palette.primary.main },
                    }}
                  >
                    <Badge
                      badgeContent={cartQuantity > 0 ? formatCartCount(cartQuantity) : 0}
                      color="primary"
                      sx={{
                        '& .MuiBadge-badge': {
                          fontSize: '0.7rem',
                          fontWeight: 'bold',
                          minWidth: '18px',
                          height: '18px',
                          padding: '0 4px',
                        }
                      }}
                    >
                      <ShoppingCartIcon />
                    </Badge>
                  </IconButton>
                  <CloseIcon
                    onClick={handleCloseSearch}
                    sx={{
                      cursor: "pointer",
                      transition: "all 0.2s ease",
                      "&:hover": {
                        color: "primary.main",
                        transform: "rotate(90deg)",
                      },
                    }}
                  />
                </Box>
              </Box>

              {/* Search Results */}
              <Suspense fallback={
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                  <CircularProgress />
                </Box>
              }>
                <SearchResults
                  searchQuery={searchQuery}
                  searchResults={searchResults}
                  popularPackages={popularPackages}
                  popularTests={popularTests}
                  isLoadingPopular={isLoadingPopular}
                  isSearching={isSearching}
                  isLoadingSearch={isLoadingSearch}
                  onCloseSearch={handleCloseSearch}
                />
              </Suspense>
            </Box>
          </Container>
        </Box>
      )}
      <Box sx={{ position: "relative" }}>
        {isCartOpen && (
          <Suspense fallback={
            <Box sx={{ position: 'fixed', right: 0, top: 0, bottom: 0, width: { xs: '100%', md: '400px' },
                       bgcolor: 'white', boxShadow: '0 0 20px rgba(0,0,0,0.1)',
                       display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <CircularProgress />
            </Box>
          }>
            <NavbarCart onClose={handleCartClose} />
          </Suspense>
        )}
      </Box>
    </>
  );
};

export default TopNavbar;