import { getEnterpriseCode } from "@/app/oasis/blogs/[blogCode]/layout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_ENDPOINT_PROFILE,
} from "@/constants";
import axios from "axios";

export const generateMetadata = async ({ params }) => {
  const { doctorCode = "" } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode();
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_ENDPOINT_PROFILE}${doctorCode}?enterpriseCode=${enterpriseCode}`;
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { seoTitle = "", seoDescription = "" } = result || {};
      return {
        title: seoTitle || "",
        description: seoDescription || "",
      };
    }
  } catch (error) {
    console.log("generateMetadata", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
