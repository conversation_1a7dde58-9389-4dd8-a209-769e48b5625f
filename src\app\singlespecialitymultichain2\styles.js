import { alpha } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme) => ({
  layoutPadding: {
    padding: "48px 120px",
    [theme.breakpoints.down("md")]: {
      padding: "32px 80px",
    },
    [theme.breakpoints.down("sm")]: {
      padding: "24px 16px",
    },
    [theme.breakpoints.down("xs")]: {
      padding: "16px 16px",
    },
  },
  navbar: {
    padding: "8px 80px",
    boxShadow: `0 3px 45px ${alpha(theme.palette.primary.main, 0.05)}`,
    position: "sticky",
    top: "0",
    zIndex: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    backdropFilter: "blur(10px)",
    [theme.breakpoints.down("md")]: {
      padding: "8px 80px",
    },
    [theme.breakpoints.down("sm")]: {
      padding: "8px 16px",
    },
    [theme.breakpoints.down("xs")]: {
      padding: "8px 16px",
    },
  },
  navbarLogoText: {
    fontSize: "2rem",
    textTransform: "uppercase",
    textShadow: `2px 2px ${theme.palette.primary.main}`,
  },
  navbarList: {
    display: "flex",
    listStyle: "none",
    gap: "2rem",
    alignItems: "center",
    color: "#333333",
    fontSize: "14px",
    fontWeight: "500",
    // "& .active": {
    //   color: 'red'
    // }
  },
  navbarListItem: {
    cursor: "pointer",
    position: "relative",
    color: "inherit",
    "&:before": {
      content: "''",
      position: "absolute",
      width: "0",
      height: "2px",
      bottom: "-5px",
      left: "50%",
      transform: "translate(-50%,0%)",
      backgroundColor: alpha(theme.palette.primary.main, .1),
      transformOrigin: "bottom right",
      visibility: "hidden",
      transition: "all 0.3s ease-in-out",
    },
    "&:hover:before": {
      visibility: "visible",
      transformOrigin: "bottom left",
      width: "100%",
    },
    "&:focus": {
      color: theme.palette.primary.main,
    },
  },
  // active: {
  //   color: 'red'
  // },
  // navbarListItemActive: {
  //   "&:before": {
  //     content: "''",
  //     position: "absolute",
  //     width: "100%",
  //     height: "2px",
  //     bottom: "-5px",
  //     // left: "50%",
  //     // transform: "translate(-50%,0%)",
  //     backgroundColor: theme.palette.secondary.main,
  //     // transformOrigin: "bottom right",
  //     visibility: "visible",
  //     transition: "all 0.3s ease-in-out",
  //   },
  // },
  navbarListItemSelected: {
    color: "red",
  },
  navbarMenuList: {
    padding: "8px",
    color:"#333333",
    fontSize: "14px",
    fontWeight: "500",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
  },
  carousalHeading: {
    color: theme.palette.text.white,
    fontSize: "3.75rem",
    fontWeight: "300",
    [theme.breakpoints.down("md")]: {
      fontSize: "64px !important",
    },
    [theme.breakpoints.down("sm")]: {
      fontSize: "48px !important",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "40px !important",
    },
  },
  carousalHeadingSubHeading: {
    color: theme.palette.text.white,
    fontSize: "1.5rem",
    fontWeight: "400",
    [theme.breakpoints.down("sm")]: {
      fontSize: "24px !important",
    },
    [theme.breakpoints.down("xs")]: {
      fontSize: "20px !important",
      marginTop: "8px",
    },
  },
  carousalContactSectionBox: {
    backgroundColor: "#fff",
    padding: "16px",
    borderRadius: "8px",
    display: "flex",
    gap: "16px",
    boxShadow: `0 2px 6px rgba(26, 24, 30, .2)`,
    minWidth: "240px",
    cursor: "pointer",
    transition: "all .3s ease-in-out",
    "&:hover": {
      backgroundColor: "#F1F0FF",
      transform: "scale(1.05)",
    },
  },
  carousalContactSectionIconBox: {
    height: "48px",
    width: "48px",
    borderRadius: "50px",
    backgroundColor: theme.palette.primary.main,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  carousalContactSectionText: {
    color: "#333333",
    fontWeight: "500",
  },
  carousalContactSectionValue: {
    color: "#333333",
    fontWeight: "400",
  },
  footer: {
    backgroundColor: alpha(theme.palette.primary.main, .05),
    paddingTop: "64px",
    paddingBottom: "64px",
    display: "grid",
    gridTemplateColumns: "repeat(3, 1fr)",
    gap: "6rem",
    [theme.breakpoints.down("sm")]: {
      gap: "4rem",
    },
    [theme.breakpoints.down("xs")]: {
      gap: "3rem",
      gridTemplateColumns: "repeat(1, 1fr)",
    },
  },
  footerGridItem: {
    display: "flex",
    flexDirection: "column",
    gap: "24px",
  },
  footerGridItemMWeb: {
    display: "grid",
    gridTemplateColumns: "1fr",
    gap: "16px",
  },
  footerGridItemHeading: {
    color: theme.palette.primary.main,
    textTransform: "uppercase",
    fontWeight: "500",
    fontSize: "1rem !important",
  },
  footerGridItemText: {
    fontSize: "14px !important",
    // fontWeight: "300 !important",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    cursor: "pointer",
    color: "#333333",
    wordBreak: "break-word",
    "&:hover": {
      color: theme.palette.primary.main,
    },
  },
  footerGridItemAddressIcon: {
    color: theme.palette.primary.main,
  },
  footerGridItemContentBox: {
    display: "flex",
    flexDirection: "column",
    gap: "12px",
  },
  footerGridItemSocialMedia: {
    display: "flex",
    gap: "1rem",
    // [theme.breakpoints.down('sm')]: {
    //   flexDirection: 'column'
    // }
  },
  shadow: {
    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, .05)}`,
  },
  bannerTextBox: {
    position: "absolute",
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    padding: "0 120px",
    [theme.breakpoints.down("md")]: {
      padding: "0 80px",
    },
    [theme.breakpoints.down("sm")]: {
      padding: "0 64px",
    },
  },
  twitterIcon: {
    backgroundColor: theme.palette.primary.main,
    mask: "url(/twitter-icon.svg) no-repeat center / contain",
    // width: "100%",
    height: "32px",
    width: "32px",
  },
}));

export default useStyles;
