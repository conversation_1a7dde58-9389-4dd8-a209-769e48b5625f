"use client";

import { CustomNextArrow, CustomPrevArrow } from "./customArrows";
import ReviewCard from "./reviewCard";
import { alpha, Box, Typography } from "@mui/material";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay, Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { keyframes, useTheme } from "@emotion/react";
import { useRef } from "react";

// Animation keyframes
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const Reviews = ({ value = [], title = "" }) => {
  const theme = useTheme();
  const swiperRef = useRef(null);

  // Custom navigation functions to work with the custom arrows
  const handlePrev = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNext = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  return (
    <Box sx={{
      backgroundColor: alpha(theme.palette.primary.main, .95),
      py: { xs: 4, md: 4 },
      px: { xs: 2, md: 4 },
      overflow: 'hidden',
      position: 'relative',
    }}>
      <Box sx={{
        maxWidth: '1400px',
        mx: 'auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        position: 'relative',
        zIndex: 1,
      }}>
        {/* Title at the top */}
        <Typography variant="h3" sx={{
          width: '100%',
          mb: { xs: 4, md: 5 },
          // color: '#4ECDC4',
          color: "white",
          fontSize: { xs: "1.75rem", sm: "2.25rem", md: "2.5rem" },
          fontWeight: '400',
          textAlign: 'center',
          position: 'relative',
          '&:after': {
            content: '""',
            position: 'absolute',
            bottom: '-12px',
            left: '50%',
            transform: 'translateX(-50%)',
            width: '80px',
            height: '3px',
            // background: 'linear-gradient(90deg, #002147, #4ECDC4)'
            background: "white",
            borderRadius: '3px',
          }
        }}>
          {title || "Patient Stories"}
        </Typography>

        <Box sx={{
          width: '100%',
          animation: `${fadeIn} 0.8s ease-out`,
          position: 'relative',
        }}>
          {/* Custom navigation arrows */}
          <Box
            onClick={handlePrev}
            sx={{
              position: 'absolute',
              left: { xs: '-10px', md: '-30px' },
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
              '&:hover': {
                backgroundColor: 'white',
                boxShadow: '0 5px 15px rgba(0, 0, 0, 0.2)',
              }
            }}
          >
            <CustomPrevArrow />
          </Box>

          <Box
            onClick={handleNext}
            sx={{
              position: 'absolute',
              right: { xs: '-10px', md: '-30px' },
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
              '&:hover': {
                backgroundColor: 'white',
                boxShadow: '0 5px 15px rgba(0, 0, 0, 0.2)',
              }
            }}
          >
            <CustomNextArrow />
          </Box>

          <Box sx={{
            '& .swiper': {
              paddingBottom: '40px !important',
            },
            '& .swiper-pagination': {
              bottom: '0px !important',
            },
            '& .swiper-pagination-bullet': {
              backgroundColor: 'rgb(255, 255, 255) !important',
              opacity: 1,
              width: '8px',
              height: '8px',
              margin: '0 5px',
            },
            '& .swiper-pagination-bullet-active': {
              backgroundColor: 'rgb(255, 255, 255)',
            }
          }}>
            <Swiper
              ref={swiperRef}
              style={{ padding: "5px 5px", paddingBottom: "40px" }}
              modules={[Autoplay, Pagination, Navigation]}
              loop={true}
              spaceBetween={5}
              slidesPerView={1}
              pagination={{
                dynamicBullets: true,
                clickable: true,
              }}
              autoplay={{
                delay: 5000,
                disableOnInteraction: true,
                pauseOnMouseEnter: true,
                stopOnLastSlide: false
              }}
              breakpoints={{
                768: {
                  slidesPerView: 2,
                },
                1024: {
                  slidesPerView: 4,
                },
              }}
            >
              {value.map((item, index) => {
                const { position = null } = item || {};
                return (
                  <SwiperSlide key={index}>
                    <Box
                      id={`reviewCard${index}Pos${position}`}
                      sx={{
                        padding: "12px",
                        height: '100%',
                      }}
                    >
                      <ReviewCard testimonial={item} />
                    </Box>
                  </SwiperSlide>
                );
              })}
            </Swiper>
          </Box>
        </Box>
        </Box>
      </Box>
  );
};

export default Reviews;
