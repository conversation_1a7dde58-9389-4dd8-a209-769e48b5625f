"use client";

import { useEffect, useState } from "react";
import { searchLabs, getLabTests, getLabPackages } from "@/api/harbor.service";
import {
  Box,
  Button,
  Drawer,
  IconButton,
  Typography,
  Fade,
  Slide,
  Divider,
  useTheme,
  alpha,
  InputBase,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import SearchIcon from "@mui/icons-material/Search";
import Link from "next/link";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import debounce from "lodash/debounce";
import NavbarDropdown from "./navbarDropdown";
import SearchResults from "./search";
// import { ArrowLeft, KeyboardArrowLeft, KeyboardArrowLeftOutlined, KeyboardArrowLeftSharp } from "@mui/icons-material";

// Search results will come from API

const MobileNav = ({
  isMenuOpen,
  setIsMenuOpen,
  navbarItemsList,
  logoUrl,
  handleCloseMenu,
  primaryPhone,
  enterpriseCode,
}) => {
  const theme = useTheme();
  const [animationComplete, setAnimationComplete] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isSearchAnimating, setIsSearchAnimating] = useState(false);
  const [popularTests, setPopularTests] = useState([]);
  const [popularPackages, setPopularPackages] = useState([]);
  const [isLoadingPopular, setIsLoadingPopular] = useState(false);

  // Reset animation state when menu closes
  useEffect(() => {
    if (!isMenuOpen) {
      setTimeout(() => {
        setAnimationComplete(false);
      }, 300);
    } else {
      setAnimationComplete(true);
    }
  }, [isMenuOpen]);

  // Add body overflow control for search modal
  useEffect(() => {
    if (isSearchOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isSearchOpen]);

  const handleSearch = debounce(async (query) => {
    if (query.length >= 3) {
      try {
        // Use the API function from harbor.service
        const searchData = await searchLabs(enterpriseCode, query);
        const searchItems = searchData?.data || [];

        // Map the API response to the format expected by the SearchResults component
        const results = searchItems.map(item => ({
          id: item.code,
          title: item.name,
          iconUrl: item.iconUrl, // Pass the actual icon URL
          originalPrice: item.originalPrice,
          discountedPrice: item.discountedPrice,
          discount: `${item.discountPercentage}% off`,
          description: item.shortDescription || item.alternativeNames || '',
          seoSlug: item.seoSlug,
          isPackage: item.type === 'package',
          // Include testsIncluded if totalTestsIncluded is 1 or more
          ...(item.totalTestsIncluded >= 1 && {
            testsIncluded: `${item.totalTestsIncluded} ${item.totalTestsIncluded === 1 ? 'Test' : 'Tests'} included`
          })
        }));

        setSearchResults(results);
      } catch (error) {
        console.error('Error searching:', error);
        setSearchResults([]);
      }
    } else {
      setSearchResults([]);
    }
  }, 250);

  // Fetch popular tests and packages
  const fetchPopularItems = async () => {
    if (popularTests.length > 0 || popularPackages.length > 0 || !enterpriseCode) return;

    setIsLoadingPopular(true);
    try {
      // Use the API functions from harbor.service
      const [packagesData, testsData] = await Promise.all([
        getLabPackages(enterpriseCode),
        getLabTests(enterpriseCode)
      ]);

      const packages = packagesData?.data || [];
      const tests = testsData?.data || [];

      // Format packages data
      const formattedPackages = packages.map(item => ({
        id: item.code,
        title: item.name,
        iconUrl: item.iconUrl,
        originalPrice: item.originalPrice,
        discountedPrice: item.discountedPrice,
        discount: `${item.discountPercentage}% off`,
        description: item.shortDescription || item.alternativeNames || '',
        seoSlug: item.seoSlug,
        isPackage: true,
        ...(item.totalTestsIncluded >= 1 && {
          testsIncluded: `${item.totalTestsIncluded} ${item.totalTestsIncluded === 1 ? 'Test' : 'Tests'} included`
        })
      }));

      // Format tests data
      const formattedTests = tests.map(item => ({
        id: item.code,
        title: item.name,
        iconUrl: item.iconUrl,
        originalPrice: item.originalPrice,
        discountedPrice: item.discountedPrice,
        discount: `${item.discountPercentage}% off`,
        description: item.shortDescription || item.alternativeNames || '',
        seoSlug: item.seoSlug,
        isPackage: false,
        // Include testsIncluded if totalTestsIncluded is 1 or more
        ...(item.totalTestsIncluded >= 1 && {
          testsIncluded: `${item.totalTestsIncluded} ${item.totalTestsIncluded === 1 ? 'Test' : 'Tests'} included`
        })
      }));

      setPopularPackages(formattedPackages);
      setPopularTests(formattedTests);
    } catch (error) {
      console.error('Error fetching popular items:', error);
    } finally {
      setIsLoadingPopular(false);
    }
  };

  const handleSearchClick = () => {
    setIsSearchAnimating(true);
    setIsSearchOpen(true);
    fetchPopularItems(); // Fetch popular items when search modal opens
  };

  const handleCloseSearch = () => {
    setIsSearchAnimating(false);
    setTimeout(() => {
      setIsSearchOpen(false);
      setSearchQuery("");
      setSearchResults([]);
    }, 300);
  };

  const handleInputChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    handleSearch(query);
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center",justifyContent:"space-between",width:"100vw", gap:2 ,p:1 }}>
      {/* Search Input */}
      <Box
        onClick={handleSearchClick}
        sx={{
          flex: 1,
          display: "flex",
          alignItems: "center",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          padding: "6px 12px",
          cursor: "pointer",
          transition: "all 0.2s ease",
          mr: 1,
          "&:hover": {
            borderColor: theme.palette.primary.main,
            boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
          },
        }}
      >
        <SearchIcon sx={{ color: "text.secondary", mr: 1 }} />
        <Typography
          variant="body2"
          color="text.black"
          sx={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            fontSize: "0.9rem"
          }}
        >
          Search for lab tests...
        </Typography>
      </Box>

      {/* Menu Button */}
      <IconButton
        onClick={() => setIsMenuOpen((prev) => !prev)}
        sx={{
          color: "#333",
          "&:hover": {
            backgroundColor: alpha(theme.palette.primary.main, 0.4),
          },
        }}
      >
        <MenuIcon />
      </IconButton>

      {/* Menu Drawer */}
      <Drawer
        anchor="right"
        open={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
        transitionDuration={400}
        PaperProps={{
          sx: {
            width: { xs: "100%", sm: "350px" },
            background: "linear-gradient(to bottom, #ffffff, #f9fffd)",
            boxShadow: "-5px 0 25px rgba(0, 0, 0, 0.1)",
            borderRadius: { xs: "0", sm: "12px 0 0 12px" },
            overflow: "hidden",
          },
        }}
        SlideProps={{
          direction: "left",
        }}
      >
        {/* Header with close button */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: 2,
            borderBottom: "1px solid rgba(0, 169, 157, 0.1)",
          }}
        >
          <Fade in={animationComplete} timeout={600}>
            <Link href="/" style={{ textDecoration: "none" }} onClick={() => setIsMenuOpen(false)}>
              <Box sx={{ cursor: "pointer" }}>
                {logoUrl ? (
                  <Image
                    alt="logo"
                    src={getThumborUrl(logoUrl) || "/placeholder.svg"}
                    width={210}
                    height={70}
                  />
                ) : (
                  <Typography
                    variant="h5"
                    sx={{ color: "#00a99d", fontWeight: "bold" }}
                  >
                    LOGO
                  </Typography>
                )}
              </Box>
            </Link>
          </Fade>

          <IconButton
            onClick={() => setIsMenuOpen(false)}
            sx={{
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              transition: "all 0.3s ease",
              "&:hover": {
                backgroundColor: alpha(theme.palette.primary.main, 0.15),
                transform: "rotate(90deg)",
              },
            }}
          >
            <CloseIcon sx={{ color: theme.palette.primary.main }} />
          </IconButton>
        </Box>

        {/* Menu content */}
        <Box
          sx={{
            height: "calc(100% - 70px)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
          }}
        >
          {/* Navigation items */}
          <Box sx={{ p: 2, overflowY: "auto" }}>
            <ul
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "8px",
                padding: "0",
                listStyle: "none",
                margin: 0,
              }}
            >
              {(navbarItemsList[0]?.sections || []).map((item, index) => {
                const {
                  displayName = "",
                  redirection = {},
                  sections = null,
                  type = 1,
                } = item || {};
                const { redirectionUrl = "" } = redirection || {};

                return (
                  <Slide
                    key={`mobile-nav-item-${index}`}
                    direction="left"
                    in={animationComplete}
                    timeout={300 + index * 100}
                    mountOnEnter
                    unmountOnExit
                  >
                    <li>
                      {type === 2 ? (
                        <Box
                          id={`navbarSection0Item${index}`}
                          sx={{
                            padding: "12px 16px",
                            borderRadius: "8px",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              backgroundColor: alpha(
                                theme.palette.primary.main,
                                0.8
                              ),
                              color: theme.palette.primary.main,
                            },
                            color: "#333333",
                          }}
                        >
                          <Link
                            href={redirectionUrl}
                            target="_blank"
                            style={{
                              textDecoration: "none",
                              color: "inherit",
                              display: "block",
                              width: "100%",
                            }}
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <Typography fontSize="16px" >
                              {displayName || ""}
                            </Typography>
                          </Link>
                        </Box>
                      ) : sections ? (
                        <Box
                          sx={{
                            borderRadius: "8px",
                            overflow: "hidden",
                            backgroundColor: "rgba(255, 255, 255, 0.7)",
                            // mb: 1,
                            fontWeight: 500,
                          }}
                        >
                          <NavbarDropdown
                            key={`${displayName}${index}`}
                            navbarItem={item}
                            id={`navbarSection0Item${index}`}
                            isDrawerOpen={true}
                            sx={{
                              padding: "12px 16px",
                              fontWeight: 500,
                            }}
                            handleCloseDrawer={() => setIsMenuOpen(false)}
                          />
                        </Box>
                      ) : (
                        <Box
                          id={`navbarSection0Item${index}`}
                          sx={{
                            padding: "12px 16px",
                            borderRadius: "8px",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              backgroundColor: alpha(
                                theme.palette.primary.main,
                                0.8
                              ),
                              color: theme.palette.primary.main,
                            },
                            color: "#333333",
                          }}
                        >
                          <Link
                            href={redirectionUrl}
                            style={{
                              textDecoration: "none",
                              color: "inherit",
                              display: "block",
                              width: "100%",
                            }}
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <Typography fontSize="16px" >
                              {displayName || ""}
                            </Typography>
                          </Link>
                        </Box>
                      )}
                    </li>
                  </Slide>
                );
              })}
              {primaryPhone && (
                <Slide
                  direction="left"
                  in={animationComplete}
                  timeout={
                    300 + (navbarItemsList[0]?.sections?.length || 0) * 100
                  }
                  mountOnEnter
                  unmountOnExit
                >
                  <li>
                    <Box
                      id="navbarPrimaryPhone"
                      sx={{
                        padding: "12px 16px",
                        borderRadius: "8px",
                        transition: "all 0.2s ease",
                        "&:hover": {
                          backgroundColor: alpha(
                            theme.palette.primary.main,
                            0.8
                          ),
                          color: theme.palette.primary.main,
                        },
                        color: "#333333",
                      }}
                    >
                      <Link
                        href={`tel:${primaryPhone}`}
                        style={{
                          textDecoration: "none",
                          color: "inherit",
                          display: "block",
                          width: "100%",
                        }}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Typography fontSize="16px" >
                          {primaryPhone}
                        </Typography>
                      </Link>
                    </Box>
                  </li>
                </Slide>
              )}
            </ul>
          </Box>

          {/* Bottom section with CTA button */}
          <Box sx={{ p: 3, mt: 2 }}>
            <Divider sx={{ mb: 3, opacity: 0.6 }} />

            <Fade in={animationComplete} timeout={1000}>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  backgroundColor: theme.palette.primary.main,
                  color: "text.primary",
                  borderRadius: "10px",
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: "16px",
                  padding: "12px 24px",
                  boxShadow: `0 4px 14px ${alpha(theme.palette.primary.main, 0.8)}`,
                  transition: "all 0.3s ease",
                  "&:hover": {
                    backgroundColor: alpha(theme.palette.primary.main, 0.4),
                    transform: "translateY(-2px)",
                    boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.8)}`,
                  },
                }}
              >
                <Link
                  href="/contact-us"
                  style={{
                    textDecoration: "none",
                    color: "text.primary",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    width: "100%",
                    justifyContent: "center",
                  }}
                  onClick={handleCloseMenu}
                >
                  <CalendarMonthIcon sx={{ fontSize: 20 }} />
                  Book Appointment
                </Link>
              </Button>
            </Fade>
          </Box>
        </Box>
      </Drawer>

      {/* Full Screen Search Modal (similar to TopNavbar) */}
      {isSearchOpen && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(255,255,255,0.98)",
            backdropFilter: "blur(10px)",
            zIndex: 1100,
            display: "flex",
            flexDirection: "column",
            opacity: isSearchAnimating ? 1 : 0,
            transform: isSearchAnimating
              ? "translateY(0)"
              : "translateY(-20px)",
            transition: "all 0.3s ease",
          }}
        >

          <Box sx={{ py: { xs: 2, md: 4 }, px: 2, height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
            {/* Search Header */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                mb: { xs: 2, md: 4 },
                position: "relative",
              }}
            >
              <Box
                sx={{
                  flex: 1,
                  maxWidth: "600px",
                  margin: "0 auto",
                  position: "relative",
                  backgroundColor: "white",
                  borderRadius: "12px",
                  boxShadow: "0 4px 20px rgba(0,0,0,0.08)",
                  padding: "4px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <SearchIcon
                  sx={{
                    color: "primary.main",
                    fontSize: { xs: 24, md: 28 },
                    ml: { xs: 1, md: 2 },
                  }}
                />
                <InputBase
                  autoFocus
                  placeholder="Search for lab tests"
                  value={searchQuery}
                  onChange={handleInputChange}
                  sx={{
                    flex: 1,
                    color: "text.black",
                    mx: 2,
                    fontSize: { xs: "1rem", md: "1.1rem" },
                    "& input": {
                      padding: { xs: "8px 0", md: "12px 0" },
                    },
                  }}
                />
                {/* {searchQuery && (
                  <CloseIcon
                    onClick={() => setSearchQuery("")}
                    sx={{
                      cursor: "pointer",
                      mr: { xs: 1, md: 2 },
                      transition: "all 0.2s ease",
                      "&:hover": {
                        color: "primary.main",
                        transform: "rotate(90deg)",
                      },
                    }}
                  />
                )} */}
              </Box>
              <CloseIcon
                onClick={handleCloseSearch}
                sx={{
                  cursor: "pointer",
                  transition: "all 0.2s ease",
                  position: "absolute",
                  right: 3,
                  "&:hover": {
                    color: "primary.main",
                    transform: "rotate(90deg)",
                  },
                }}
              />
            </Box>

            {/* Search Results */}
            <SearchResults
              searchQuery={searchQuery}
              searchResults={searchResults}
              popularPackages={popularPackages}
              popularTests={popularTests}
              isLoadingPopular={isLoadingPopular}
              onCloseSearch={handleCloseSearch}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default MobileNav;
