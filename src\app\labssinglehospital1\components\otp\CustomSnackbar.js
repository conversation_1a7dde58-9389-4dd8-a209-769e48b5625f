'use client';

import React from 'react';
import { Snackbar, Alert } from '@mui/material';
import { createPortal } from 'react-dom';

const CustomSnackbar = ({ open, message, severity, onClose }) => {
  // Use createPortal to render the snackbar at the root level of the DOM
  // This ensures it's outside any stacking contexts created by other components
  const snackbar = (
    <Snackbar
      open={open}
      autoHideDuration={6000}
      onClose={onClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      sx={{
        mb: 2,
        zIndex: 99999, // Extremely high z-index
        position: 'fixed', // Force fixed positioning
        left: '50%', // Center horizontally
        transform: 'translateX(-50%)', // Adjust for centering
        bottom: '24px', // Position from bottom
      }}
    >
      <Alert
        onClose={onClose}
        severity={severity}
        variant="filled"
        elevation={6}
        sx={{
          width: '100%',
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)', // Stronger shadow
          position: 'relative',
          zIndex: 100000, // Even higher z-index
          '& .MuiAlert-icon': {
            fontSize: '1.25rem'
          },
          '& .MuiAlert-message': {
            fontSize: '0.95rem',
            fontWeight: 500
          }
        }}
      >
        {message}
      </Alert>
    </Snackbar>
  );

  // Only create the portal if we're in a browser environment
  if (typeof document !== 'undefined') {
    return createPortal(snackbar, document.body);
  }

  // Fallback for SSR
  return snackbar;
};

export default CustomSnackbar;
