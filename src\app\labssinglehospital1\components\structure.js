"use client";

import { Box } from "@mui/material";
import Navbar from "../Navbar";
import Footer from "../Footer";
import React, { useEffect, useState } from "react";
import BottomNavBar from "@/app/labssinglehospital1/components/bottomNavBar";
import { usePathname } from "next/navigation";

const Structure = ({ children }) => {
  const noNavbarPaths = ["landing-page", "chat"];
  const [showNavbar, setShowNavbar] = useState(true);
  const [isChecking, setIsChecking] = useState(true);
  const pathname = usePathname();
  const isLocationPage = pathname.includes("/locations/");

  useEffect(() => {
    const isShow = !noNavbarPaths.includes(
      window.location.pathname.split("/")[1]
    );
    setShowNavbar(isShow);
    setIsChecking(false);
  }, []);

  if (isChecking) return <></>;
  return (
    <div
      style={{
        minHeight: "100vh",
        maxWidth: "100vw",
      }}
    >
      {showNavbar && <Navbar />}
      <Box sx={{ minHeight: "calc(100vh - 150px)", }} >{children}</Box>
      {showNavbar && !isLocationPage && <Footer />}
      <BottomNavBar />
    </div>
  );
};

export default Structure;
