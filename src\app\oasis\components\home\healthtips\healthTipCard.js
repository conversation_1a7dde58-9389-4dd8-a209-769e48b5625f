"use client";

import { Box, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import ArrowCircleRightIcon from "@mui/icons-material/ArrowCircleRight";

const HealthTipCard = ({ healthTipItem = {} }) => {
  const router = useRouter();
  const {
    shortDescription = "",
    title = "",
    imageUrl = "",
    seoSlug = "",
    showDetails = false,
  } = healthTipItem || {};

  const handleHealthTipRedirection = () => {
    if (seoSlug) router.push(`/health-tips/${seoSlug}`);
  };

  return (
    <Box
      sx={{
        display: "flex",
        borderRadius: "8px",
        boxShadow: "0 1px 10px rgba(0,0,0,.1)",
        cursor: showDetails && "pointer",
        height: { xs: showDetails ?  "220px" : "190px", sm: "200px"},
        padding: "16px",
        position: "relative",
      }}
      onClick={showDetails ? handleHealthTipRedirection : null}
    >
      {imageUrl && (
        <Box
          sx={{
            display: { xs: "none", md: "block" },
            alignContent: "center",
            marginRight: "16px",
          }}
        >
          <Image
            alt={title}
            src={imageUrl}
            height={150}
            width={150}
            style={{
              borderRadius: "8px",
              objectFit: "cover",
            }}
          />
        </Box>
      )}

      {imageUrl && (
        <Box
          sx={{
            display: { xs: "none", md: "none" },
            alignContent: "center",
            marginRight: "16px",
          }}
        >
          <Image
            alt={title}
            src={imageUrl}
            height={100}
            width={100}
            style={{
              borderRadius: "8px",
              objectFit: "cover",
            }}
          />
        </Box>
      )}
      <Box>
        <Typography
          variant="h6"
          sx={{
            fontSize: "16px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: "2",
            WebkitBoxOrient: "vertical",
          }}
        >
          {title || ""}
        </Typography>

        <Typography
          variant="body1"
          sx={{
            overflow: "hidden",
            marginTop: "8px",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            color: "#333333",
            WebkitLineClamp: 5,
            WebkitBoxOrient: "vertical",
          }}
        >
          {shortDescription || ""}
        </Typography>

        <Box
          sx={{
            position: "absolute", // Positioning the box absolutely
            bottom: "12px", // Align to bottom
            right: "16px", // Align to right
            display: showDetails ? "flex" : "none",
            alignItems: "center",
          }}
        >
          <Typography
            variant="subtitle2"
            sx={{
              fontSize: "14px",
              alignContent: "center",
            }}
          >
            View Details
          </Typography>

          <ArrowCircleRightIcon
            sx={{
              padding: "2px",
              color: "primary.main",
              marginLeft: "12px",
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default HealthTipCard;
