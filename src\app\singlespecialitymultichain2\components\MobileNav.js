"use client";

import { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Drawer,
  IconButton,
  Typography,
  Fade,
  Slide,
  Divider,
  useTheme,
  alpha,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import Link from "next/link";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import NavbarDropdown from "./navbarDropdown";

const MobileNav = ({
  isMenuOpen,
  setIsMenuOpen,
  navbarItemsList,
  logoUrl,
  classes,
  handleCloseMenu,
  primaryPhone,
}) => {
  const theme = useTheme();
  const [animationComplete, setAnimationComplete] = useState(false);

  // Reset animation state when menu closes
  useEffect(() => {
    if (!isMenuOpen) {
      setTimeout(() => {
        setAnimationComplete(false);
      }, 300);
    } else {
      setAnimationComplete(true);
    }
  }, [isMenuOpen]);

  return (
    <Box>
      <IconButton
        onClick={() => setIsMenuOpen((prev) => !prev)}
        sx={{
          color: "#333",
          "&:hover": {
            backgroundColor: alpha(theme.palette.primary.main, 0.4),
          },
        }}
      >
        <MenuIcon />
      </IconButton>

      <Drawer
        anchor="right"
        open={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
        transitionDuration={400}
        PaperProps={{
          sx: {
            width: { xs: "100%", sm: "350px" },
            background: "linear-gradient(to bottom, #ffffff, #f9fffd)",
            boxShadow: "-5px 0 25px rgba(0, 0, 0, 0.1)",
            borderRadius: { xs: "0", sm: "12px 0 0 12px" },
            overflow: "hidden",
          },
        }}
        SlideProps={{
          direction: "left",
        }}
      >
        {/* Header with close button */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: 2,
            borderBottom: "1px solid rgba(0, 169, 157, 0.1)",
          }}
        >
          <Fade in={animationComplete} timeout={600}>
            <Link href="/" style={{ textDecoration: "none" }} onClick={() => setIsMenuOpen(false)}>
              <Box sx={{ cursor: "pointer" }}>
                {logoUrl ? (
                  <Image
                    alt="logo"
                    src={getThumborUrl(logoUrl) || "/placeholder.svg"}
                    width={210}
                    height={70}
                  />
                ) : (
                  <Typography
                    variant="h5"
                    sx={{ color: "#00a99d", fontWeight: "bold" }}
                  >
                    LOGO
                  </Typography>
                )}
              </Box>
            </Link>
          </Fade>

          <IconButton
            onClick={() => setIsMenuOpen(false)}
            sx={{
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              transition: "all 0.3s ease",
              "&:hover": {
                backgroundColor: alpha(theme.palette.primary.main, 0.15),
                transform: "rotate(90deg)",
              },
            }}
          >
            <CloseIcon sx={{ color: theme.palette.primary.main }} />
          </IconButton>
        </Box>

        {/* Menu content */}
        <Box
          sx={{
            height: "calc(100% - 70px)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
          }}
        >
          {/* Navigation items */}
          <Box sx={{ p: 2, overflowY: "auto" }}>
            <ul
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "8px",
                padding: "0",
                listStyle: "none",
                margin: 0,
              }}
            >
              {(navbarItemsList[1]?.sections || []).map((item, index) => {
                const {
                  displayName = "",
                  redirection = {},
                  sections = null,
                  type = 1,
                } = item || {};
                const { redirectionUrl = "" } = redirection || {};

                return (
                  <Slide
                    key={`mobile-nav-item-${index}`}
                    direction="left"
                    in={animationComplete}
                    timeout={300 + index * 100}
                    mountOnEnter
                    unmountOnExit
                  >
                    <li>
                      {type === 2 ? (
                        <Box
                          id={`navbarSection0Item${index}`}
                          sx={{
                            padding: "12px 16px",
                            borderRadius: "8px",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              backgroundColor: alpha(
                                theme.palette.primary.main,
                                0.8
                              ),
                              color: theme.palette.primary.main,
                            },
                            color: "#333333",
                          }}
                        >
                          <Link
                            href={redirectionUrl}
                            target="_blank"
                            style={{
                              textDecoration: "none",
                              color: "inherit",
                              display: "block",
                              width: "100%",
                            }}
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <Typography fontSize="16px" >
                              {displayName || ""}
                            </Typography>
                          </Link>
                        </Box>
                      ) : sections ? (
                        <Box
                          sx={{
                            borderRadius: "8px",
                            overflow: "hidden",
                            backgroundColor: "rgba(255, 255, 255, 0.7)",
                            // mb: 1,
                            fontWeight: 500,
                          }}
                        >
                          <NavbarDropdown
                            key={`${displayName}${index}`}
                            navbarItem={item}
                            id={`navbarSection0Item${index}`}
                            isDrawerOpen={true}
                            sx={{
                              padding: "12px 16px",
                              fontWeight: 500,
                            }}
                            handleCloseDrawer={() => setIsMenuOpen(false)} 
                          />
                        </Box>
                      ) : (
                        <Box
                          id={`navbarSection0Item${index}`}
                          sx={{
                            padding: "12px 16px",
                            borderRadius: "8px",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              backgroundColor: alpha(
                                theme.palette.primary.main,
                                0.8
                              ),
                              color: theme.palette.primary.main,
                            },
                            color: "#333333",
                          }}
                        >
                          <Link
                            href={redirectionUrl}
                            style={{
                              textDecoration: "none",
                              color: "inherit",
                              display: "block",
                              width: "100%",
                            }}
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <Typography fontSize="16px" >
                              {displayName || ""}
                            </Typography>
                          </Link>
                        </Box>
                      )}
                    </li>
                  </Slide>
                );
              })}
              {primaryPhone && (
                <Slide
                  direction="left"
                  in={animationComplete}
                  timeout={
                    300 + (navbarItemsList[1]?.sections?.length || 0) * 100
                  }
                  mountOnEnter
                  unmountOnExit
                >
                  <li>
                    <Box
                      id="navbarPrimaryPhone"
                      sx={{
                        padding: "12px 16px",
                        borderRadius: "8px",
                        transition: "all 0.2s ease",
                        "&:hover": {
                          backgroundColor: alpha(
                            theme.palette.primary.main,
                            0.8
                          ),
                          color: theme.palette.primary.main,
                        },
                        color: "#333333",
                      }}
                    >
                      <Link
                        href={`tel:${primaryPhone}`}
                        style={{
                          textDecoration: "none",
                          color: "inherit",
                          display: "block",
                          width: "100%",
                        }}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <Typography fontSize="16px" >
                          {primaryPhone}
                        </Typography>
                      </Link>
                    </Box>
                  </li>
                </Slide>
              )}
            </ul>
          </Box>

          {/* Bottom section with CTA button */}
          <Box sx={{ p: 3, mt: 2 }}>
            <Divider sx={{ mb: 3, opacity: 0.6 }} />

            <Fade in={animationComplete} timeout={1000}>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  backgroundColor: theme.palette.primary.main,
                  color: "white",
                  borderRadius: "10px",
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: "16px",
                  padding: "12px 24px",
                  boxShadow: `0 4px 14px ${alpha(theme.palette.primary.main, 0.8)}`,
                  transition: "all 0.3s ease",
                  "&:hover": {
                    backgroundColor: alpha(theme.palette.primary.main, 0.4),
                    transform: "translateY(-2px)",
                    boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.8)}`,
                  },
                }}
              >
                <Link
                  href="/contact-us"
                  style={{
                    textDecoration: "none",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    width: "100%",
                    justifyContent: "center",
                  }}
                  onClick={handleCloseMenu}
                >
                  <CalendarMonthIcon sx={{ fontSize: 20 }} />
                  Book Appointment
                </Link>
              </Button>
            </Fade>
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
};

export default MobileNav;
