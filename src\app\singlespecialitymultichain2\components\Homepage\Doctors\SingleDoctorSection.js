import React, {useState} from "react";

import {<PERSON>, <PERSON><PERSON>, Link, Typography} from "@mui/material";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";

import useStyles from "../../../styles";
import homepageStyles from "../styles";
import AppointmentDialog from "@/app/components/Appointments/AppointmentDialog";
import {getThumborUrl} from "@/utils/getThumborUrl";
import DoctorCard from "@/app/dewdrop/components/doctorCard";
import SingleDoctorCard from "@/app/serene/components/Homepage/Doctors/singleDoctorCard";
import SectionLayout from "@/app/serene/components/SectionLayout";

const SingleDoctor = ({doctors = []}) => {
    const singleDoctor = doctors.slice(0, 1);
    const commonClasses = useStyles();
    const classes = homepageStyles();
    const [doctorInfo, setDoctorInfo] = useState({});
    const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);

    const handleMakeAppointment = (doctorInfo) => {
        setDoctorInfo(doctorInfo);
        setShowAppointmentDialog(true);
    };

    if (doctors.length === 0) return <></>
    return (
        <SectionLayout id="doctors">
            <Box
                sx={{
                    display: "grid",
                    gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" }, // Single column on mobile, two columns on desktop
                    alignItems: "start",
                    gap: "32px",
                }}
            >
                {/* Meet the Doctor container */}
                <Box
                    sx={{
                        flex: 1,
                        order: { xs: 1, md: 2 }, // On mobile (xs), show after the single doctor; on desktop (md), show on the right
                        display: "flex",
                        flexDirection: "column",
                        gap: "16px",
                    }}
                >
                    <Typography variant="h5"
                                sx={{
                                    textTransform: "uppercase",
                                    color: "primary.main",
                                }}>
                        Meet the Doctor
                    </Typography>

                    {/* Extracting doctor's name and specialty */}
                    {singleDoctor.map((doctor) => {
                        const { doctorDetails } = doctor || {};
                        const { name: doctorName, medicalSpecialities, description: aboutDoctor } = doctorDetails || {};
                        const specialty_name = medicalSpecialities[0]?.name || ""; // Fallback if no specialty
                        let specialty = "is a specialist in ";
                        if (specialty_name.length === 0) {
                            specialty = "";
                        }
                        return (
                            <div key={doctorName}>
                                <Typography
                                    variant="h3"
                                    className={classes.homepageSectionSubHeading}
                                    sx={{
                                        marginBottom: "16px",
                                        color: "#333333"
                                    }}
                                >
                                    {doctorName}
                                </Typography>
                                <Typography variant="body1" style={{ lineHeight: "1.75" }}>
                                    {aboutDoctor ? aboutDoctor : `${doctorName}, ${specialty + specialty_name}, is dedicated to providing you with top-quality care. With a wealth of knowledge and experience, ${doctorName} is here to guide you through your healthcare journey, whether you need a routine check-up or specialized treatment. You can count on ${doctorName} for compassionate support and expertise to help you achieve your best health.`}
                                </Typography>
                            </div>
                        );
                    })}
                </Box>

                {/* Single Doctor container */}
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        order: { xs: 2, md: 1 }, // On mobile (xs), show after the Meet the Doctor; on desktop (md), show on the left
                    }}
                >
                    {singleDoctor.map((doctor, index) => {
                        const { doctorDetails = {} } = doctor || {};
                        const {
                            medicalSpecialities = [],
                            name: doctorName = "",
                            bookAppointmentEnabled = false,
                            profilePicture = null,
                        } = doctorDetails || {};
                        return (
                            <SingleDoctorCard
                                key={index}
                                doctorDetails={doctorDetails}
                                id={`doctor${index}`}
                            />
                        );
                    })}
                </Box>
            </Box>

        </SectionLayout>
    );
};

export default SingleDoctor;
