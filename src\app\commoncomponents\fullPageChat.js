"use client";

import {FullPageChat} from "flowise-embed-react"
import {getPlugins} from "@/api/marketplace.service";
import {useEffect, useState} from "react";

const Chat = ({enterpriseCode = null}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [hasErrors, setHasErrors] = useState(false);
    const [chatFlowId, setChatFlowId] = useState(null);
    const [apiHost, setApiHost] = useState(null);
    const [primaryColor, setPrimaryColor] = useState("#0a5687");
    const [secondaryColor, setSecondaryColor] = useState("#fec84b");
    const [welcomeMessage, setWelcomeMessage] = useState("Hi, how can I help you today?");
    // const response =  getPlugins(enterpriseCode)
    useEffect(() => {
        const fetchPlugins = async () => {
            if (!enterpriseCode) return;
            setIsLoading(true);
            const res = await getPlugins(enterpriseCode);
            const jsonRes = await res.json();
            const aibotConfig = jsonRes.result.aibot;
            if (aibotConfig){
                aibotConfig.forEach(config => {
                    switch (config.configName) {
                        case "chatflowid":
                            setChatFlowId(config.configValue);
                            break;
                        case "apiHost":
                            setApiHost(config.configValue);
                            break;
                        case "primaryColor":
                            setPrimaryColor(config.configValue);
                            break;
                        case "secondaryColor":
                            setSecondaryColor(config.configValue);
                            break;
                        case "welcomeMessage":
                            setWelcomeMessage(config.configValue);
                            break;
                        default:
                            break; // Handle any unknown configNames if needed
                    }
                });
            }else {
                setHasErrors(true)
            }
            setIsLoading(false)
        };

        fetchPlugins();
    }, [enterpriseCode]);

    if (isLoading){
        return null;
    }

    if (hasErrors){
        return null;
    }


    return (
        <FullPageChat
            chatflowid={chatFlowId}
            apiHost={apiHost}
            theme={{
                chatWindow: {
                    showTitle: false,
                    title: 'Chat Bot',
                    titleAvatarSrc: 'https://raw.githubusercontent.com/walkxcode/dashboard-icons/main/svg/google-messages.svg',
                    welcomeMessage: welcomeMessage,
                    errorMessage: 'Something went wrong please try after sometime',
                    backgroundColor: "#ffffff",
                    fontSize: 16,
                    poweredByTextColor: "#303235",
                    botMessage: {
                        backgroundColor: "#f7f8ff",
                        textColor: "#303235",
                        showAvatar: false,
                        avatarSrc: "https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/parroticon.png",
                    },
                    userMessage: {
                        backgroundColor: secondaryColor,
                        textColor: "#ffffff",
                        showAvatar: false,
                        avatarSrc: "https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/usericon.png",
                    },
                    textInput: {
                        placeholder: 'Type your question',
                        backgroundColor: '#ffffff',
                        textColor: '#303235',
                        sendButtonColor: primaryColor,
                        maxChars: 50,
                        maxCharsWarningMessage: 'You exceeded the characters limit. Please input less than 50 characters.',
                        autoFocus: true, // If not used, autofocus is disabled on mobile and enabled on desktop. true enables it on both, false disables it on both.
                    },
                    feedback: {
                        color: '#303235',
                    },
                    footer: {
                        textColor: '#303235',
                        text: 'Powered by',
                        company: 'Docfyn',
                        companyLink: 'https://www.docfyn.com',
                    }
                }
            }}
        />
    );

};

export default Chat;