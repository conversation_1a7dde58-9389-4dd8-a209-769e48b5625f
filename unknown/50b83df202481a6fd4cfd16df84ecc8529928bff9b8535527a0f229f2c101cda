'use client'
import React from "react";
import { Box, Typography, Rating, But<PERSON> } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import "swiper/css";
import "swiper/css/pagination";
import { useRouter } from "next/navigation";

const ReviewsSlider = ({testimonials}) => {
    const router = useRouter()
  return (
   <>
    <Swiper
      modules={[Pagination, Autoplay]}
      spaceBetween={20}
      slidesPerView={1}
      loop={true}
      pagination={{ dynamicBullets: true, clickable: true }}
      autoplay={{ delay: 3000, disableOnInteraction: false }}
      breakpoints={{
        640: {
          slidesPerView: 1,
        },
        768: {
          slidesPerView: 2,
        },
      }}
      style={{
        paddingBottom: "30px",
      }}
    >
      {testimonials?.map((testimonial) => (
        <SwiperSlide key={testimonial.id}>
          <Box
            sx={{
              p: 4,
              minHeight: { xs: "400px", md: "300px" },
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              border: "0.1px solid rgba(26, 26, 26, 0.12)",
              backgroundColor: "#fff",
              borderRadius: "16px",
              boxShadow: "5px 4px 20px rgba(0,0,0,0.05)",
              position: "relative",
              overflow: "visible",
            }}
          >
            <Rating value={testimonial.rating} readOnly sx={{ mb: 2 }} />
            <FormatQuoteIcon
              sx={{
                fontSize: "4rem",
                color: "text.black",
                position: "absolute",
                top: "2rem",
                right: "2rem",
                zIndex: 0,
                opacity: 0.5,
              }}
            />
            <FormatQuoteIcon
              sx={{
                fontSize: "4rem",
                color: "text.black",
                position: "absolute",
                top: "2rem",
                left: "2rem",
                rotate: "180deg",
                zIndex: 0,
                opacity: 0.5,
              }}
            />
            <Typography
              sx={{
                mb: 3,
                lineHeight: 1.8,
                color: "text.black",
                position: "relative",
                zIndex: 1,
              }}
            >
              {testimonial.text}
            </Typography>
            <Box sx={{ mt: "auto" }}>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  color: "text.black",
                }}
              >
                {testimonial.name}
              </Typography>
            </Box>
          </Box>
        </SwiperSlide>
      ))}
    </Swiper>
    <Box sx={{ display: "flex", mt: 1, justifyContent: "center" }}>
    <Button
    onClick={() => router.push("/reviews")}
      sx={{
        borderRadius: "100px",
        backgroundColor: "primary.main",
        transform: "scale(1)",
        color: "text.primary",
        textTransform: "capitalize",
        fontWeight: "normal",
        fontSize: "14px",
        padding: "10px 20px",
        transition: "transform 0.3s ease",
        "&:hover": {
          backgroundColor: "primary.main",
        },
      }}
    >
      View Testimonials
    </Button>
  </Box>
   </>
  );
};

export default ReviewsSlider;
