"use client";

import { Box, Typography } from "@mui/material";
import SectionLayoutAspire from "../styledComponents/SectionLayoutAspire";
import { useTheme } from "@emotion/react";
import SectionHeading from "./sectionHeading";
import PlayCircleIcon from "@mui/icons-material/PlayCircle";
import { useEffect, useState } from "react";
import { YouTubeEmbed } from "@next/third-parties/google";

const VideosSection = ({ multiMedia = [] }) => {
  const theme = useTheme();
  const [videos, setVideos] = useState([]);
  const [selectedVideoIndex, setSelectedVideoIndex] = useState(0);

  useEffect(() => {
     if (multiMedia.length > 0) {
       const filteredVideos = multiMedia.filter((media) => media.video_url) || [];
       setVideos(filteredVideos);
     }
   }, [multiMedia]);
 
 
   if (videos.length === 0) return null;
 
   const selectedVideo = videos[selectedVideoIndex];
  // const [selectedVideo, setSelectedVideo] = useState(null);

  // const handleVideoSelection = (videoUrl, isCurrentVideo) => {
  //   if (!isCurrentVideo) setSelectedVideo(videoUrl);
  // };

  // useEffect(() => {
  //   if (multiMedia.length > 0) {
  //     const videos = multiMedia.filter((media) => media.video_url) || [];
  //     const currentVideo = videos[0]?.video_url;
  //     setSelectedVideo(currentVideo);
  //     setVideos(videos);
  //   }
  // }, [multiMedia]);
  return (
    <SectionLayoutAspire>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
        <SectionHeading align="right">Health Education Videos</SectionHeading>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", md: "1.5fr 1fr" },
            gap: { xs: "20px", sm: "24px", md: "32px" },
          }}
        >
          {/* Main Video Player */}
          <Box
            sx={{
              position: "relative",
              overflow: "hidden",
            }}
          >
            <Box>
              <Box
                sx={{
                  borderRadius: "12px",
                  height: "auto",
                  overflow: "hidden",
                  boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                  backgroundColor: "white",
                }}
              >
                <YouTubeEmbed
                  videoid={selectedVideo?.video_url}
                  params="controls=1"
                  style={{
                    width: "100%",
                    height: "100%",
                    borderRadius: "12px",
                  }}
                />
              </Box>
              {selectedVideo?.title && (
                <Typography
                  variant="body1"
                  sx={{
                    my: 1.5,
                    color: "black",
                    fontSize: { xs: "1rem", sm: "1.125rem" },
                  }}
                >
                  {selectedVideo.title}
                </Typography>
              )}
              {selectedVideo?.subTitle && (
                <Typography
                  variant="body2"
                  sx={{
                    color: "#666",
                    fontSize: { xs: "0.875rem", sm: "1rem" },
                  }}
                >
                  {selectedVideo.subTitle}
                </Typography>
              )}
            </Box>
          </Box>

          {/* Video List */}
          <Box
            sx={{
              maxHeight: { xs: "400px", md: "500px" },
              overflowY: "auto",
              display: "flex",
              flexDirection: "column",
              gap: 2,
              pr: 1,
              "&::-webkit-scrollbar": {
                width: "6px",
              },
              "&::-webkit-scrollbar-track": {
                background: "#f1f1f1",
                borderRadius: "10px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#888",
                borderRadius: "10px",
                "&:hover": {
                  background: "#666",
                },
              },
            }}
          >
            {videos.map((video, index) => {
              const { video_url: videoUrl = "", title = "" } = video || {};
              const isCurrentVideo = selectedVideoIndex === videoUrl;

              return (
                <Box
                  key={index}
                  onClick={() =>
                    !isCurrentVideo && setSelectedVideoIndex(index)
                  }
                  sx={{
                    display: "flex",
                    cursor: "pointer",
                    alignItems: "start",
                    transition: "all 0.3s ease",
                    borderRadius: "12px",
                    backgroundColor: isCurrentVideo
                      ? "rgba(0,0,0,0.05)"
                      : "white",
                    border: "1px solid",
                    borderColor: isCurrentVideo
                      ? "primary.main"
                      : "transparent",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0,0,0,0.08)",
                      transform: "translateY(-2px)",
                      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                    },
                  }}
                >
                  <Box
                    sx={{
                      position: "relative",
                      width: { xs: "120px", sm: "160px", md: "180px" },
                      height: { xs: "80px", sm: "90px", md: "100px" },
                      flexShrink: 0,
                      borderRadius: "8px",
                      overflow: "hidden",
                      m: 1,
                    }}
                  >
                    <YouTubeEmbed
                      videoid={videoUrl}
                      params="controls=0"
                      style={{
                        borderRadius: "8px",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                  </Box>

                  <Box
                    sx={{
                      flex: 1,
                      p: { xs: 1.5, sm: 2 },
                    }}
                  >
                    {title && (
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontSize: { xs: "0.875rem", sm: "1rem" },
                          display: "-webkit-box",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                          overflow: "hidden",
                          lineHeight: 1.3,
                          mb: 0.5,
                        }}
                      >
                        {title}
                      </Typography>
                    )}
                    {videos?.subTitle && (
                      <Typography
                        variant="body2"
                        color="rgb(75, 75, 75)"
                        sx={{
                          mb: 0.5,
                          fontSize: { xs: "0.75rem", sm: "0.875rem" },
                        }}
                      >
                        {videos?.subTitle}
                      </Typography>
                    )}
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{
                        fontSize: { xs: "0.7rem", sm: "0.75rem" },
                        opacity: 0.8,
                      }}
                    >
                      {new Date().toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        year: "numeric",
                      })}
                    </Typography>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Box>
      </Box>
    </SectionLayoutAspire>
  );
};

export default VideosSection;
