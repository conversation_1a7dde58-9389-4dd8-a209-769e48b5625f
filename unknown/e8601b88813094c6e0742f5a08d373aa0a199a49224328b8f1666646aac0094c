"use client";

import { Box, Divider, Typography } from "@mui/material";
import Rating from "@mui/material/Rating";
import SectionLayoutAspire from "../aspire/styledComponents/SectionLayoutAspire";
import { useTheme } from "@emotion/react";
import useStyles from "../aspire/styles";
import OutlinedButton from "../aspire/styledComponents/OutlinedButton";
import { getInitials } from "../utils/getInitials";
import SectionHeading from "../aspire/components/sectionHeading";
import { useRouter } from "next/navigation";
import LatestReviewCard from "./latestReviewCard";
import { useContext, useEffect, useState } from "react";
import dynamic from "next/dynamic";
// import AllReviewsDrawer from "./allReviewsDrawer";
// import { AppContext } from "@/app/AppContextLayout";
import {
  API_ENDPOINT_AGGREGATED_REVIEWS,
  API_ENDPOINT_REVIEWS,
  API_SECTION_API,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_VERSION,
  HARBOR_API_DOCFYN_DOMAIN,
} from "@/constants";
// import axios from "axios";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import Image from "next/image";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";

const AllReviewsDrawer = dynamic(() => import('./allReviewsDrawer'), {
  ssr:false
})

const ReviewsSection = ({
  enterpriseCode = null,
  entityCode = null,
  specialityCode = null,
  padding = null,
  isDetailPage = false,
  testimonials = [],
  showDefaultReviews = false,
}) => {
  const classes = useStyles();
  const theme = useTheme();
  const router = useRouter();
  const [viewReviewsDrawer, setViewReviewsDrawer] = useState(false);
  const [latestReviews, setLatestReviews] = useState([]);
  const [overallRating, setOverallRating] = useState([]);
  const [isReviewsLoading, setIsReviewsLoading] = useState(true);

  const reviewTypeColors = {
    practo: '#28328c',
    justdial: '#007A0C',
    google: '#F4B400',
    default: '#333333',
  };

  const getReviews = async () => {
    setIsReviewsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}/${API_ENDPOINT_AGGREGATED_REVIEWS}?list=true&page=1${entityCode ? `&entityCode=${entityCode}` : ""}${specialityCode ? `&specialityCode=${specialityCode}` : ""}`;
    try {
      const response = await fetch(url);
      const status = response.status;
      const data = await response.json();
      // const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { data: reviews = [] } = result || {};
        setLatestReviews(reviews);
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsReviewsLoading(false);
    }
  };

  const getAggregatedRating = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}/aggregatedOverallReviews?list=true&page=1`;
    try {
      // const response = await axios.get(url);
      // const { status = null, data = {} } = response || {};
      const response = await fetch(url);
      const status = response.status;
      const data = await response.json();
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { data: ratings = [] } = result || {};
        setOverallRating(ratings);
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  const handleViewAllReview = () => {
    if (showDefaultReviews && latestReviews.length === 0) router.push("/reviews");
    else setViewReviewsDrawer(true);
  };

  useEffect(() => {
    if (enterpriseCode && (isDetailPage ? Boolean(specialityCode) : true))
      getReviews();
  }, [enterpriseCode, specialityCode]);

  useEffect(() => {
    if (latestReviews.length > 0 && !specialityCode && !entityCode) {
      getAggregatedRating();
    }
  }, [latestReviews]);

  return (
    <>
      {overallRating.length > 0 && (
        <Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "48px",
            }}
          >
            <SectionHeading>Patient Reviews</SectionHeading>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  md: "repeat(3, 1fr)",
                },
                gap: "48px",
              }}
            >
              {overallRating.map((item, index) => {
                const {
                  rating = "",
                  reviewSource = {},
                  ratingType = "",
                  totalReviews = ""
                } = item || {};
                const { icon = ""} = reviewSource || {};
                return (
                  <Box
                    key={index}
                    id="aggregated_card"
                    onClick={() => setViewReviewsDrawer(true)}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      gap: "16px",
                      cursor: 'pointer'
                    }}
                  >
                    <Box
                      sx={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        gap: "16px",
                      }}
                    >
                      <Box
                        sx={{
                          position: "relative",
                          height: "48px",
                          width: "100%",
                        }}
                      >
                        {icon && (
                          <Image
                            src={getThumborUrl(icon || "")}
                            alt="review icon"
                            fill
                            sizes={{ maxWidth: "100%", height: "48px" }}
                            style={{ objectFit: "contain" }}
                          />
                        )}
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: "8px",
                          justifyContent: "center",
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          align="center"
                          sx={{ fontSize: "32px", color: reviewTypeColors.default }}
                        >
                          {rating}
                          {ratingType === 1 && "%"}
                        </Typography>
                        <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          flexDirection: "column",
                          gap: "8px",
                        }}>
                        {ratingType === 2 && (
                          <Rating readOnly value={rating} precision={0.5} />
                        )}
                          { (totalReviews )&& (
                              <Typography
                                  variant="body2"
                                  align="center"
                                  sx={{ color: '#808080'}}
                              >
                                {`(${totalReviews} reviews)`}
                              </Typography>
                          )}
                        </Box>
                      </Box>
                    </Box>
                    {index < overallRating.length - 1 && (
                      <Divider
                        orientation="vertical"
                        flexItem
                        sx={{
                          borderWidth: "2px",
                          display: { xs: "none", md: "flex" },
                        }}
                      />
                    )}
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Box>
      )}
      {!isReviewsLoading && latestReviews.length === 0 && showDefaultReviews ? (
        <Box background={"#fcf9f9"}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "48px",
            }}
          >
            <SectionHeading>Latest Reviews</SectionHeading>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  md: "repeat(3, 1fr)",
                },
                gap: "32px",
              }}
            >
              {testimonials.slice(0, 3).map((testimonial, index) => {
                const {
                  name = "",
                  rating = null,
                  text = "",
                } = testimonial || {};
                return (
                  <Box key={index}>
                    <Box className={classes.reviewsSectionCarousalBoxItem}>
                      <Box className={classes.reviewsSectionContentBox}>
                        <Box className={classes.reviewsSectionReviewBox}>
                          <Rating
                            name="simple-controlled"
                            value={rating}
                            readOnly
                          />
                          <Typography
                            variant="body1"
                            className={classes.reviewsSectionReviewBoxText}
                          >
                            {text || ""}
                          </Typography>
                        </Box>
                      </Box>
                      <Box className={classes.reviewsSectionProfileBox}>
                        <Box className={classes.reviewsSectionProfileImg}>
                          {getInitials(name) || ""}
                        </Box>
                        <Box
                          className={classes.reviewsSectionProfileDetailsBox}
                        >
                          <Typography
                            className={classes.reviewsSectionProfileName}
                          >
                            {name || ""}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Box>
            <Box sx={{ textAlign: "center" }}>
              <OutlinedButton
                id="viewAllReviews"
                style={{ width: "fit-content" }}
                onClick={handleViewAllReview}
              >
                View all
              </OutlinedButton>
            </Box>
          </Box>
        </Box>
      ) : (
        latestReviews.length > 0 && (
          <Box
            background={"#fcf9f9"}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                marginTop: "96px",
                gap: "48px",
              }}
            >
              <SectionHeading>Latest Reviews</SectionHeading>
              <Box
              // sx={{
              //   display: "grid",
              //   gridTemplateColumns: {
              //     xs: "1fr",
              //     sm: "1fr 1fr",
              //     md: "repeat(3, 1fr)",
              //   },
              //   gap: "32px",
              // }}
              >
                <Swiper
                  spaceBetween={36}
                  breakpoints={{
                    0: {
                      slidesPerView: 1,
                    },
                    600: {
                      slidesPerView: 2,
                    },
                    1200: {
                      slidesPerView: 3,
                    },
                  }}
                  navigation={true}
                  modules={[Navigation]}
                  className="mySwiper"
                  style={{
                    "--swiper-navigation-color": "black",
                    "--swiper-navigation-size": "24px",
                    "--swiper-navigation-sides-offset": "10px",
                  }}
                >
                  {latestReviews.slice(0, 7).map((testimonial, index) => {
                    return (
                      <SwiperSlide key={index}>
                        <LatestReviewCard
                          testimonial={testimonial}
                          setViewReviewsDrawer={setViewReviewsDrawer}
                        />
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </Box>
              <Box sx={{ textAlign: "center" }}>
                <OutlinedButton
                  id="viewAllReviews"
                  style={{ width: "fit-content" }}
                  onClick={handleViewAllReview}
                >
                  View all
                </OutlinedButton>
              </Box>
            </Box>
            <AllReviewsDrawer
              enterpriseCode={enterpriseCode}
              entityCode={entityCode}
              specialityCode={specialityCode}
              viewReviewsDrawer={viewReviewsDrawer}
              setViewReviewsDrawer={setViewReviewsDrawer}
            />
          </Box>
        )
      )}
    </>
  );
};

export default ReviewsSection;
