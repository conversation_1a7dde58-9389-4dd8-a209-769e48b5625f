import { Box, Container, Typography } from '@mui/material'
import React from 'react'
import FaqClient from '../components/FaqClient'

const LocationFaqWrapper = ({faqs,heading,iconUrl = ""}) => {
  return (
    <Container maxWidth="lg" sx={{ py:"32px" }}>
    <Box
      component="section"
      sx={{
        position: "relative",
        maxWidth: "2150px",
        mx: "auto",
        width: "100%",
        height: { xs: "auto", md: "auto" }, // Fixed height for desktop
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          textAlign: "center",
          mb: { xs: 4, sm: 5, md: 3 },
          maxWidth: "800px",
          mx: "auto",
        }}
      >
        <Typography
          variant="h3"
          sx={{
            color: "#1a1a1a",
            mb: 2,
            fontSize: {
              xs: '24px',
              md: '36px', 
            },
            color:"text.black",
            lineHeight: 1.2,
          }}
        >
          {heading || "Health FAQs"}
          
        </Typography>
      </Box>
      <FaqClient faqs={faqs} iconUrl={iconUrl}/>
    </Box>
  </Container>
  )
}

export default LocationFaqWrapper