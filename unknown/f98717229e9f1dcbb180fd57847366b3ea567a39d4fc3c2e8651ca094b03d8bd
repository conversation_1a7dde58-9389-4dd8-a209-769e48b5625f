"use client";

import { Box, Typography } from "@mui/material";
import { useContext } from "react";
import { AppContext } from "../../AppContextLayout";
import { formatPhoneNumber } from "../../utils/formatPhoneNumber";

const CallUsButton = () => {
  const { websiteData = {} } = useContext(AppContext);
  const { phoneNumbers = [] } = websiteData || {};

  const handleCallUs = () => {
    const primaryNumber =
      phoneNumbers.filter((item) => item.is_primary)[0] || {};
    const { phone: primaryPhone = null } = primaryNumber || {};
    if (primaryPhone) {
      const phoneNo = formatPhoneNumber(primaryPhone);
      window.open(`tel:${phoneNo}`, "_blank");
    }
  };

  return (
    // <Box
    //   id="navbarCallUsBtn"
    //   onClick={handleCallUs}
    //   sx={{
    //     cursor: "pointer",
    //     color: "primary.main",
    //     transition: "all .3s",
    //     "&:hover": {
    //       transform: "scale(1.1)",
    //     },
    //   }}
    // >
    //   <Box
    //     sx={{
    //       backgroundColor: "primary.main",
    //       mask: "url(/request-call.svg) no-repeat center / contain",
    //       height: "32px",
    //       width: "32px",
    //     }}
    //   ></Box>
    //   <Typography
    //     variant="subtitle1"
    //     align="center"
    //     sx={{ fontSize: "12px", color: "primary.main" }}
    //   >
    //     Call us
    //   </Typography>
    // </Box>

    <Box
        id="navbarCallUsBtn"
        onClick={handleCallUs}
        sx={{
        display: "flex",
        flexDirection: "column", // Stack children vertically
        alignItems: "center", // Center align items horizontally
            cursor: "pointer",
            color: "primary.main",
            transition: "all .3s",
            "&:hover": {
                transform: "scale(1.1)",
            },
    }}>

        <Box
            sx={{
                backgroundColor: "primary.main",
                mask: "url(/call_2.svg) no-repeat center / contain",
                height: "36px",
                width: "36px",
            }}
        />

        <Typography
            fontSize="12px"
            color="primary.main"
        >
            Call Us
        </Typography>

    </Box>

);
};

export default CallUsButton;
