"use client"
import <PERSON>ript from 'next/script';
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import {getWebsiteHost} from "@/app/utils/clientOnly/clientUtils";
import { useContext } from 'react';
import { AppContext } from '../AppContextLayout';

const DoctorStructureDataScript = ({doctor, domainSlug}) => {
    // Create structured data for the physician
    const { websiteData } = useContext(AppContext);

    const structuredData = {
        "@context": "https://schema.org",
        "@type": "Physician",
        "name": doctor.name
    };

    //Image
    if (doctor.profilePicture) {
        structuredData.image = getThumborUrl(doctor.profilePicture)
    }

    //URL
    if (domainSlug) {
        structuredData.url = `https://${getWebsiteHost()}/doctors/${domainSlug}/${doctor.seoSlug}`
    } else {
        structuredData.url = `https://${getWebsiteHost()}/doctors/${doctor.seoSlug}`
    }

    //medical specialities
    if (doctor.enterpriseSpecialities && doctor.enterpriseSpecialities.length > 0) {
        structuredData.medicalSpecialty = doctor.enterpriseSpecialities.map(specialty => specialty.displayName)
    }

    //Desc
    if (doctor.description) {
        structuredData.description = doctor.description
    }

    //Price
    if (doctor.addtionalDetails && doctor.addtionalDetails.consultationFee) {
        structuredData.priceRange = `INR ${doctor.addtionalDetails.consultationFee}`
    }

    //OPD timings
    if (doctor.practiceTimings && doctor.practiceTimings.availability.length > 0){
        structuredData.openingHours = doctor.practiceTimings.availability.reduce((openingHours, slot) => {
            const days = slot.entries.map(entry => ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"][entry.day - 1]);
            const dayString = days.join(",");
            const timeString = `${dayString} ${slot.slotStartTime}-${slot.slotEndTime}`;
            return [...openingHours, timeString];
        }, [])
    }

    //qualification
    if(doctor.educationDetails){
        structuredData.alumniOf = doctor.educationDetails.map(education => {
            const educationData = {
                "@type": "EducationalOccupationalCredential",
                "credentialCategory": education.degree?.name || null,
                "recognizedBy": {
                    "@type": "EducationalOrganization",
                    "name": education.institute?.institutionName || null,
                },
                "dateAwarded": education.passoutYear || null
            };
            return educationData;
        })
    }

    //memberships
    if (doctor.memberships && doctor.memberships.length > 0){
        structuredData.memberOf = doctor.memberships.map(membership => {
            const membershipData = {
                "@type": "Organization",
                "name": membership.membershipName || null
            };
            // Remove null fields
            Object.keys(membershipData).forEach(key => {
                if (membershipData[key] === null) {
                    delete membershipData[key];
                }
            });

            return membershipData;
        });
    }

    //Experience
    // if ( doctor.experienceDetails &&  doctor.experienceDetails.length >0){
    //     structuredData.hasOccupation =  {
    //         "@type": "Occupation",
    //             "name": "Physician",
    //             "description": "Medical professional",
    //             "worksFor": doctor.experienceDetails.map(experience => {
    //             const experienceData = {
    //                 "@type": "Organization",
    //                 "name": experience.instituteName || null,
    //                 "jobTitle": experience.designationName || null,
    //                 "startDate": experience.startYear || null,
    //                 "endDate": experience.endYear || null
    //             };
    //
    //             // Remove null fields
    //             Object.keys(experienceData).forEach(key => {
    //                 if (experienceData[key] === null) {
    //                     delete experienceData[key];
    //                 }
    //             });
    //
    //             return experienceData;
    //         })
    //     }
    // }

    //Awards
    if (doctor.certificationDetails && doctor.certificationDetails.length > 0){
        structuredData.credential = doctor.certificationDetails.map(certification => {
            const certificationData = {
                "@type": "EducationalOccupationalCredential",
                "name": certification.certificationName || null,
                "provider": {
                    "@type": "EducationalOrganization",
                    "name": certification.certificationBody || null
                },
                "dateAwarded": certification.yearOfCertificate || null
            };

            Object.keys(certificationData).forEach(key => {
                if (certificationData[key] === null) {
                    delete certificationData[key];
                }
            });

            return certificationData;
        })
    }

    // address
    if (websiteData.addresses && websiteData.addresses.length > 0) {
        structuredData.address = {
            "@type": "PostalAddress",
            "streetAddress": websiteData.addresses[0].line1 + websiteData.addresses[0].line2,
            "addressLocality": websiteData.addresses[0].city,
            "addressRegion": websiteData.addresses[0].state,
            "postalCode": websiteData.addresses[0].postal_code,
        };
    }

    return (
        <Script
            type="application/ld+json"
            dangerouslySetInnerHTML={{__html: JSON.stringify(structuredData)}}
        />
    );
};

export default DoctorStructureDataScript;
