"use client";
import React, { useState, useEffect, useRef } from "react";
import { Box, Typography, Container, Grid } from "@mui/material";
import Image from "next/image";
import SectionLayoutSingleSpecialitySingleHospital from "../singlespecialitysinglehospital1/styledComponents/SectionLayoutSingleSpecialitySingleHospital";

const CounterItem = ({ icon: Icon, endValue, label }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => {
      if (counterRef.current) {
        observer.unobserve(counterRef.current);
      }
    };
  }, []);

  useEffect(() => {
    let animationFrame;
    const duration = 2000; // 2 seconds
    const startTime = Date.now();

    const animate = () => {
      const currentTime = Date.now();
      const progress = Math.min((currentTime - startTime) / duration, 1);

      if (progress < 1) {
        setCount(Math.floor(endValue * progress));
        animationFrame = requestAnimationFrame(animate);
      } else {
        setCount(endValue);
      }
    };

    if (isVisible) {
      animate();
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, endValue]);

  return (
    <Box
      ref={counterRef}
      sx={{
        display: "flex",
        alignItems: "center",
        // px: 1,
        gap: 2,
        textAlign: "center",
      }}
    >
      <Image src={Icon} width={50} height={50} color="#1976d2" />
      <Box sx={{ textAlign: "left" }}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: "bold",
            fontSize: "2.2rem",
            my: 1,
            color: "primary.main",
          }}
        >
          {count}+
        </Typography>
        <Typography
          variant="h5"
          color="text.secondary"
          sx={{
            fontSize: "1.2rem",
            fontWeight: "medium",
          }}
        >
          {label}
        </Typography>
      </Box>
    </Box>
  );
};

const CounterSection = () => {
  // const stats = [
  //   {
  //       icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/bd85345ae2f8305dff78249fc8b9c4d85b27d5dd/verify.webp",
  //       value: 10,
  //       label: "Years of Trust"
  //   },
  //     {
  //         icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/4cc9b13fad2aa9240a89deda8a249aa3dcabeecc/eye.webp",
  //         value: 5000,
  //         label: "Successful Eye Treatments"
  //     },
  //     {
  //         icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/c745826b9170b710f4e84d3cafcf7c0d35496fc2/care.webp",
  //         value: 20000,
  //         label: "Lives Touched"
  //     },
  //     {
  //         icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/a381c8b0e0c26bf7aed3a0ed199c3d7e872a58b1/medical-team.webp",
  //         value: 8,
  //         label: "Specialties"
  //     }
  //
  // ];

    const stats = [
        {
            icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/e6b3262e0e602ef45320d934be00447c69aaae2b/verify-2.webp",
            value: 10,
            label: "Years of Trust"
        },
        {
            icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/96297f8a8ed714d66305fc9fdaee804a2744ca29/eye-exam.webp\n",
            value: 5000,
            label: "Successful Eye Treatments"
        },
        {
            icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/b6fce48fb15def7bb665037b34ad053369ad4776/care-1.webp",
            value: 20000,
            label: "Lives Touched"
        },
        {
            icon: "https://cdn.docfyn.com/com.harbor/documents/2025/02/26/0e993e43fe0aa807370a23b617783a083d61549c/medical-staff-1.webp",
            value: 8,
            label: "Specialties"
        }

    ];


    return (
    <Box>
      <SectionLayoutSingleSpecialitySingleHospital>
        <Box
          sx={{
            py: 5,
            minHeight: "30vh",
            display: "flex",
            alignItems: "center",
              marginLeft: "40px",
            justifyContent: "center",
          }}
        >
          <Box
            sx={{
              width: "100%",
                flex: 1,
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                flexWrap: "wrap",
                gap: 6,
              }}
            >
              {stats.map((stat, index) => (
                <Box
                  key={index}
                  sx={{
                    flex: "1 1 250px", 
                    maxWidth: "300px",
                    minWidth: "200px",
                  }}
                >
                  <CounterItem
                    icon={stat.icon}
                    endValue={stat.value}
                    label={stat.label}
                  />
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </SectionLayoutSingleSpecialitySingleHospital>
    </Box>
  );
};

export default CounterSection;
