import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isCartOpen: false,
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setCartOpen: (state, action) => {
      state.isCartOpen = action.payload;
    },
  },
});

// Export actions
export const { setCartOpen } = uiSlice.actions;

// Selectors
export const selectIsCartOpen = (state) => state.ui.isCartOpen;

export default uiSlice.reducer;
