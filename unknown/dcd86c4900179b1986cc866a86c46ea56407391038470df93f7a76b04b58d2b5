"use client";

import Image from "next/image";
import Link from "next/link";
import { Box, Divider, Typography, useMediaQuery } from "@mui/material";
import NavbarDropdown from "./components/navbarDropdown";
import NavbarMenu from "./components/navbarMenu";
import { useContext, useEffect, useState, useMemo } from "react";
import { AppContext } from "../AppContextLayout";
import { getThumborUrl } from "../utils/getThumborUrl";
import { getNavbarItems } from "../../api/harbor.service";
import { useTheme } from "@emotion/react";
import BottomNavBar from "@/app/commoncomponents/bottomNavBar";
import PrimaryButton from "./styledComponents/PrimaryButton";
import { useRouter } from "next/navigation";
import CallUsButton from "./components/callUsButton";

const Navbar = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const [navbarItemsList, setNavbarItemsList] = useState([]);
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, logo_url: logoUrl = "" } =
    websiteData || {};

  useEffect(() => {
    const fetchNavbarItems = async () => {
      try {
        const data = await getNavbarItems(enterpriseCode);
        setNavbarItemsList(data?.result || []);
        // console.log("Navbar Items", data?.result);
      } catch (error) {
        setViewSnackbarMain({
          message: "Something went wrong!",
          type: "error",
        });
      }
    };
    if (enterpriseCode) fetchNavbarItems();
  }, [enterpriseCode]);

  const navbarItems = useMemo(
    () => navbarItemsList[0]?.sections || [],
    [navbarItemsList]
  );

  return (
    <>
      {/* Fixed Navbar */}
      <Box
        sx={{
          position:  "sticky",
          bgcolor: "#fff",
          width: "100%",
          top: 0,
          zIndex: 1100,
          boxShadow: "0px 2px 10px rgba(0,0,0,0.1)",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: {
              xs: "5px 12px",
              sm: "5px 12px",
              md: "5px 10px",
              lg: "5px 20px",
            },
          }}
        >
          {/* Logo Section */}
          <Link href="/" style={{ display: "flex", alignItems: "center" }}>
            {logoUrl && (
              <Image
                alt="logo"
                src={getThumborUrl(logoUrl) || "/placeholder.svg"}
                width={210}
                height={70}
                style={{ objectFit: "cover" }}
              />
            )}
          </Link>

          {/* Desktop Navigation */}
          {!isMobile && (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flex: 1,
                flexWrap: "wrap", 
                maxWidth: "100%",
                gap: 2,
                fontSize: { xs: "12px", sm: "14px", md: "16px" }, 
                padding: { xs: "0px 10px", sm: "0px 5px", }, 
              }}
            >
              {navbarItems.map((item, index) => {
                const {
                  displayName = "",
                  redirection = {},
                  sections = null,
                  type = 1,
                } = item || {};
                const { redirectionUrl = "" } = redirection || {};

                if (type === 2) {
                  return (
                    <Box
                      key={index}
                      sx={{
                        position: "relative",
                        "&:hover a": { color: theme.palette.primary.main },
                        "&::after": {
                          content: '""',
                          position: "absolute",
                          bottom: "-4px",
                          left: 0,
                          width: "0%",
                          height: "2px",
                          backgroundColor: theme.palette.primary.main,
                          transition: "width 0.3s ease",
                        },
                        "&:hover::after": { width: "100%" },
                      }}
                    >
                      <Link
                        href={redirectionUrl}
                        target="_blank"
                        style={{
                          textDecoration: "none",
                          color: "#333",
                          transition: "color 0.3s ease",
                        }}
                      >
                        <Typography
                          sx={{
                            color: "#333",
                            fontSize: "16px",
                            fontWeight: 500,
                          }}
                        >
                          {displayName}
                        </Typography>
                      </Link>
                    </Box>
                  );
                }

                if (sections) {
                  return <NavbarDropdown key={index} navbarItem={item} />;
                }

                return (
                  <Box
                    key={index}
                    sx={{
                      position: "relative",
                      "&:hover a": { color: theme.palette.primary.main },
                      "&::after": {
                        content: '""',
                        position: "absolute",
                        bottom: "-4px",
                        left: 0,
                        width: "0%",
                        height: "2px",
                        backgroundColor: theme.palette.primary.main,
                        transition: "width 0.3s ease",
                      },
                      "&:hover::after": { width: "100%" },
                    }}
                  >
                    <Link
                      href={redirectionUrl}
                      style={{
                        textDecoration: "none",
                        color: "#333",
                        transition: "color 0.3s ease",
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: "16px",
                          fontWeight: 500,
                          textTransform: "capitalize",
                          letterSpacing: "0.2px",
                        }}
                      >
                        {displayName}
                      </Typography>
                    </Link>
                  </Box>
                );
              })}
            </Box>
          )}

          {/* Right Section - Call Button & Mobile Menu */}
          <Box sx={{ display: "flex", alignItems: "center", gap: "10px" }}>
            {/* Desktop - Book Appointment */}
            {!isMobile && (
              <PrimaryButton onClick={() => router.push("/doctors")}>
                Book Appointment
              </PrimaryButton>
            )}

            {/* Mobile Menu */}
            {isMobile && (
              <Box sx={{ display: "flex", alignItems: "center", gap: "16px" }}>
                <CallUsButton/>
                <Divider
                      orientation="vertical"
                      flexItem
                      sx={{ display: { xs: "inline-block", md: "none" } }}
                  />
                <NavbarMenu navbarItemsList={navbarItemsList} />
              </Box>
            )}
          </Box>
        </Box>
      </Box>

      {/* Bottom Navigation for Mobile */}
      {isMobile && <BottomNavBar />}
    </>
  );
};

export default Navbar;
