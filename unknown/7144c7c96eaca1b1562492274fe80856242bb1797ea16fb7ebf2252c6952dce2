import { CurrencyRupee, CurrencyRupeeSharp, Money, Work } from "@mui/icons-material";
import { Box, Button, Typography, Paper } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";

const DoctorCard = ({
  name,
  specialization,
  imageUrl,
  cardsVisible,
  index,
  experience,
  consultationFee,
  doctorCode,
  seoSlug
}) => {
  const router = useRouter()

  const handleDoctorClick = (e) => {
    e.preventDefault();
    router.push(`/doctors/${seoSlug || doctorCode}`);
  };

  return (
    <Box
    onClick={handleDoctorClick}
      sx={{
        cursor: "pointer",
        maxWidth: 280,
        height: 420,
        width: "100%",
        borderRadius: "20px",
        padding: "10px 10px 0 10px",
        backgroundColor: "#fff",
        opacity: cardsVisible ? 1 : 0,
        transform: cardsVisible ? "translateY(0)" : "translateY(30px)", // Better transition
        transition: `opacity 0.2s ease-out ${(index + 1) * 0.1}s, 
                     transform 0.2s ease-out ${(index + 1) * 0.1}s`,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)", 
        "&:hover": {
          transform: "scale(1.03)", 
          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.15)",
        },
      }}
    >
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: 260,
          borderRadius: "20px",
          overflow: "hidden",
          marginBottom: "8px",
        }}
      >
        <Image
          src={imageUrl || "/doctor-profile-icon.png"}
          alt={name}
          fill
          style={{
            objectFit: "cover",
            borderRadius: "20px",
          }}
          priority
        />
      </Box>

      <Box sx={{
          textAlign: "center",
          padding: "8px 0 16px",
        }}>
        <Typography
          variant="h3"
          sx={{
            mb: 0.5,
            color: "#1a1a1a",
            fontSize: "1.125rem",
          }}
        >
          {name}
        </Typography>

        <Typography
          variant="body2"
          sx={{
            color: "rgb(75, 75, 75)",
            mb: 1.5,
            display: "-webkit-box",
            height: "40px",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {specialization}
        </Typography>

        <Box
          sx={{
            width:"fit",
            display: "inline-flex",
            alignItems:"center",
            gap:5,
            p: 1,
            // mb: 1,
            // backgroundColor: "grey.50",
            borderRadius: "12px",
          }}
        >
          {experience ? <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Work sx={{ color: "primary.main", fontSize: 20 }} />
            <Box>
              <Typography
                variant="caption"
                sx={{ color: "rgb(75, 75, 75)", display: "block" }}
              >
                Experience
              </Typography>
              <Typography
                variant="body2"
                sx={{ fontWeight: 600, color: "#1a1a1a" }}
              >
                {experience} Years
              </Typography>
            </Box>
          </Box> : ""}
          

          {consultationFee ? <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <CurrencyRupee sx={{ color: "primary.main", fontSize: 20 }} />
            <Box>
              <Typography
                variant="caption"
                sx={{ color: "rgb(75, 75, 75)", display: "block" }}
              >
                Consultation
              </Typography>
              <Typography
                variant="body2"
                sx={{ fontWeight: 600, color: "#1a1a1a" }}
              >
                ₹{consultationFee }
                
              </Typography>
            </Box>
          </Box> : ""}
        </Box>

        {/* <Box
          sx={{
            textAlign: "center",
          }}
        >
          <Button
          onClick={handleDoctorClick}
            color="primary"
            sx={{
              borderRadius: "100px",
              backgroundColor: "primary.main",
              transform: "scale(1)",
              color: "text.primary",
              textTransform: "capitalize",
              fontWeight: "normal",
              fontSize: "14px",
              padding: "10px 20px",
              transition: "transform 0.3s ease",
              "&:hover": {
                backgroundColor: "primary.main",
              },
            }}
          >
            Book Appointment
          </Button>
        </Box> */}
      </Box>
    </Box>
  );
};

export default DoctorCard;
