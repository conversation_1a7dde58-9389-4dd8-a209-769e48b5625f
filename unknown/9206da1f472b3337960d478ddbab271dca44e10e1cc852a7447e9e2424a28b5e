"use client"
import {useRouter} from "next/navigation";
import {useEffect} from "react";

const HealthTipsUrlReplacer = ({ seoSlug, code }) => {
    const router = useRouter();
    useEffect(() => {
        // If doctorCode doesn't match seoSlug, redirect to the correct slug
        if (code !== seoSlug) {
            router.replace(`/health-tips/${seoSlug}`);
        }
    }, [code, seoSlug, router]);

    return null
};

export default HealthTipsUrlReplacer;