import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import Box from "@mui/material/Box";

const ImageCard = ({ code, imgUrl }) => {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <React.Fragment>
      <div key={code} onClick={handleClickOpen} style={{ cursor: "pointer" }}>
        <div
          style={{
            display: "flex",
            padding: "8px",
            justifyContent: "center",
            height: "300px",
          }}
        >
          <img
            alt="slider1"
            src={imgUrl}
            style={{
              height: "100%",
              width: "100%",
              objectFit: "cover",
              borderRadius: "8px",
            }}
          />
        </div>
      </div>
        <Dialog
            open={open}
            onClose={handleClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            sx={{
                ".MuiDialog-paper": {
                    maxHeight: "calc(100vh - 64px)",
                    maxWidth: "calc(100vw - 64px)",
                    padding: 0,
                    borderRadius: "8px",
                    overflow: "hidden",
                    display: "flex", // Center content in the dialog
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "rgba(0, 0, 0, 0.5)", // Optional for better contrast
                },
            }}
        >
            <Box
                sx={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    overflow: "auto", // Allow scrolling if the image is still too large
                }}
            >
                <img
                    alt="slider1"
                    src={imgUrl}
                    style={{
                        maxHeight: "calc(100vh - 128px)", // Ensure it doesn't exceed dialog height
                        maxWidth: "calc(100vw - 128px)", // Ensure it doesn't exceed dialog width
                        width: "auto",
                        height: "auto",
                        objectFit: "contain",
                        objectPosition: "center",
                    }}
                />
            </Box>
        </Dialog>

    </React.Fragment>
  );
};

export default ImageCard;
