"use client";

import { useTheme } from "@emotion/react";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import MedicationIcon from "@mui/icons-material/Medication";
import { alpha } from "@mui/material/styles";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { getThumborUrl } from "../../utils/getThumborUrl";
import OutlinedButton from "../styledComponents/OutlinedButton";
import PrimaryButton from "../styledComponents/PrimaryButton";
import Slide from "@mui/material/Slide";
import { forwardRef, useEffect, useState } from "react";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const SpecialityCard = ({
  speciality = {},
  id = null,
  centers = [],
  selectedLocation = null,
  chainCode = null,
}) => {
  const theme = useTheme();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [selectedCenter, setSelectedCenter] = useState(null);
  const {
    displayName: specialityName = "",
    shortDescription = "",
    iconUrl = "",
    code: specialityCode = null,
    // seoSlug = "",
  } = speciality || {};
  const { domain_slug: centerSlug = "", enterpriseSpecialityDetails = {} } = selectedCenter || {};
  const { seoSlug = "" } = enterpriseSpecialityDetails || {};

  const handleCentersModal = () => setOpen(false);

  const handleCenterChange = (event) => {
    const value = event.target.value;
    const selectedValue = centers.find(
      (center) => center.code === value
    );
    setSelectedCenter(selectedValue);
  };

  const handleSpecialityClick = () => {
    if (selectedLocation && selectedLocation !== chainCode)
      router.push(`/specialities/${centerSlug}/${seoSlug}`);
    else setOpen(true);
  };

  const handleRedirection = () => {
    router.push(`/specialities/${centerSlug}/${seoSlug}`);
  };

  useEffect(() => {
    if (centers.length > 0) {
      setSelectedCenter(centers[0]);
    }
  }, [centers]);

  return (
    <>
      <Box
        id={id}
        sx={{
          padding: "16px 24px",
          boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          alignItems: "center",
          borderRadius: "12px",
          cursor: "pointer",
        }}
        onClick={handleSpecialityClick}
      >
        <Box
          sx={{
            height: "64px",
            width: "64px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: theme.palette.primary.main,
            borderRadius: "50%",
          }}
        >
          {iconUrl ? (
            <Image
              alt="speciality"
              height={35}
              width={35}
              src={getThumborUrl(iconUrl, 35, 35)}
            />
          ) : (
            <MedicationIcon fontSize="large" sx={{ color: "#fff" }} />
          )}
        </Box>
        <Box>
          <Typography align="center" variant="h6" sx={{ fontSize: "18px" }}>
            {specialityName || ""}
          </Typography>
          <Typography
            variant="subtitle1"
            align="center"
            sx={{ fontSize: "14px", color: "rgba(0, 0, 0, 0.6)" }}
          >
            {shortDescription || ""}
          </Typography>
        </Box>
      </Box>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        onClose={handleCentersModal}
        keepMounted={false}
        aria-describedby="alert-dialog-slide-description"
        sx={{ ".MuiDialog-paper": { width: { xs: "100%", sm: "600px" } } }}
      >
        <DialogTitle>Select Location</DialogTitle>
        <DialogContent>
          <FormControl sx={{ padding: "8px 16px" }}>
            <RadioGroup
              aria-labelledby="demo-radio-buttons-group-label"
              name="radio-buttons-group"
              value={selectedCenter?.code}
              onChange={handleCenterChange}
            >
              {centers.map((center) => {
                const { code = null, name = "", area = {} } = center || {};
                const { name: areaName = "" } = area || {};
                return (
                  <FormControlLabel
                    value={code}
                    control={<Radio />}
                    label={`${name || ""} - ${areaName || ""}`}
                  />
                );
              })}
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <OutlinedButton onClick={handleCentersModal}>Close</OutlinedButton>
          <PrimaryButton onClick={handleRedirection}>Confirm</PrimaryButton>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SpecialityCard;
