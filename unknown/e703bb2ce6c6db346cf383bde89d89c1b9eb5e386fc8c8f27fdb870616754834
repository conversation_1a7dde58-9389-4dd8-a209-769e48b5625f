import Box from "@mui/material/Box";
import {
  Grid,
  Typography,
  Container,
} from "@mui/material";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_REVIEWS,
} from "@/constants";
import SectionLayout from "@/app/labssinglehospital1/components/SectionLayout";
import { getEnterpriseCode } from "../blogs/location/[locationCode]/[blogCode]/layout";
import ReviewCard from "../components/ReviewCard";

const getReviews = async (enterpriseCode) => {
  if (!enterpriseCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}?list=true`;
  try {
    const response = await fetch(url, {
      cache: "no-store"
    });
    const jsonResult = await response.json();
    const { result: reviews = {} } = jsonResult || {};
    return reviews;
  } catch (error) {
    console.log("getReviewsError", error);
  }
};

export default async function Reviews() {
  const enterpriseCode = await getEnterpriseCode();
  const testimonials = await getReviews(enterpriseCode) || [];

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" , py:2 }}>
      
        <Container maxWidth="xl" >
          <Typography variant="h5" fontWeight="bold" color="text.black" sx={{py:"16px"}}>Patient Reviews</Typography>
          <Grid container spacing={3}>
          {testimonials.map((testimonial, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
              <ReviewCard testimonial={testimonial} disableNavigation={true} />
            </Grid>
          ))}
        </Grid>
        </Container>
      {/* <FaqsSection /> */}
    </Box>
  );
}
