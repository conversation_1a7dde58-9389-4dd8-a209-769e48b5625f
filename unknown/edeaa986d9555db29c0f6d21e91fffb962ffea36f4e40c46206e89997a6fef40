"use client";
import {
  Box,
  Typography,
  Grid,
  alpha,
  useTheme,
  Button,
  Container,
} from "@mui/material";
import Image from "next/image";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import { useState } from "react";

export default function Services({ services, headings = [] }) {
  const heading = headings[0]?.heading;
  // const subHeading = headings[0]?.subHeading
  const [selectedService, setSelectedService] = useState({});
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  const closeModal = () => setOpen(false);
  const openModal = (service) => {
    setSelectedService(service);
    setOpen(true);
  };
  if (!services || services.length === 0) {
    return null;
  }

  return (
    <Container
      maxWidth="lg"
      sx={{py:{ xs:"32px", sm:"32px", md:"40px"}, backgroundColor: "white", textAlign: "center" }}
    >
      <Typography
        variant="h2"
        sx={{
          fontWeight: 400,
          fontSize: {
            xs: "24px",
            md: "36px",
          },
          color: "text.black",
        }}
        my={2}
      >
        {heading ||
          "Why Choose Our Clinic For Skin, Hair & Aesthetic Treatments"}
      </Typography>

      <Grid container spacing={2} justifyContent="center" mt={1}>
        {services.map((item, index) => {
          const { service = {} } = item || {};
          const {
            name: serviceName = null,
            short_description: serviceDescription = null,
            image_url: serviceImg = "",
          } = service || {};
          return (
            <Grid item xs={6} sm={6} md={3} key={index}>
              <Box
                sx={{
                  position: "relative",
                  overflow: "hidden",
                  cursor: "pointer",
                  borderRadius: "10px",
                  backgroundColor:alpha( theme.palette.primary.main,0.1),
                  transition: "all 0.3s ease-in-out",
                  "&:hover .overlay": { top: 0 },
                }}
                onClick={() => openModal(service)}
              >
                {/* Hover Overlay */}
                <Box
                  className="overlay"
                  sx={{
                    position: "absolute",
                    top: "-100%",
                    left: 0,
                    width: "100%",
                    height: "100%",
                    backgroundColor: alpha(theme.palette.primary.main, 0.05),
                    transition: "top 0.3s ease-in-out",
                    zIndex: 2,
                    pointerEvents: "none",
                  }}
                />

                {/* Card Content */}
                <Box
                  sx={{
                    // p: 3,
                    p:{
                      xs:2,
                      sm:2,
                      md:3
                    },
                    textAlign: "center",
                    borderRadius: "10px",
                    position: "relative",
                    zIndex: 1,
                  }}
                >
                  <Box
                    sx={{ display: "flex", justifyContent: "center", mb: 2 }}
                  >
                    <Image
                      src={serviceImg}
                      alt={serviceName}
                      width={52}
                      height={52}
                      style={{
                        padding: 4,
                        borderRadius: "20%",
                        backgroundColor: alpha(theme.palette.primary.main, 0.2),
                      }} // Icon border
                    />
                  </Box>

                  <Typography
                   sx={{
                    height: { xs: "40px", sm: "auto" }, 
                    lineHeight: "1",
                  }}
                    variant="h6"
                    fontWeight="bold"
                    fontSize="18px"
                    color="text.black"
                    mb={1}
                  >
                    {serviceName}
                  </Typography>

                  <Typography
                    variant="body2"
                    color="text.black"
                    fontSize="14px"
                    sx={{
                      display: "-webkit-box",
                      WebkitBoxOrient: "vertical",
                      WebkitLineClamp: 2,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {serviceDescription}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          );
        })}
      </Grid>
      {/* Modal for Full Description */}
      <Dialog open={open} onClose={closeModal}>
        <DialogTitle sx={{color:"text.black"}}>{selectedService.name}</DialogTitle>
        <DialogContent>
          <Typography sx={{color:"text.black"}} variant="body1">
            {selectedService.short_description}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeModal} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
