"use client";

import { useContext } from "react";
import { AppContext } from "../../AppContextLayout";
import parse from "html-react-parser";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

export default function AboutUs() {
  const { websiteData } = useContext(AppContext);
  const { about_us: aboutUs = "" } = websiteData || {};
  return <SectionLayoutSingleSpecialitySingleHospital><div style={{paddingTop:"120px"}}
      className="ck-content"
      dangerouslySetInnerHTML={{__html: aboutUs}} // Render HTML safely
  /></SectionLayoutSingleSpecialitySingleHospital>;
}
