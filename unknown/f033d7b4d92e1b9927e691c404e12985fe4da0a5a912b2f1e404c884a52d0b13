"use client";

import { Box, Typography, useTheme, alpha, useMediaQuery } from "@mui/material";
import Link from "next/link";
import { styled } from "@mui/material/styles";

const RelatedLinkItem = styled(Link)(({ theme, isMobile }) => ({
  color: theme.palette.primary.main,
  textDecoration: "none",
  padding: isMobile ? "6px 12px" : "8px 16px",
  borderRadius: isMobile ? "6px" : "8px",
  background: alpha(theme.palette.primary.main, 0.05),
  transition: "all 0.3s ease",
  marginRight: isMobile ? "0" : "12px",
  marginBottom: isMobile ? "8px" : "12px",
  display: isMobile ? "block" : "inline-block",
  width: isMobile ? "100%" : "auto",
  fontWeight: 500,
  fontSize: isMobile ? "0.8rem" : "0.95rem",
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  "&:hover": {
    background: alpha(theme.palette.primary.main, 0.1),
    transform: isMobile ? "translateY(-2px)" : "translateY(-3px)",
    boxShadow: "0 4px 10px rgba(0,0,0,0.05)",
  },
}));

const RelatedLinks = ({
  links = [],
  basePath = "",
  title = "Related Links",
  containerSx = {},
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // Filter out links without slug and prepare display data
  const validLinks = links
    .filter((link) => link.slug)
    .map((link) => ({
      ...link,
      displayName: link.name || link.slug, // Use slug as fallback if name is null or empty
    }));

  if (!validLinks || validLinks.length === 0) return null;

  return (
    <Box
      sx={{
        marginTop: isMobile ? "24px" : "32px",
        padding: isMobile ? "16px" : "24px",
        borderRadius: isMobile ? "10px" : "12px",
        background: alpha(theme.palette.primary.main, 0.02),
        border: `1px solid ${alpha(theme.palette.primary.main, 0.08)}`,
        ...containerSx,
      }}
    >
      <Typography
        variant="h6"
        sx={{
          marginBottom: isMobile ? "12px" : "16px",
          fontWeight: 600,
          color: "black",
          position: "relative",
          paddingBottom: "8px",
          fontSize: isMobile ? "1rem" : "inherit",
          "&:after": {
            content: '""',
            position: "absolute",
            bottom: 0,
            left: 0,
            width: isMobile ? "30px" : "40px",
            height: isMobile ? "2px" : "3px",
            background: theme.palette.primary.main,
            borderRadius: "2px",
          },
        }}
      >
        {title}
      </Typography>

      {isMobile ? (
        <Box
          sx={{
            maxHeight: "230px", // Fixed height for mobile
            overflowY: "auto",
            WebkitOverflowScrolling: "touch",
            scrollbarWidth: "thin",
            "&::-webkit-scrollbar": {
              width: "4px",
            },
            "&::-webkit-scrollbar-track": {
              background: alpha(theme.palette.primary.main, 0.05),
              borderRadius: "10px",
            },
            "&::-webkit-scrollbar-thumb": {
              background: alpha(theme.palette.primary.main, 0.2),
              borderRadius: "10px",
            },
            paddingRight: "10px", // Provide space for scrollbar
            "-webkit-tap-highlight-color": "transparent",
          }}
        >
          {validLinks.map((link, index) => (
            <RelatedLinkItem
              key={index}
              href={`${basePath}/${link.slug}`}
              isMobile={isMobile}
            >
              {link.displayName}
            </RelatedLinkItem>
          ))}
        </Box>
      ) : (
        <Box sx={{ display: "flex", flexWrap: "wrap" }}>
          {validLinks.map((link, index) => (
            <RelatedLinkItem
              key={index}
              href={`${basePath}/${link.slug}`}
              isMobile={isMobile}
            >
              {link.displayName}
            </RelatedLinkItem>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default RelatedLinks;
