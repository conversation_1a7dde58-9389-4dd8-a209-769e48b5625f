"use client";

import { useState } from "react";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import Menu from "@mui/material/Menu";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import NavbarDropdownItem from "./navbarDropdownItem";
import { useRouter } from "next/navigation";
import { useTheme } from "@mui/material/styles"


const NavbarDropdown = ({ navbarItem = {}, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const theme = useTheme()

  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const {
    displayName = "",
    redirection = {},
    iconUrl = null,
    sections = [],
    type = 1,
  } = navbarItem || {};

  const handleClick = (event) => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <li style={{ cursor: "pointer", position: "relative", listStyle: "none" }}>
    <Box
      sx={{
        fontSize: "inherit",
        display: "flex",
        alignItems: "center",
        gap: "4px",
        color: open ? theme.palette.primary.main : "#333",
        cursor: "pointer",
        transition: "color 0.3s ease",
        "&:hover": {
          color: theme.palette.primary.main,
          "&::after": {
            width: "100%",
          },
        },
        "&::after": {
          content: '""',
          position: "absolute",
          bottom: "-4px",
          left: 0,
          width: "0%",
          height: "2px",
          backgroundColor: theme.palette.primary.main,
          transition: "width 0.3s ease",
        },
      }}
      onClick={handleClick}
    >
      <Typography
        sx={{
          fontSize: "15px",
          fontWeight: 500,
          textTransform: "capitalize",
          letterSpacing: "0.2px",
        }}
      >
        {displayName || ""}
      </Typography>
      {sections?.length > 0 && (
        <KeyboardArrowDownIcon
          fontSize="small"
          sx={{
            transition: "transform 0.3s ease",
            transform: open ? "rotate(180deg)" : "rotate(0)",
          }}
        />
      )}
    </Box>
    <Menu
      id="basic-menu"
      anchorEl={anchorEl}
      open={open}
      onClose={handleClose}
      MenuListProps={{
        "aria-labelledby": "basic-button",
      }}
      sx={{
        mt: "25px",
        "& .MuiPaper-root": {
          backgroundColor: "#000",
          borderRadius: "0px 0px 8px 8px",
          borderTop: "5px solid #187EA0",
          boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
          minWidth: "220px",
        },
      }}
      transformOrigin={{ horizontal: "left", vertical: "top" }}
      anchorOrigin={{ horizontal: "left", vertical: "bottom" }}
    >
      <Box
        sx={{
          width: "100%",
          maxHeight: "400px",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {sections &&
          sections.map((section, index) => {
            const { displayName = "" } = section || {}
            return (
              <NavbarDropdownItem
                key={`${displayName}${index}`}
                section={section}
                setAnchorEl={setAnchorEl}
                isDrawerOpen={isDrawerOpen}
                handleCloseDrawer={handleCloseDrawer}
              />
            )
          })}
      </Box>
    </Menu>
  </li>
  );
};

export default NavbarDropdown;
