'use client';
import React from "react";

import { alpha, Box, Typography } from "@mui/material";
import PhoneIcon from "@mui/icons-material/Phone";
import EmailIcon from "@mui/icons-material/Email";
import useStyles from "./styles";
import FmdGoodIcon from "@mui/icons-material/FmdGood";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useTheme } from "@emotion/react";
import { formatPhoneNumber } from "@/app/utils/formatPhoneNumber";
import PoweredByText from "@/app/commoncomponents/poweredByText";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import XIcon from "@mui/icons-material/X";
// import { AppContext } from "@/app/singlespecialitymultichain2/AppContextLayoutSingleSpecialityMultiChain2";
import FacebookIcon from "@mui/icons-material/Facebook";
import InstagramIcon from "@mui/icons-material/Instagram";
import TwitterIcon from "@mui/icons-material/Twitter";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import YouTubeIcon from "@mui/icons-material/YouTube";

export const SOCIAL_LINKS = {
  1: {
    icon: FacebookOutlinedIcon,
  },
  2: {
    icon: InstagramIcon,
  },
  3: {
    icon: XIcon,
  },
  4: {
    icon: LinkedInIcon,
  },
  5: {
    icon: YouTubeIcon,
  },
};

export const SOCIAL_LINKS_ICONS = {
  1: {
    icon: FacebookIcon,
  },
  2: {
    icon: InstagramIcon,
  },
  3: {
    icon: TwitterIcon,
  },
  4: {
    icon: LinkedInIcon,
  },
  5: {
    icon: YouTubeIcon,
  },
};

const Footer = ({ websiteData = {} }) => {
  const classes = useStyles();
  const router = useRouter();
  const theme = useTheme();
  // const { websiteData = {}, mobileView } = useContext(AppContext);

  if (!websiteData) return null;
  const {
    addresses = [],
    emails = [],
    socialMediaLinks = [],
    phoneNumbers = [],
    centers = [],
  } = websiteData || {};

  const handleCall = (phone) => {
    const phoneNo = formatPhoneNumber(phone);
    window.open(`tel:${phoneNo}`, "_blank");
  };

  const handleMailClick = (email) => {
    const url = `mailto:${email}`;
    window.open(url, "_blank");
  };

  return (
    <Box
      sx={{
        marginBottom: { xs: "56px", md: "0px" },
      }}
    >
      <Box
        className={`${classes.layoutPadding}`}
        sx={{
          backgroundColor: "#dbdbdb9e",
          paddingTop: "64px",
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "repeat(3, 1fr)" },
          gap: { xs: "2rem", md: "6rem" },
        }}
      >
        <Box
        // className={`${classes.footerGridItem} ${
        //   mobileView ? classes.footerGridItemMWeb : ""
        // }`}
        >
          <Typography variant="h6" className={classes.footerGridItemHeading}>
            Contact us
          </Typography>
          <Box className={classes.footerGridItem}>
            {addresses.length > 0 && (
              <Box className={classes.footerGridItemContentBox}>
                {addresses.map((address, index) => {
                  const {
                    line1 = "",
                    line2 = "",
                    city = "",
                    country = "",
                    map_url = "",
                  } = address || {};
                  const addressString = `${line1 || ""} ${line2 || ""}, ${city || ""}, ${country || ""}`;
                  const mapUrl = map_url
                    ? map_url
                    : `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;

                  return (
                    <Box key={index} sx={{ display: "flex", gap: "8px" }}>
                      <FmdGoodIcon
                        sx={{ color: "primary.main" }}
                        fontSize="small"
                      />
                      {mapUrl ? ( // Render link only if map_url exists
                        <Typography
                          variant="body1"
                          sx={{
                            color: "#333",
                            fontSize: "14px",
                            "&:hover": {
                              color: "primary.main",
                            },
                            cursor: "pointer",
                          }}
                          component="a" // Use <a> element for link
                          href={mapUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {addressString}
                        </Typography>
                      ) : (
                        <Typography
                          variant="body1"
                          sx={{
                            color: "#333",
                            "&:hover": {
                              color: "primary.main",
                            },
                            fontSize: "14px",
                            cursor: "default",
                          }}
                        >
                          {addressString}
                        </Typography>
                      )}
                    </Box>
                  );
                })}
              </Box>
            )}
            <Box className={classes.footerGridItemContentBox}>
              {phoneNumbers.map((number, index) => {
                const { phone = "" } = number || {};
                return (
                  <Typography
                    id={`footerPhone${index}`}
                    key={index}
                    variant="subtitle1"
                    className={classes.footerGridItemText}
                    onClick={() => handleCall(phone)}
                  >
                    <PhoneIcon className={classes.footerGridItemAddressIcon} />
                    {phone || ""}
                  </Typography>
                );
              })}
            </Box>
            {emails.length > 0 && (
              <Box className={classes.footerGridItemContentBox}>
                {emails.map((item, index) => {
                  const { email = "" } = item || {};
                  return (
                    <Typography
                      id={`footerMail${index}`}
                      key={index}
                      variant="subtitle1"
                      className={classes.footerGridItemText}
                      onClick={() => handleMailClick(email)}
                    >
                      <EmailIcon
                        className={classes.footerGridItemAddressIcon}
                      />
                      {email || ""}
                    </Typography>
                  );
                })}
              </Box>
            )}
          </Box>
        </Box>
        <Box
        // className={`${classes.footerGridItem} ${
        //   mobileView ? classes.footerGridItemMWeb : ""
        // }`}
        >
          <Typography variant="h6" className={classes.footerGridItemHeading}>
            Quick Links
          </Typography>
          <Box className={classes.footerGridItemContentBox}>
            <Link
              id="footerAboutUs"
              className={classes.footerGridItemText}
              href="/about-us"
            >
              <Typography fontSize="14px">About Us</Typography>
            </Link>
            <Link
              id="footerServices"
              className={classes.footerGridItemText}
              href="/#services"
            >
              <Typography fontSize="14px">Services</Typography>
            </Link>
            <Link
              id="footerReviews"
              className={classes.footerGridItemText}
              href="/reviews"
            >
              <Typography fontSize="14px">Reviews</Typography>
            </Link>
            <Link
              id="footerGallery"
              className={classes.footerGridItemText}
              href="/gallery"
            >
              <Typography fontSize="14px">Gallery</Typography>
            </Link>
            <Link
              id="footerBlogs"
              className={classes.footerGridItemText}
              href="/blogs"
            >
              <Typography fontSize="14px">Blogs</Typography>
            </Link>
            <Link
              id="footerPrivacyPolicy"
              href="/privacy-policy"
              className={classes.footerGridItemText}
            >
              <Typography fontSize="14px">Privacy Policy</Typography>
            </Link>
            <Link
              id="footerDisclaimer"
              href="/disclaimer"
              className={classes.footerGridItemText}
            >
              <Typography fontSize="14px">Disclaimer</Typography>
            </Link>
            <Link
              id="footerTnC"
              href="/terms-and-conditions"
              className={classes.footerGridItemText}
            >
              <Typography fontSize="14px">Terms and Conditions</Typography>
            </Link>
          </Box>
        </Box>
        {socialMediaLinks.length > 0 && (
          <Box
          // className={`${classes.footerGridItem} ${
          //   mobileView ? classes.footerGridItemMWeb : ""
          // }`}
          >
            <Typography variant="h6" className={classes.footerGridItemHeading}>
              Follow us
            </Typography>
            <Box
              className={classes.footerGridItemSocialMedia}
              sx={{ flexDirection: { xs: "row", md: "row" } }}
            >
              {socialMediaLinks.map((mediaLink) => {
                const { link = "", type = null } = mediaLink || {};
                const Icon = SOCIAL_LINKS_ICONS[type]?.icon;
                if (!link) return <></>;
                else if (type === 3)
                  return (
                    <Box
                      id={`footerSocialMediaLink${type}`}
                      onClick={() => window.open(link, "_blank")}
                      style={{ cursor: "pointer" }}
                      className={classes.twitterIcon}
                    ></Box>
                  );
                return (
                  <Icon
                    onClick={() => window.open(link, "_blank")}
                    className={classes.footerGridItemAddressIcon}
                    style={{ fontSize: "2rem", cursor: "pointer" }}
                  />
                );
              })}
            </Box>
            <Box sx={{ display: "flex", flexDirection: "column", gap: "24px" }}>
              <Typography
                variant="h6"
                className={classes.footerGridItemHeading}
              >
                Locations
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "8px",
                  color: "#fff",
                }}
              >
                {centers.map((center) => {
                  const {
                    code = null,
                    name = "",
                    phone = "",
                    domain_slug: slugTitle,
                    area,
                  } = center || {};
                  const { name: areaName } = area || {};
                  return (
                    <Link
                      id="footerFindADoctor"
                      key={code}
                      className={classes.footerGridItemText}
                      href={`/locations/${slugTitle}`}
                    >
                      <Typography fontSize="14px">
                        {`${name || ""} - ${areaName || ""}`}
                      </Typography>
                    </Link>
                  );
                })}
              </Box>
            </Box>
          </Box>
        )}
      </Box>
      <PoweredByText />
    </Box>
  );
};

export default Footer;
