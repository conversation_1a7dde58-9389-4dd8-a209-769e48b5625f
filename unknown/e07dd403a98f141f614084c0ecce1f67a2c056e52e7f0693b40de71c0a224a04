'use client';

import React from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Avatar,
  Fade
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';

const SuccessStep = ({
  phone,
  dialCode,
  onSuccess,
  onClose,
  theme,
  formatPhoneNumber
}) => {
  return (
    <Fade in={true} timeout={800}>
      <Box>
        {/* Success Icon and Animation */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box
            sx={{
              position: 'relative',
              width: 80,
              height: 80,
              margin: '0 auto 16px',
            }}
          >
            {/* Inner circle with icon */}
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: `${theme.palette.success.main}15`,
                color: theme.palette.success.main,
                border: '2px solid',
                borderColor: theme.palette.success.main,
                boxShadow: `0 0 10px ${theme.palette.success.main}30`,
              }}
            >
              <CheckCircleIcon sx={{ fontSize: 40 }} />
            </Avatar>
          </Box>

          <Typography
            variant="h5"
            sx={{
              fontWeight: 700,
              mb: 1,
              color: theme.palette.success.main
            }}
          >
            Booking Successful
          </Typography>

          <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 300, mx: 'auto', mb: 1 }}>
            Your items have been booked successfully
          </Typography>
        </Box>

        {/* Confirmation Details */}
        <Box
          elevation={0}
          sx={{
            p: 2,
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'divider',
            '&:hover': {
              borderColor: 'text.black'
            },
            bgcolor: 'background.paper',
            mb: 3
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                Phone Number
              </Typography>
              <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.black' }}>
                {dialCode} {formatPhoneNumber(phone)}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Box
                sx={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  bgcolor: theme.palette.success.main
                }}
              />
              <Typography variant="body2" sx={{ fontWeight: 600, color: theme.palette.success.main }}>
                Confirmed
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Next Steps */}
        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mb: 2, display: 'block' }}>
          We'll get in touch with you shortly with details about your booking.
        </Typography>

        {/* Close Button */}
        <Button
          variant="contained"
          fullWidth
          onClick={() => {
            if (onSuccess) {
              onSuccess();
            }
            onClose();
          }}
          sx={{
            py: 1,
            borderRadius: 2,
            fontSize: '0.9rem',
            fontWeight: 600,
            textTransform: 'none',
            bgcolor: theme.palette.success.main,
            '&:hover': {
              bgcolor: theme.palette.success.dark,
              boxShadow: `0 4px 8px ${theme.palette.success.main}30`
            }
          }}
        >
          Back to Home
        </Button>
      </Box>
    </Fade>
  );
};

export default SuccessStep;
