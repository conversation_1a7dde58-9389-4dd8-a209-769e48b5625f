import { Box, Container, Typography } from '@mui/material'
import React from 'react'
import BlogsClient from '../components/BlogsClient'

const LocationBlogsWrapper = ({blogs, heading = ""}) => {
  return (
    <Container maxWidth="lg" sx={{ py: "32px" }}>
      <Box sx={{ mb: 0, textAlign: "center" }}>
        <Typography
          variant="h4"
          align="center"
          fontWeight={400}
          sx={{
            mb: 2,
            fontSize: {
              xs: "24px",
              md: "36px",
            },
            color: "text.black",
          }}
        >
          {heading || "Latest Articles & Blogs"}
        </Typography>
      </Box>
      <BlogsClient blogs={blogs} />
    </Container>
  )
}

export default LocationBlogsWrapper