"use client";

import {ArrowRight} from "@mui/icons-material";
import {
    Box,
    Typography,
    CardContent,
    CardActions,
} from "@mui/material";
import {styled} from "@mui/material/styles";
import {useRouter} from "next/navigation";

const StyledBox = styled(Box)(({theme}) => ({
    cursor: "pointer",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    transition: "transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out",
    "&:hover": {
        transform: "scale(1.01)",
        boxShadow: theme.shadows[8],
        "& .arrow-icon": {
            transform: "translateX(4px)",
        },
        "& .read-more-text": {
            color: theme.palette.primary.main,
        },
    },
    borderRadius: theme.spacing(2),
    overflow: "hidden",
    backgroundColor: "rgba(205, 205, 205, 0.14)",
}));

const StyledMedia = styled(Box)(({theme}) => ({
    cursor: "pointer",
    paddingTop: "60%",
    position: "relative",
    backgroundSize: "cover",
    backgroundPosition: "center",
    "&::after": {
        content: '""',
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        height: "30%",
        background:
            "linear-gradient(to top, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%)",
    },
}));

const StyledButton = styled(Typography)(({theme}) => ({
    display: "flex",
    alignItems: "center",
    gap: "8px",
    cursor: "pointer",
    transition: "all 0.3s ease",
    "& .read-more-text": {
        fontSize: "0.875rem",
        transition: "color 0.3s ease",
        color: "#1a1a1a",
    },
    "& .arrow-icon": {
        fontSize: "1.2rem",
        transition: "transform 0.3s ease",
        color: "#1a1a1a",
    },
}));

const BlogCard = ({blog = []}) => {
  const {
    code = null,
    content = "",
    title = "",
    imageUrl = "",
    seoSlug
  } = blog;
  // console.log(blog)

  const router = useRouter()

  return (
      <Box  onClick={() => {
        if(seoSlug) router.push(`/blogs/${seoSlug}`)
      }}>
        <StyledBox>

          <StyledMedia
              sx={{
                backgroundImage: `url(${imageUrl || "/blogs-default.avif"})`,
              }}
          />
          <CardContent sx={{flexGrow: 1, px: 2}}>
            <Typography
                variant="h5"
                component="h3"
                sx={{
                  color: "#1a1a1a",
                  lineHeight: 1.3,
                }}
            >
              {title || ""}
            </Typography>
          </CardContent>
          <CardActions sx={{p: 2, pt: 0}}>
            <StyledButton>
              <span className="read-more-text">Continue Reading</span>
              <ArrowRight className="arrow-icon"/>
            </StyledButton>
          </CardActions>
        </StyledBox>
      </Box>)
      ;
}
export default BlogCard;
