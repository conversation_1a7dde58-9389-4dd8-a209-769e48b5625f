* {
  box-sizing: border-box;
  /* padding: 0;
  margin: 0; */
  scroll-behavior: smooth;
}

html,
body {
  max-width: 100vw;
  /* overflow-x: hidden; */
  padding: 0;
  margin: 0;
}
lite-youtube {
  max-width: none !important; /* Remove the 720px limit */
  width: 100% !important; /* Ensure full width */
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@font-face {
  font-family: "Roboto";
  src: url("./fonts/roboto-v32-latin-regular.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Inter";
  src: url("./fonts/inter-v18-latin-regular.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}
.patterned-section {
  position: relative;
  background-color: #3479c70a;
}

.patterned-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background-image: 
    linear-gradient(to right, #3479c7 1px, transparent 1px),
    linear-gradient(to bottom, #3479c7 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.12;
  pointer-events: none;
}
/* .card-hidden {
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
} */

/* .fade-in {
  opacity: 1;
  transform: translateY(0);
} */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-hidden {
  opacity: 1;
}

.fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

/* Add Swiper custom styles */
.swiper-pagination-bullet {
  background: #000 !important;
  opacity: 0.2 !important;
}

.swiper-pagination-bullet-active {
  opacity: 0.8 !important;
}


.swiper-button-next,
.swiper-button-prev {
  color: #1976d2 !important;
}

.swiper-pagination-bullet-active {
  background: #1976d2 !important;
}

.ck-content img {
  max-width: 100% !important;
  height: auto;
}