import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";

export const NextArrow = (props) => {
    const { className, style, onClick } = props;
    return (
        <ArrowForwardIosIcon
            className={className}
            onClick={onClick}
            sx={{
                color: "black",
                ...style,
                "&:hover": { color: "black" },
                display: { xs: "none", sm: "block" },
            }}
        />
    );
};

export const PrevArrow = (props) => {
    const { className, style, onClick } = props;
    return (
        <ArrowBackIosIcon
            className={className}
            onClick={onClick}
            sx={{
                color: "black",
                ...style,
                "&:hover": { color: "black" },
                display: { xs: "none", sm: "block" },
            }}
        />
    );
};