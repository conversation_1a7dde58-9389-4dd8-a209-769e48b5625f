"use client";

import { useState } from "react";
import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import MenuOpenOutlinedIcon from "@mui/icons-material/MenuOpenOutlined";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import NavbarDropdown from "./navbarDropdown";
import NavbarDropdownItem from "./navbarDropdownItem";
import Link from "next/link";
import {Typography} from "@mui/material";

const NavbarMenu = ({ navbarItemsList = [] }) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  }

  return (
    <Box sx={{ display: { xs: "flex", md: "none" } }}>
      <MenuOpenOutlinedIcon
        fontSize="large"
        onClick={() => setIsDrawerOpen(true)}
      />
      <Drawer
        open={isDrawerOpen}
        anchor="right"
        onClose={() => setIsDrawerOpen(false)}
      >
        <Box sx={{ width: "100vw", padding: "8px" }}>
          <CloseIcon onClick={() => setIsDrawerOpen(false)} />
          <Box
            sx={{
              color: "primary.main",
              listStyle: "none",
              display: "flex",
              flexDirection: "column",
              gap: "24px",
              padding: "16px 24px",
            }}
          >
            {(navbarItemsList[0]?.sections || []).map((item, index) => {
              const {
                displayName = "",
                redirection = {},
                sections = null,
                type = 1,
              } = item || {};
              const { redirectionUrl = "" } = redirection || {};
              if (type === 2)
                return (
                  <li id={`navbarSection0Item${index}`} onClick={handleCloseDrawer}>
                    <Link href={redirectionUrl} target="_blank">
                        <Typography
                            fontSize="14px">
                            {displayName || ""}
                        </Typography>
                    </Link>
                  </li>
                );
              else if (sections)
                return (
                    <NavbarDropdown
                      id={`navbarSection0Item${index}`}
                      key={`${displayName}${index}`}
                      navbarItem={item}
                      isDrawerOpen={true}
                      handleCloseDrawer={handleCloseDrawer}
                    />
                );
              else
                return (
                  <li id={`navbarSection0Item${index}`} onClick={handleCloseDrawer}>
                    <Link href={redirectionUrl}>
                        <Typography
                            fontSize="14px">
                            {displayName || ""}
                        </Typography>
                    </Link>
                  </li>
                );
            })}
            {(navbarItemsList[1]?.sections || []).map((item, index) => {
              const {
                displayName = "",
                redirection = {},
                sections = null,
                type = 1,
              } = item || {};
              const { redirectionUrl = "" } = redirection || {};
              if (type === 2)
                return (
                  <li id={`navbarSection1Item${index}`} onClick={handleCloseDrawer}>
                    <Link href={redirectionUrl} target="_blank">
                        <Typography
                            fontSize="14px">
                            {displayName || ""}
                        </Typography>
                    </Link>
                  </li>
                );
              else if (sections)
                return (
                  <NavbarDropdown
                    id={`navbarSection0Item${index}`}
                    key={`${displayName}${index}`}
                    navbarItem={item}
                    isDrawerOpen={true}
                    handleCloseDrawer={handleCloseDrawer}
                  />
                );
              else
                return (
                  <li id={`navbarSection0Item${index}`} onClick={handleCloseDrawer}>
                    <Link href={redirectionUrl}>
                        <Typography
                            fontSize="14px">
                            {displayName || ""}
                        </Typography>
                    </Link>
                  </li>
                );
            })}
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
};

export default NavbarMenu;
