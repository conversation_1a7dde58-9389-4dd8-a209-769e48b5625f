import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

const SectionHeading = ({ children, ...props }) => {
  return (
    <Typography
      variant="h4"
      sx={{
        borderBottom: "1px solid #F9F5F6",
        fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2rem" },
      }}
      {...props}
    >
      <Box
        sx={{
          display: "inline-block",
          "&::after": {
            content: "''",
            width: "100%",
            borderBottom: `3px solid `,
            borderColor: "primary.main",
            display: "block",
            marginBottom: "-1px",
          },
        }}
      >
        {children}
      </Box>
    </Typography>
  );
};

export default SectionHeading;
