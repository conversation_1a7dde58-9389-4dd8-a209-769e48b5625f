"use client";

import { useState } from "react";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import Menu from "@mui/material/Menu";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import NavbarDropdownItem from "./navbarDropdownItem";
import { useRouter } from "next/navigation";

const NavbarDropdown = ({ navbarItem = {}, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const {
    displayName = "",
    redirection = {},
    iconUrl = null,
    sections = [],
    type = 1,
  } = navbarItem || {};

  const handleClick = (event) => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <li style={{ cursor: "pointer" }}>
      <Box
        sx={{
          fontSize: "inherit",
          display: "flex",
          alignItems: "center",
          gap: "4px",
          color: open ? "secondary.main" : "primary.main",
          cursor: "pointer",
          "&:hover": { color: "secondary.main" }
        }}
        onClick={handleClick}
      >
          <Typography
              fontSize="14px"
          >
              {displayName || ""}
          </Typography>
        {sections?.length > 0 && <KeyboardArrowDownIcon fontSize="small" />}
      </Box>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
        sx={{ top: "16px" }}
      >
        <Box
          sx={{
            width: "100%",
            maxHeight: "400px",
            // padding: "16px",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {sections &&
            sections.map((section, index) => {
              const { displayName = "" } = section || {};
              return (
                <NavbarDropdownItem
                  key={`${displayName}${index}`}
                  section={section}
                  setAnchorEl={setAnchorEl}
                  isDrawerOpen={isDrawerOpen}
                  handleCloseDrawer={handleCloseDrawer}
                />
              );
            })}
        </Box>
      </Menu>
    </li>
  );
};

export default NavbarDropdown;
