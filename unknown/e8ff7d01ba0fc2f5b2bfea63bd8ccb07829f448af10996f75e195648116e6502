import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { useContext } from "react";
import { AppointmentSchedulerContext } from "./appointmentScheduler";
import { parse, format } from "date-fns";

const AppointmentChange = () => {
  const {
    selectedAppointment = {},
    handleComponentDisplay,
    setSelectedAppointment,
  } = useContext(AppointmentSchedulerContext);
  const { ts: appointmentDateWithTime = "", st: startTime = "" } =
    selectedAppointment || {};
  const appointmentDate = appointmentDateWithTime.split(" ")[0] || "";
  const dateObject = new Date(appointmentDate);
  const parsedTime = parse(startTime, "HH:mm", new Date());
  const formattedStartTime = format(parsedTime, "h:mm aa");
  const formattedDated = format(dateObject, "d MMM y");

  const handleChangeAppointment = () => {
    setSelectedAppointment({});
    handleComponentDisplay(0);
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        gap: "24px",
      }}
    >
      <Typography variant="subtitle1" sx={{ fontSize: "12px" }}>
        Appointment Date/Time -{" "}
        <span style={{ fontWeight: "500" }}>{`${formattedStartTime}, ${formattedDated}`}</span>
      </Typography>
      <Typography
        variant="subtitle2"
        sx={{
          textTransform: "none",
          textDecoration: "underline",
          color: "primary.main",
          fontSize: "14px",
          cursor: "pointer",
        }}
        onClick={handleChangeAppointment}
      >
        Change
      </Typography>
    </Box>
  );
};

export default AppointmentChange;
