'use client'
import { Box, Typography, useTheme } from "@mui/material";
import React from "react";

const StarPattern = () => {
  const theme = useTheme();
  return (
    <>
      <Box
        sx={{
          position: "absolute",
          top: "0px",
          left: "0px",
          display: "grid",
          gridTemplateColumns: "repeat(4, 1fr)", // Triangle layout
          gap: "10px",
          opacity: 0.5,
        }}
      >
        {["+", "+", "+", "+", "+", "+", "+", "+", "+", "+"].map(
          (plus, index) => (
            <Typography
              key={index}
              sx={{
                color: theme.palette.primary.main,
                fontSize: "24px",
                fontWeight: "bold",
              }}
            >
              {plus}
            </Typography>
          )
        )}
      </Box>

      <Box
        sx={{
          position: "absolute",
          bottom: "0px",
          right: "0px",
          display: "grid",
          rotate: "180deg",
          gridTemplateColumns: "repeat(4, 1fr)", // Mirrored Triangle
          gap: "10px",
          opacity: 0.5,
        }}
      >
        {["+", "+", "+", "+", "+", "+", "+", "+", "+", "+"].map(
          (plus, index) => (
            <Typography
              key={index}
              sx={{
                color: theme.palette.primary.main,
                fontSize: "24px",
                fontWeight: "bold",
              }}
            >
              {plus}
            </Typography>
          )
        )}
      </Box>
    </>
  );
};

export default StarPattern;
