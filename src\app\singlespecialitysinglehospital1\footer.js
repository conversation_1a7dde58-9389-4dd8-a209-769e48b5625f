"use client";

import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Container from "@mui/material/Container";
import Grid from "@mui/material/Grid";
import Divider from "@mui/material/Divider";
import Link from "next/link";
import FmdGoodIcon from "@mui/icons-material/FmdGood";
import PhoneIcon from "@mui/icons-material/Phone";
import MailIcon from "@mui/icons-material/Mail";
import InstagramIcon from "@mui/icons-material/Instagram";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import XIcon from "@mui/icons-material/X";
import YouTubeIcon from "@mui/icons-material/YouTube";
import { useContext } from "react";
import { AppContext } from "../AppContextLayout";
import { alpha, styled } from "@mui/material/styles";
import { useTheme } from "@emotion/react";
import PoweredByText from "../commoncomponents/poweredByText";
import { getThumborUrl } from "../utils/getThumborUrl";
import { useMediaQuery } from "@mui/material";
import Image from "next/image";

export const SOCIAL_LINKS = {
  1: { icon: FacebookOutlinedIcon },
  2: { icon: InstagramIcon },
  3: { icon: XIcon },
  4: { icon: LinkedInIcon },
  5: { icon: YouTubeIcon },
};

const StyledFooter = styled(Box)(({ theme }) => ({
  position: "relative",
  marginBottom: { xs: "56px", md: 0 },
  backgroundColor: "#0F172A", // Dark blue-gray background
  backgroundImage: "linear-gradient(135deg, #0F172A 0%, #1E293B 100%)",
  color: "#fff",

  boxShadow: "0 -10px 30px rgba(0,0,0,0.1)",
  overflow: "hidden",
  "&::before": {
    content: '""',
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 20% 20%, ${alpha("#fff", 0.05)} 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, ${alpha("#fff", 0.05)} 0%, transparent 50%)
    `,
    backgroundSize: "100% 100%",
    opacity: 0.8,
    zIndex: 0,
  },
}));

const ContentWrapper = styled(Box)({
  position: "relative",
  zIndex: 1,
});

const FooterLink = styled(Link)(({ theme }) => ({
  color: "rgba(255,255,255,0.7)",
  textDecoration: "none",
  transition: "all 0.3s ease",
  display: "flex",
  alignItems: "center",
  fontSize: "0.95rem",
  fontWeight: 400,
  position: "relative",
  "&:hover": {
    color: "#FFF",
    transform: "translateX(5px)",
  },
  "&::after": {
    content: '""',
    position: "absolute",
    bottom: -2,
    left: 0,
    width: 0,
    height: 2,
    background: "linear-gradient(90deg, #38BDF8 0%, #818CF8 100%)",
    transition: "width 0.3s ease",
    borderRadius: "2px",
  },
  "&:hover::after": {
    width: "30px",
  },
}));

const SocialIcon = styled(Box)(({ theme }) => ({
  height: "45px",
  width: "45px",
  borderRadius: "14px",
  background: "rgba(255,255,255,0.1)",
  backdropFilter: "blur(10px)",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  cursor: "pointer",
  transition: "all 0.3s ease",
  border: "1px solid rgba(255,255,255,0.27)",
  "&:hover": {
    background: "rgba(255,255,255,0.15)",
    transform: "translateY(-5px)",
    boxShadow: "0 10px 20px rgba(0,0,0,0.1)",
  },
}));

// const ContactItem = styled(Box)(({ theme }) => ({
//   display: "flex",
//   alignItems: "center",
//   gap: "12px",
//   marginBottom: "16px",
//   padding: "8px 12px",
//   borderRadius: "10px",
//   transition: "all 0.3s ease",
//   background: "rgba(255,255,255,0.03)",
//   border: '1px solid rgba(255, 255, 255, 0.27)',
//   '&:hover': {
//     transform: "translateX(5px)",
//     background: "rgba(255,255,255,0.07)",
//   },
// }));

const SectionTitle = styled(Typography)(({ theme }) => ({
  color: "#fff",
  marginBottom: theme.spacing(3),
  position: "relative",
  display: "inline-block",
  paddingBottom: "10px",
  fontSize: "1.25rem",
  fontWeight: 600,
  letterSpacing: "0.5px",
  "&::after": {
    content: '""',
    position: "absolute",
    bottom: 0,
    left: 0,
    width: "40px",
    height: "3px",
    background: "linear-gradient(90deg, #38BDF8 0%, #818CF8 100%)",
    borderRadius: "2px",
  },
}));

const Footer = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const { websiteData } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, logo_url: logoUrl = "" } =
    websiteData || {};
  const {
    phoneNumbers = [],
    emails = [],
    addresses = [],
    socialMediaLinks = [],
  } = websiteData || {};

  return (
    <StyledFooter>
      <Box
        sx={{
          padding: {
            xs: "16px 30px",
            sm: "24px 30px",
            md: "32px 80px",
            lg: "32px 100px 0",
          },
        }}
      >
        <ContentWrapper>
          <Grid container spacing={6}>
            {/* Contact Information */}
            <Grid item xs={12} md={4}>
              {/* Logo Section */}
              <Link
                href="/"
                style={{
                  display: "flex",
                  alignItems: "center",
                  paddingBottom: "15px",
                }}
              >
                {logoUrl && (
                  <Image
                    alt="logo"
                    src={getThumborUrl(logoUrl) || "/placeholder.svg"}
                    width={isMobile ? 120 : 210}
                    height={isMobile ? 40 : 70}
                    style={{
                      objectFit: "contain",
                      backgroundColor: "white",
                      width: "60%",
                      padding: "5px",
                    }}
                  />
                )}
              </Link>
              <SectionTitle variant="h6">Contact Us</SectionTitle>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 2.5,
                }}
              >
                {emails.map((item, index) => (
                  <FooterLink
                    key={index}
                    component="a"
                    href={`mailto:${item.email}`}
                    sx={{ textDecoration: "none" }}
                  >
                    <MailIcon
                      sx={{ color: "rgba(255,255,255,0.7)", marginRight: 1 }}
                    />
                    <Typography sx={{ color: "#fff", fontSize: "0.9rem" }}>
                      {item.email}
                    </Typography>
                  </FooterLink>
                ))}

                {phoneNumbers.map((item, index) => (
                  <FooterLink
                    key={index}
                    component="a"
                    href={`tel:${item.phone}`}
                    sx={{ textDecoration: "none" }}
                  >
                    <PhoneIcon
                      sx={{ color: "rgba(255,255,255,0.7)", marginRight: 1 }}
                    />
                    <Typography sx={{ color: "#fff", fontSize: "0.9rem" }}>
                      {item.phone}
                    </Typography>
                  </FooterLink>
                ))}

                {addresses.map((address, index) => {
                  const addressString = `${address.line1 || ""} ${address.line2 || ""}, ${address.city || ""}, ${address.country || ""}`;
                  const mapUrl =
                    address.map_url ||
                    `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;

                  return (
                    <FooterLink
                      key={index}
                      component="a"
                      href={mapUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ textDecoration: "none" }}
                    >
                      <FmdGoodIcon
                        sx={{ color: "rgba(255,255,255,0.7)", marginRight: 1 }}
                      />
                      <Typography sx={{ color: "#fff", fontSize: "0.9rem" }}>
                        {addressString}
                      </Typography>
                    </FooterLink>
                  );
                })}
              </Box>
            </Grid>

            {/* Quick Links */}
            <Grid item xs={12} md={4}>
              <SectionTitle variant="h6">Quick Links</SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}
                  >
                    <Typography>
                      <FooterLink href="/doctors">Find a doctor</FooterLink>
                    </Typography>
                    <Typography>
                      <FooterLink href="/about-us">About us</FooterLink>
                    </Typography>
                    <Typography>
                      <FooterLink href="/blogs">Blogs</FooterLink>
                    </Typography>
                    <Typography>
                      <FooterLink href="/specialities">Specialities</FooterLink>
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}
                  >
                    <Typography>
                      <FooterLink href="/contact-us">Contact us</FooterLink>
                    </Typography>
                    <Typography>
                      <FooterLink href="/privacy-policy">
                        Privacy Policy
                      </FooterLink>
                    </Typography>
                    <Typography>
                      <FooterLink href="/disclaimer">Disclaimer</FooterLink>
                    </Typography>
                    <Typography>
                      <FooterLink href="/terms-and-conditions">
                        Terms & Conditions
                      </FooterLink>
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>

            {/* Social Media */}
            {socialMediaLinks.length > 0 && (
              <Grid item xs={12} md={3}>
                <SectionTitle variant="h6">Connect With Us</SectionTitle>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 2.5,
                    flexWrap: "wrap",
                    borderRadius: "16px",
                  }}
                >
                  {socialMediaLinks.map((item, index) => {
                    const { link = "", type = null } = item || {};
                    const Icon = SOCIAL_LINKS[type]?.icon;
                    if (!link) return null;

                    return (
                      <SocialIcon
                        key={index}
                        onClick={() => window.open(link, "_blank")}
                        id={`footerSocialMediaLink${type}`}
                      >
                        <Icon sx={{ color: "#fff" }} />
                      </SocialIcon>
                    );
                  })}
                </Box>
              </Grid>
            )}
          </Grid>

          <Divider
            sx={{
              my: 3,
              opacity: 0.2,
              borderColor: "rgba(255,255,255,0.15)",
              "&::before, &::after": {
                borderTop: "1px solid rgba(255,255,255,0.1)",
              },
            }}
          />
        </ContentWrapper>
      </Box>
      <Box sx={{position: "relative", cursor: "pointer" }}>
        <PoweredByText />
      </Box>
    </StyledFooter>
  );
};

export default Footer;
// "use client";

// import Box from "@mui/material/Box";
// import Typography from "@mui/material/Typography";
// import Image from "next/image";
// import Link from "next/link";
// import FmdGoodIcon from "@mui/icons-material/FmdGood";
// import PhoneIcon from "@mui/icons-material/Phone";
// import MailIcon from "@mui/icons-material/Mail";
// import InstagramIcon from "@mui/icons-material/Instagram";
// import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
// import LinkedInIcon from "@mui/icons-material/LinkedIn";
// import XIcon from "@mui/icons-material/X";
// import YouTubeIcon from "@mui/icons-material/YouTube";
// import { useContext } from "react";
// import { AppContext } from "../AppContextLayout";
// import { getThumborUrl } from "../utils/getThumborUrl";
// import { alpha } from "@mui/material";
// import { useTheme } from "@emotion/react";
// import PoweredByText from "../commoncomponents/poweredByText";

// export const SOCIAL_LINKS = {
//   1: {
//     icon: FacebookOutlinedIcon,
//   },
//   2: {
//     icon: InstagramIcon,
//   },
//   3: {
//     icon: XIcon,
//   },
//   4: {
//     icon: LinkedInIcon,
//   },
//   5: {
//     icon: YouTubeIcon,
//   },
// };

// const Footer = () => {
//   const { websiteData } = useContext(AppContext);
//   const theme = useTheme();
//   const {
//     phoneNumbers = [],
//     emails = [],
//     addresses = [],
//     socialMediaLinks = [],
//     logo_url: logoUrl = "",
//   } = websiteData || {};
//   return (
//     <Box sx={{ marginBottom: { xs: "56px", md: 0 } }}>
//       <Box
//         sx={{
//           bgcolor: alpha(theme.palette.primary.main, 0.8),
//           padding: { xs: "32px 16px", md: "32px 80px", lg: "32px 100px" },
//           display: "grid",
//           gridTemplateColumns: { sm: "1fr", md: "1fr 200px 1fr" },
//           gap: "32px",
//         }}
//       >
//         <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>
//           {/* <Link href="/">
//             <Image
//               alt="logo"
//               src={getThumborUrl(logoUrl)}
//               width={100}
//               height={36}
//             />
//           </Link> */}
//           <Box sx={{ display: "flex", flexDirection: "column", gap: "16px" }}>
//             {emails.map((item, index) => {
//               const { email = "" } = item || {};
//               return (
//                 <Box
//                   id={`footerMail${index}`}
//                   key={index}
//                   sx={{ display: "flex", gap: "8px", cursor: "pointer" }}
//                   onClick={() => window.open(`mailto:${email}`)}
//                 >
//                   <MailIcon sx={{ color: "#fff" }} fontSize="small" />
//                   <Typography
//                     variant="body1"
//                     sx={{ color: "#fff", fontSize: "14px" }}
//                   >
//                     {email || ""}
//                   </Typography>
//                 </Box>
//               );
//             })}
//             {phoneNumbers.map((item, index) => {
//               const { phone = "" } = item || {};
//               return (
//                 <Link key={index} id={`footerPhone${index}`} href={`tel:${phone}`}>
//                   <Box
//                     sx={{ display: "flex", gap: "8px", cursor: "pointer" }}
//                   >
//                     <PhoneIcon sx={{ color: "#fff" }} fontSize="small" />
//                     <Typography
//                       variant="body1"
//                       sx={{ color: "#fff", fontSize: "14px" }}
//                     >
//                       {phone || ""}
//                     </Typography>
//                   </Box>
//                 </Link>
//               );
//             })}
//               {addresses.map((address, index) => {
//                   const {
//                       line1 = "",
//                       line2 = "",
//                       city = "",
//                       country = "",
//                       map_url = ""
//                   } = address || {};

//                   // Construct the address string
//                   const addressString = `${line1 || ""} ${line2 || ""}, ${city || ""}, ${country || ""}`;
//                   const mapUrl = map_url
//                       ? map_url
//                       : `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;

//                   return (
//                       <Box key={index} sx={{ display: "flex", gap: "8px" }}>
//                           <FmdGoodIcon sx={{ color: "#fff" }} fontSize="small" />
//                           {mapUrl ? ( // Render link only if map_url exists
//                               <Typography
//                                   variant="body1"
//                                   sx={{ color: "#fff", fontSize: "14px", cursor: "pointer" }}
//                                   component="a" // Use <a> element for link
//                                   href={mapUrl}
//                                   target="_blank"
//                                   rel="noopener noreferrer"
//                               >
//                                   {addressString}
//                               </Typography>
//                           ) : (
//                               <Typography
//                                   variant="body1"
//                                   sx={{ color: "#fff", fontSize: "14px", cursor: "default" }}
//                               >
//                                   {addressString}
//                               </Typography>
//                           )}
//                       </Box>
//                   );
//               })}

//           </Box>
//         </Box>
//         <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>
//           <Typography variant="h6" sx={{ color: "#fff" }}>
//             Quick Links
//           </Typography>
//           <Box
//             sx={{
//               display: "flex",
//               flexDirection: "column",
//               gap: "8px",
//               color: "#fff",
//               fontSize: "14px",
//             }}
//           >
//             <Link id="footerFindADoctor" href="/doctors">
//                 <Typography
//                     fontSize="14px"
//                 >
//                     Find a doctor
//                 </Typography>
//             </Link>
//             <Link id="footerAboutUs" href="/about-us">
//                 <Typography
//                     fontSize="14px"
//                 >
//                     About us
//                 </Typography>
//             </Link>
//             <Link id="footerBlogs" href="/blogs">
//                 <Typography
//                     fontSize="14px"
//                 >
//                 Blogs
//                 </Typography>
//             </Link>
//             <Link id="footerSpecialities" href="/specialities">
//                 <Typography
//                     fontSize="14px"
//                 >
//                 Specialities
//                 </Typography>
//             </Link>
//             <Link id="footerContactUs" href="/contact-us">
//                 <Typography
//                     fontSize="14px"
//                 >
//                     Contact us

//                 </Typography>
//             </Link>
//             <Link id="footerPrivacyPolicy" href="/privacy-policy">
//                 <Typography
//                     fontSize="14px"
//                 >
//                     Privacy Policy

//                 </Typography>
//             </Link>
//             <Link id="footerDisclaimer" href="/disclaimer">
//                 <Typography
//                     fontSize="14px"
//                 >
//                     Disclaimer

//                 </Typography>
//             </Link>
//             <Link id="footerTnC" href="/terms-and-conditions">
//                 <Typography
//                     fontSize="14px"
//                 >
//                     Terms and Conditions

//                 </Typography>
//             </Link>
//           </Box>
//         </Box>
//           { socialMediaLinks.length > 0 &&
//               <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>
//                   <Typography variant="h6" sx={{ color: "#fff" }}>
//                       Stay in touch
//                   </Typography>
//                   <Box
//                       sx={{
//                           display: "flex",
//                           gap: "16px",
//                           color: "#fff",
//                           fontSize: "14px",
//                       }}
//                   >
//                       {socialMediaLinks.map((item, index) => {
//                           const { link = "", type = null } = item || {};
//                           const Icon = SOCIAL_LINKS[type]?.icon;
//                           if (!link) return <></>;
//                           return (
//                               <Box
//                                   id={`footerSocialMediaLink${type}`}
//                                   key={index}
//                                   sx={{
//                                       height: "36px",
//                                       width: "36px",
//                                       borderRadius: "100%",
//                                       background: "#fff",
//                                       display: "flex",
//                                       justifyContent: "center",
//                                       alignItems: "center",
//                                       cursor: "pointer",
//                                   }}
//                                   onClick={() => window.open(link, "_blank")}
//                               >
//                                   <Icon sx={{ color: "#000" }} />
//                               </Box>
//                           );
//                       })}
//                   </Box>
//               </Box>
//           }
//       </Box>
//         <PoweredByText />
//     </Box>
//   );
// };

// export default Footer;
