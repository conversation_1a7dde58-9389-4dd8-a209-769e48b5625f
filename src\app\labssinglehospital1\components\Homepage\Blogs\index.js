import { Box, Container, Typography } from "@mui/material";
import Link from "next/link";
import BlogsClient from "../../BlogsClient";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import {
  getHomeComponentsData,
  getHomeSectionHeadings,
} from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";

const fetchBlogs = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData(
      { domainName },
      HOME_WIDGET_TYPE.BLOGS
    );
    return data?.code === 200 ? data?.result?.blogs : [];
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return [];
  }
};

const getBlogsHeadings = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeSectionHeadings(
      { domainName: domainName },
      HOME_SECTION_HEADING_TYPE.BLOGS
    );
    if (data.code === 200) {
      return data?.result || [];
    } else return [];
  } catch (error) {
    console.error("Error fetching blogs data:", error);
    return [];
  }
};
export default async function BlogSection() {
  const blogs = await fetchBlogs();

  const headings = await getBlogsHeadings();

  const heading = headings[0]?.heading;
  //  const subHeading = headings[0]?.subHeading

  if (blogs.length === 0) {
    return <></>;
  }

  return (
    <Container maxWidth="xl" sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
          position: "relative",
        }}
      >
        <Typography variant="h2" component="h2" fontWeight="bold" color="text.black" sx={{ fontSize: { xs: '1.5rem', md: '1.8rem' } }}>
          {heading || "Latest Articles & Blogs"}
        </Typography>
        <Link href="/blogs" passHref aria-label="View all blogs">
          <Typography
            component="span"
            sx={{
              textWrap: "nowrap",
              color: "primary.main",
              cursor: "pointer",
              fontWeight: 500,
              fontSize: "0.9rem",
            }}
          >
            View All
          </Typography>
        </Link>
      </Box>
      <BlogsClient blogs={blogs} />
    </Container>
  );
}
