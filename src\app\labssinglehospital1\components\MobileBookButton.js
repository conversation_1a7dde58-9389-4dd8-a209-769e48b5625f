'use client';

import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  useMediaQuery,
  useTheme,
  Snackbar,
  Alert
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { addToCart, removeFromCart, selectIsItemInCart } from '../redux/slices/cartSlice';
import { selectIsCartOpen } from '../redux/slices/uiSlice';

const MobileBookButton = ({ testData }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const dispatch = useDispatch();

  // Get cart open state from Redux
  const isCartOpen = useSelector(selectIsCartOpen);

  // Check if this item is already in the cart (specifying it's a test)
  const isInCart = useSelector(selectIsItemInCart(testData.id, 'test'));

  const [showSnackbar, setShowSnackbar] = useState(false);

  const handleAddToCart = () => {
    // Add item to cart
    dispatch(addToCart({
      id: testData.id,
      title: testData.title,
      discountedPrice: testData.discountedPrice,
      originalPrice: testData.originalPrice,
      discount: testData.discount,
      icon: '🔬', // Default icon
      testsIncludedText: testData.testsIncludedText || `${testData.totalTests} Tests included`,
      description: testData.subtitle,
      itemType: 'test' // Specify this is a test, not a package
    }));

    // Show confirmation message
    setShowSnackbar(true);
  };

  const handleRemoveFromCart = () => {
    dispatch(removeFromCart({ id: testData.id, itemType: 'test' }));

    // Show confirmation message for removal
    setShowSnackbar(true);
  };

  const handleSnackbarClose = () => {
    setShowSnackbar(false);
  };

  // Render nothing if not on mobile or cart is open
  if (!isMobile || isCartOpen) {
    return null;
  }

  return (
    <>
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          padding: 2,
          backgroundColor: 'white',
          width: '100%',
          boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 2
        }}
      >
        <Box>
          {/* If discount percentage is null or 0, show only original price */}
          {(!testData.discountPercentage || testData.discountPercentage <= 0) ? (
            <Typography variant="subtitle1" fontWeight="bold">
              ₹{testData.originalPrice}
            </Typography>
          ) : (
            // Otherwise show discounted price with strikethrough original price
            <>
              <Typography variant="subtitle1" fontWeight="bold">
                ₹{testData.discountedPrice}
              </Typography>
              <Typography
                variant="caption"
                component="span"
                sx={{
                  textDecoration: 'line-through',
                  color: 'text.secondary',
                  mr: 0.5
                }}
              >
                ₹{testData.originalPrice}
              </Typography>
              <Typography
                variant="caption"
                component="span"
                sx={{
                  color: 'success.main',
                  fontWeight: 'medium'
                }}
              >
                {testData.discount}
              </Typography>
            </>
          )}
        </Box>

        {isInCart ? (
          <Button
            variant="outlined"
            color="error"
            onClick={handleRemoveFromCart}
            sx={{
              borderRadius: '8px',
              width: '60%',
              textTransform: 'none',
              fontWeight: 'medium',
              py: 1.5
            }}
          >
            <Typography sx={{ fontWeight: 500 }}>
              Remove
            </Typography>
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            onClick={handleAddToCart}
            sx={{
              borderRadius: '8px',
              width: '60%',
              textTransform: 'none',
              fontWeight: 'medium',
              py: 1.5
            }}
          >
            <Typography sx={{ fontWeight: 500, color: 'text.primary' }}>
              Add to Cart
            </Typography>
          </Button>
        )}
      </Box>

      {/* Success message when item is added to cart */}
      <Snackbar
        open={showSnackbar}
        autoHideDuration={1200}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity="success"
          variant="filled"
          sx={{ width: '100%' }}
        >
          {isInCart ? `${testData.title} removed from cart!` : `${testData.title} added to cart!`}
        </Alert>
      </Snackbar>
    </>
  );
};

export default MobileBookButton;
