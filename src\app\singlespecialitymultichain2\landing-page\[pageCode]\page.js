"use client";

import { useEffect, useState, useContext } from "react";
import axios from "axios";
import CustomThemeProvider from "@/CustomThemeProvider";
import Box from "@mui/material/Box";
import { getEnterpriseCode } from "@/api/enterprise.service";
import {useRouter} from "next/navigation";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_LANDING_PAGE,
} from "@/constants";
import Loader from "@/app/commoncomponents/loader";
import NotFound from "@/app/commoncomponents/NotFound";
import Navbar from "@/app/commoncomponents/landingpage/navbar";
import Faqs from "@/app/commoncomponents/landingpage/faqs";
import Carousal from "@/app/commoncomponents/landingpage/carousal";
import BannerWidget from "@/app/commoncomponents/landingpage/bannerWidget";
import Reviews from "@/app/commoncomponents/landingpage/reviews";
import Banner from "@/app/commoncomponents/landingpage/banner";
import BoxWidget from "@/app/commoncomponents/landingpage/boxWidget";
import NeedHelp from "@/app/commoncomponents/landingpage/needHelp";
import ctaStickyWidget from "@/app/commoncomponents/landingpage/ctaStickyWidget";
import Video from "@/app/commoncomponents/landingpage/video";
import ckeditorWidget from "@/app/commoncomponents/landingpage/ckeditorWidget";
import ModalForm from "@/app/commoncomponents/landingpage/dialogForm";
import { AppContext } from "@/app/AppContextLayout";
import Footer from "../../Footer";

// Define widget mapping
const WIDGET = {
  faqs: Faqs,
  carousels: Carousal,
  banners: BannerWidget,
  reviews: Reviews,
  coverPages: Banner,
  boxItems: BoxWidget,
  ctas: NeedHelp,
  stickyCtas: ctaStickyWidget,
  gallery: Video,
  ckeditorWidgets: ckeditorWidget,
};

const LandingPage = ({ params }) => {
  const [pageDetails, setPageDetails] = useState(null); // Store page details
  const [isLoading, setIsLoading] = useState(true);     // Loading state
  const [isModalOpen, setIsModalOpen] = useState(false); // Modal state
  const { pageCode = null } = params || {};
  const router = useRouter();
  const { websiteData } = useContext(AppContext);
  

  // Fetch landing page details
  useEffect(() => {
    const fetchLandingPageDetails = async () => {
      const entepriseCode = await getEnterpriseCode(); // Get enterprise code
      if (!entepriseCode) return;

      const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_LANDING_PAGE}?pageCode=${pageCode}&enterpriseCode=${entepriseCode}`;
      let slug = pageCode;

      try {
        const response = await axios.get(url);
        const { status = null, data = {} } = response || {};

        if (status >= 200 && status < 300) {
          const { result = {} } = data || {};
          const { seoSlug = "" } = result || {};

          if (pageCode !== seoSlug) {
            slug = seoSlug;
          } else {
            setPageDetails(result);
          }
        }
      } catch (error) {
        console.error("Error fetching landing page details:", error);
      }finally {
        setIsLoading(false)
      }

      if (pageCode !== slug) router.replace(`/landing-page/${slug}`);
    };

    fetchLandingPageDetails(); // Trigger fetch
  }, [pageCode]);

  if (isLoading) {
    return <Loader/>; // Show loading state
  }

  if (!isLoading && !pageDetails){
    return <NotFound/>
  }

  const { theme = {}, widgetCollections = [], enterpriseCode = null, campaignName = null, requestMappings = [] } = pageDetails || {};

  // Check if there are stickyCta widgets
  const stickyCtaData = widgetCollections.find(widget =>
      Array.isArray(widget.stickyCtas) && widget.stickyCtas.length > 0
  )?.stickyCtas || [];

  return (
    <>
      <CustomThemeProvider template={theme}>
        <Navbar details={pageDetails} />

        {/* Apply padding conditionally based on stickyCtaData */}
        <Box sx={{
          paddingBottom: stickyCtaData.length > 0 ? { xs: '80px', sm: '0px' } : '0px',
          overflowX: "hidden"
        }}>
          {widgetCollections.map((widget, index) => {
            let key = null;
            let value = null;
            const { theme = {}, collectionTitle = "" } = widget || {};
            for (let i in widget) {
              if (Array.isArray(widget[i]) && widget[i].length > 0) {
                key = i;
                value = widget[i];
              }
            }

            const Component = WIDGET[key];
            if (!key || !WIDGET[key]) return <></>;

            return <Component key={index} campaignName={campaignName} title={collectionTitle} value={value}
                              enterpriseCode={enterpriseCode} theme={theme} setIsModalOpen={setIsModalOpen}
                              productCode={pageCode} requestMappings={requestMappings}/>;
          })}

          {isModalOpen && (
              <ModalForm
                  enterpriseCode={enterpriseCode}
                  campaignName={campaignName}
                  isModalOpen={isModalOpen}
                  setIsModalOpen={setIsModalOpen}
                  productCode={pageCode}
                  requestMappings={requestMappings}
              />
          )}
        </Box>
      </CustomThemeProvider>
      <Footer websiteData={websiteData}/>
    </>
  );
};

export default LandingPage;
