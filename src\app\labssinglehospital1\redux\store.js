import { configureStore } from '@reduxjs/toolkit';
import cartReducer from './slices/cartSlice';
import uiReducer from './slices/uiSlice';
import { loadState } from './hooks/useLocalStorage';

// Create store with preloaded state from localStorage if available
export const store = configureStore({
  reducer: {
    cart: cartReducer,
    ui: uiReducer,
    // Add other reducers here as needed
  },
  // Preload state from localStorage
  preloadedState: {
    cart: loadState('cart', {
      items: [],
      totalQuantity: 0,
      totalAmount: 0,
    }),
    ui: {
      isCartOpen: false,
    },
  },
  // Enable Redux DevTools in development
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;
