"use client";

import { Box, Typography, alpha } from "@mui/material";
import SectionLayout from "../styledComponents/SectionLayout";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import CallIcon from "@mui/icons-material/Call";
import EmailIcon from "@mui/icons-material/Email";
import FmdGoodIcon from "@mui/icons-material/FmdGood";
import OutlinedButton from "../styledComponents/OutlinedButton";
import QuickEnquiry from "../../commoncomponents/quickEnquiry";
import { useContext, useEffect, useState } from "react";
import { AppContext } from "../../AppContextLayout";
import { formatPhoneNumber } from "../../utils/formatPhoneNumber";
import { isEmptyObject } from "../../utils/isEmptyObject";
import { useTheme } from "@emotion/react";
import {LEAD_SOURCES} from "@/constants";

export default function ContactUs() {
  const theme = useTheme();
  const { websiteData } = useContext(AppContext);
  const { addresses = [], emails = [], phoneNumbers = [] } = websiteData || {};
  const [activeContactInfo, setActiveContactInfo] = useState({
    primaryPhone: null,
    whatsappPhone: null,
    primaryEmail: null,
  });

  const handleSendMessage = () => {
    if (activeContactInfo["whatsappPhone"]) {
      const msg = `Hi, I have an inquiry. Please assist!`;

      const encodedMessage = encodeURIComponent(msg);
      const phoneNo = formatPhoneNumber(activeContactInfo["whatsappPhone"]);
      const url = `https://wa.me/${phoneNo}/?text=${encodedMessage}`;
      window.open(url, "_blank");
    }
  };

  const handleMailClick = () => {
    if (activeContactInfo["primaryEmail"]) {
      const url = `mailto:${activeContactInfo["primaryEmail"]}`;
      window.open(url, "_blank");
    }
  };

  const handleCallUs = () => {
    if (activeContactInfo["primaryPhone"]) {
      const phoneNo = formatPhoneNumber(activeContactInfo["primaryPhone"]);
      window.open(`tel:${phoneNo}`, "_blank");
    }
  };

  useEffect(() => {
    if (!isEmptyObject(websiteData)) {
      if (phoneNumbers.length > 0) {
        const whatsappNumber =
          phoneNumbers.filter((item) => item.is_whatsapp)[0] || {};
        const primaryNumber =
          phoneNumbers.filter((item) => item.is_primary)[0] || {};
        const { phone: whatsappPhone = null } = whatsappNumber || {};
        const { phone: primaryPhone = null } = primaryNumber || {};
        setActiveContactInfo((prev) => ({
          ...prev,
          whatsappPhone,
          primaryPhone,
        }));
      }
      if (emails.length > 0) {
        const primaryEmailObj =
          emails.filter((email) => email.is_primary)[0] || {};
        const { email: primaryEmail = null } = primaryEmailObj || {};
        setActiveContactInfo((prev) => ({ ...prev, primaryEmail }));
      }
    }
  }, [websiteData]);

  return (
    <SectionLayout>
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 400px", lg: "1fr 500px" },
          gap: "48px",
        }}
      >
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
            gap: "32px",
            alignSelf: "start",
          }}
        >
          {Boolean(activeContactInfo["whatsappPhone"]) && (
            <Box
              sx={{
                padding: "20px",
                border: "2px solid #F5F5F5",
                borderRadius: "16px",
                display: "flex",
                flexDirection: "column",
                gap: "36px",
                height: "fit-content",
              }}
              onClick={handleSendMessage}
            >
              <Box
                sx={{
                  width: "fit-content",
                  padding: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  background: alpha(theme.palette.primary.main, 0.05),
                  borderRadius: "8px",
                }}
              >
                <WhatsAppIcon color="primary" />
              </Box>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                  Chat with us
                </Typography>
                <Typography
                  variant="subtitle2"
                  sx={{ color: "#9F9F9F", fontSize: "12px", fontWeight: 400 }}
                >
                  We're here to help.
                </Typography>
                <OutlinedButton
                  id="contactUsChatWithUs"
                  sx={{
                    mt: 1,
                    width: "100%",
                    textTransform: "none",
                    boxShadow: "none",
                  }}
                >
                  Chat with us
                </OutlinedButton>
              </Box>
            </Box>
          )}
          {Boolean(activeContactInfo["primaryEmail"]) && (
            <Box
              sx={{
                padding: "20px",
                border: "2px solid #F5F5F5",
                borderRadius: "16px",
                display: "flex",
                flexDirection: "column",
                gap: "36px",
                height: "fit-content",
              }}
              onClick={handleMailClick}
            >
              <Box
                sx={{
                  width: "fit-content",
                  padding: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  background: alpha(theme.palette.primary.main, 0.05),
                  borderRadius: "8px",
                }}
              >
                <EmailIcon color="primary" />
              </Box>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                  Mail us
                </Typography>
                <Typography
                  variant="subtitle2"
                  sx={{ color: "#9F9F9F", fontSize: "12px", fontWeight: 400 }}
                >
                  Reach out to us for any inquiries.
                </Typography>
                <OutlinedButton
                  id="contactUsMail"
                  sx={{
                    mt: 1,
                    width: "100%",
                    textTransform: "none",
                    boxShadow: "none",
                  }}
                >
                  Mail us
                </OutlinedButton>
              </Box>
            </Box>
          )}
          {Boolean(activeContactInfo["primaryPhone"]) && (
            <Box
              sx={{
                padding: "20px",
                border: "2px solid #F5F5F5",
                borderRadius: "16px",
                display: "flex",
                flexDirection: "column",
                gap: "36px",
                height: "fit-content",
              }}
              onClick={handleCallUs}
            >
              <Box
                sx={{
                  width: "fit-content",
                  padding: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  background: alpha(theme.palette.primary.main, 0.05),
                  borderRadius: "8px",
                }}
              >
                <CallIcon color="primary" />
              </Box>
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                  Call us
                </Typography>
                <Typography
                  variant="subtitle2"
                  sx={{ color: "#9F9F9F", fontSize: "12px", fontWeight: 400 }}
                >
                  Our lines are open.
                </Typography>
                <OutlinedButton
                  id="contactUsCall"
                  sx={{
                    mt: 1,
                    width: "100%",
                    textTransform: "none",
                    boxShadow: "none",
                  }}
                >
                  Call us
                </OutlinedButton>
              </Box>
            </Box>
          )}
        </Box>
        <Box sx={{ alignSelf: "start" }}>
          <QuickEnquiry leadSource={LEAD_SOURCES.CONTACT_US_PAGE} />
        </Box>
      </Box>
    </SectionLayout>
  );
}
