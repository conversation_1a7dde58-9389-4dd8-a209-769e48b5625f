import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import dynamic from "next/dynamic";
import { getHealthTips } from "@/api/healthtip.service";
import {getEnterpriseCode} from "@/app/hospitalchaintemplate2apollo/health-tips/[healthTipCode]/layout";
import SectionLayoutChainTemp2 from "@/app/hospitalchaintemplate2apollo/styledComponents/SectionLayoutChainTemp2";
import {LEAD_SOURCES, PAGE_NAMES} from "@/constants";

const QuickEnquiry = dynamic(
  () => import("@/app/commoncomponents/quickEnquiry"),
  {
    ssr: false,
  }
);

const HealthTipsUrlReplacer = dynamic(() => import("./HealthTipsUrlReplacer"), {
  ssr: false,
});

const HealthTipsDetail = async ({ params }) => {
  const { healthTipCode = null } = params || {};
  const enterpriseCode = await getEnterpriseCode();
  const data = await getHealthTips(enterpriseCode, { code: healthTipCode });
  const { result = []} = data;
  if (result.length === 0) return null;

  const {
    title = "",
    content = "",
    imageUrl = "",
    seoSlug = "",
  } = result[0] || {};

  return (
    <Box>
      <SectionLayoutChainTemp2>
        <Box sx={{ display: "flex", flexDirection: "column", gap: {xs: "12px", md: "24px" }}}>
          {imageUrl && (
            <Box
              sx={{
                minHeight: { xs: "240px", sm: "320px", md: "400px" },
                position: "relative",
              }}
            >
              <Image
                alt="healthtip-banner"
                fill
                sizes="(max-width: 768px) 100vw, 700px"
                priority
                src={imageUrl}
                style={{
                  height: "100%",
                  width: "100%",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  objectFit: "contain",
                  borderRadius: "10px",
                }}
              />
            </Box>
          )}
          <Typography
            variant="h3"
            align= {imageUrl ? "center" : "start"}
            sx={{
              fontWeight: "500",
              color: "primary.main",
              fontSize: { xs: "1.75rem", sm: "40px" },
            }}
          >
            {title || ""}
          </Typography>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: { xs: "1fr", lg: "1fr .5fr" },
              gap: "24px",
            }}
          >
            <div
              className="ck-content"
              dangerouslySetInnerHTML={{ __html: content }} // Render HTML safely
            />
            <Box
              sx={{
                position: "sticky",
                top: "220px", 
                alignSelf: "start", 
                marginLeft: {lg: "16px"}
              }}
            >
              {/*<QuickEnquiry leadSource={11} productCode={healthTipCode} />*/}
                <QuickEnquiry pageData={{leadSource: LEAD_SOURCES.HEALTH_TIP_PAGE, pageTitle: title,
                    enterpriseCode: enterpriseCode, productCode: healthTipCode }}/>

            </Box>
          </Box>
        </Box>
      </SectionLayoutChainTemp2>
      <HealthTipsUrlReplacer code={healthTipCode} seoSlug={seoSlug} />
    </Box>
  );
};

export default HealthTipsDetail;
