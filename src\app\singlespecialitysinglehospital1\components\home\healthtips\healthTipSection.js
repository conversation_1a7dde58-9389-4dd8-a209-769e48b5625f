"use client";

import { AppContext } from "@/app/AppContextLayout";
import HealthTipCard from "./healthTipCard";
import Slider from "react-slick";
import { Box } from "@mui/material";
import OutlinedButton from "@/app/oasis/styledComponents/OutlinedButton";
import SectionHeading from "../../sectionHeading";
import { getHealthTips } from "@/api/healthtip.service";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import SectionLayoutOasis from "@/app/oasis/styledComponents/SectionLayoutOasis";

const HealthTipSection = () => {
  const router = useRouter();

  // Step 1: Retrieve enterprise code from context
  const { websiteData } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  // Step 2: Set up state for health tips list
  const [healthTipsList, setHealthTipsList] = useState([]);

  // Step 3: Slider settings for responsiveness and behavior
  const sliderSettings = {
    dots: true,
    infinite: true,
    lazyLoad: true,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    adaptiveHeight: true,
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  // Step 4: Fetch health tips from the API
  const fetchHealthTips = async () => {
    try {
      const data = await getHealthTips(enterpriseCode, { list: true });

      if (data.code === 200) {
        const { result } = data || [];
        setHealthTipsList(result);
      }
    } catch (error) {
      console.error("Error while fetching health tips:", error);
    }
  };

  // Step 5: Handle redirection to view all health tips
  const handleViewAllHealthTips = () => {
    router.push("/health-tips");
  };

  // Step 6: Fetch health tips when the enterprise code changes
  useEffect(() => {
    if (enterpriseCode) fetchHealthTips();
  }, [enterpriseCode]);

  return (
    // Step 7: Render the section only if health tips are available
    <>
      {healthTipsList.length > 0 && (
      
          <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
            {/* Step 8: Section heading */}
            <SectionHeading align="left">Health Tips</SectionHeading>

            {/* Step 9: Conditional rendering based on the number of health tips */}
            {healthTipsList.length > 2 ? (
              <Box>
                {/* Slider for health tips */}
                <Slider {...sliderSettings} style={{ height: "auto" }}>
                  {healthTipsList.map((healthTipItem, index) => {
                    const { code = null } = healthTipItem || {};
                    return (
                      <Box key={code} sx={{ padding: "8px" }}>
                        <HealthTipCard healthTipItem={healthTipItem} />
                      </Box>
                    );
                  })}
                </Slider>
              </Box>
            ) : (
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: {
                    xs: "1fr",
                    sm: "repeat(2, 1fr)",
                    md: "repeat(2, 1fr)",
                  },
                  gap: "24px",
                }}
              >
                {/* Grid layout for health tips */}
                {healthTipsList.slice(0, 2).map((healthTipItem, index) => {
                  const { code = null } = healthTipItem || {};
                  return (
                    <Box key={code} id={`healthTip${index}`}>
                      <HealthTipCard healthTipItem={healthTipItem} />
                    </Box>
                  );
                })}
              </Box>
            )}

            {/* Step 10: Button to view all health tips */}
            <Box sx={{ textAlign: "center" }}>
              <OutlinedButton
                id="viewAllHealthTips"
                style={{ width: "fit-content" }}
                onClick={handleViewAllHealthTips}
              >
                View all
              </OutlinedButton>
            </Box>
          </Box>
       
      )}
    </>
  );
};

export default HealthTipSection;
