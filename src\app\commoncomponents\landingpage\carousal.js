"use client";

import { Box, Container, Typography, useTheme } from "@mui/material";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay, Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { keyframes } from "@emotion/react";
import { useRef } from "react";
import CarousalItem from "./carousalItem";
import { CustomNextArrow, CustomPrevArrow } from "./customArrows";

// Animation keyframes
const fadeIn = keyframes`
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
`;

const Carousal = ({value = [], title = "", isCarouselBanner = 0}) => {
  const theme = useTheme();
  const swiperRef = useRef(null);

  // Custom navigation functions to work with the custom arrows
  const handlePrev = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNext = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  return (
    <Box
      sx={{
        py: { xs: 4, md: 4 },
        // px: isCarouselBanner ? 0 : { xs: 2, md: 4 },
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#f8f9fa',
      }}
    >
      <Box
        sx={{
          maxWidth: '1400px',
          mx: 'auto',
          px: { xs: 2, md: 4 },
          position: 'relative',
          // px: isCarouselBanner ? 0 : { xs: 2, md: 3 },
        }}
      >
        {title && (
          <Box
            sx={{
              textAlign: 'center',
              mb: { xs: 5, md: 6 },
              animation: `${fadeIn} 0.8s ease-out`,
            }}
          >
            <Typography
              variant="h3"
              sx={{
                color: 'primary.main',
                fontSize: { xs: "1.75rem", sm: "2.25rem", md: "2.5rem" },
                fontWeight: '400',
                position: 'relative',
                display: 'inline-block',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: '-12px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '60px',
                  height: '3px',
                  backgroundColor: 'primary.main',
                  borderRadius: '3px',
                }
              }}
            >
              {title}
            </Typography>
          </Box>
        )}

        <Box
          sx={{
            width: '100%',
            animation: `${fadeIn} 0.8s ease-out`,
            position: 'relative',
            animationDelay: '0.2s',
            animationFillMode: 'both',
          }}
        >
          {/* Custom navigation arrows */}
          <Box
            onClick={handlePrev}
            sx={{
              position: 'absolute',
              left: { xs: '10px', md: '-20px' },
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: { xs: '40px', md: '50px' },
              height: { xs: '40px', md: '50px' },
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              boxShadow: '0 4px 14px rgba(0, 0, 0, 0.15)',
              transition: 'all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1)',
              '&:hover': {
                backgroundColor: 'white',
                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
                transform: 'translateY(-50%) translateX(-3px)',
              }
            }}
          >
            <CustomPrevArrow />
          </Box>

          <Box
            onClick={handleNext}
            sx={{
              position: 'absolute',
              right: { xs: '10px', md: '-20px' },
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: { xs: '40px', md: '50px' },
              height: { xs: '40px', md: '50px' },
              borderRadius: '50%',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              boxShadow: '0 4px 14px rgba(0, 0, 0, 0.15)',
              transition: 'all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1)',
              '&:hover': {
                backgroundColor: 'white',
                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
                transform: 'translateY(-50%) translateX(3px)',
              }
            }}
          >
            <CustomNextArrow />
          </Box>

          <Box
            sx={{
              '& .swiper': {
                paddingBottom: '40px !important',
              },
              '& .swiper-pagination': {
                bottom: '0px !important',
              },
              '& .swiper-pagination-bullet': {
                backgroundColor: `${theme.palette.primary.main} !important`,
                opacity: 0.5,
                width: '8px',
                height: '8px',
                margin: '0 5px',
              },
              '& .swiper-pagination-bullet-active': {
                backgroundColor: theme.palette.primary.main,
                opacity: 1,
                width: '8px',
                height: '8px',
              }
            }}
          >
            <Swiper
              ref={swiperRef}
              style={{ padding: "5px 5px", paddingBottom: "40px" }}
              modules={[Autoplay, Pagination, Navigation]}
              loop={true}
              spaceBetween={24}
              slidesPerView={1}
              pagination={{
                dynamicBullets: true,
                clickable: true,
              }}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
                stopOnLastSlide: false
              }}
              breakpoints={{
                640: {
                  slidesPerView: 1,
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 24,
                },
                1024: {
                  slidesPerView: 2,
                  spaceBetween: 30,
                },
              }}
            >
              {value.map((photo, index) => {
                const { imageUrl = "", position = null, redirection = {}, title: photoTitle = "" } = photo || {};
                const { redirectionUrl = null } = redirection || {};
                return (
                  <SwiperSlide key={index}>
                    <Box
                      sx={{
                        padding: "12px",
                        height: '100%',
                      }}
                    >
                      <CarousalItem
                        code={index}
                        imgUrl={imageUrl}
                        position={position}
                        redirectionUrl={redirectionUrl}
                        title={photoTitle}
                      />
                    </Box>
                  </SwiperSlide>
                );
              })}
            </Swiper>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Carousal;

// "use client";

// import PhotoCard from "@/app/oasis/components/photoCard";
// import { NextArrow, PrevArrow } from "@/app/oasis/components/photosSection";
// import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
// import { Box, Typography } from "@mui/material";
// import Slider from "react-slick";
// import CarousalItem from "./carousalItem";

// const Carousal = ({value = [], title = ""}) => {
//   const settings = {
//     dots: true,
//     infinite: true,
//     speed: 500,
//     slidesToShow: 2,
//     slidesToScroll: 1,
//     nextArrow: <NextArrow />,
//     prevArrow: <PrevArrow />,
//     autoplay: true,
//     autoplaySpeed: 3500,
//     pauseOnHover: true,
//     responsive: [
//       {
//         breakpoint: 900,
//         settings: {
//           slidesToShow: 2,
//           slidesToScroll: 1,
//         },
//       },
//       {
//         breakpoint: 600,
//         settings: {
//           slidesToShow: 1,
//           slidesToScroll: 1,
//         },
//       },
//     ],
//   };

//   return (
//     <SectionLayout>
//       <Typography
//         variant="h3"
//         sx={{ marginBottom: {xs: "32px", md: "32px", lg: "48px" }, color: "primary.main", fontSize: { xs: "2rem", sm: "2.5rem", md: "3rem" }, }}
//         align="center"
//       >
//         <Box
//           sx={{
//             display: "inline-block"
//           }}
//         >
//           {title || ""}
//         </Box>
//       </Typography>
//       <Slider {...settings} style={{ height: "auto" }}>
//         {value.map((photo, index) => {
//           const { imageUrl = "", position = null, redirection = {} } = photo || {};
//           const { redirectionUrl = null } = redirection || {};
//           return <CarousalItem key={index} code={index} imgUrl={imageUrl} position={position} redirectionUrl={redirectionUrl} />;
//         })}
//       </Slider>
//     </SectionLayout>
//   );
// };

// export default Carousal;

