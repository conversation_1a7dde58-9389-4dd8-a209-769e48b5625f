"use client"
import HomepageBanner from "../../components/homepageBanner";
import WidgetsSection from "../../components/widgetsSection";
import PhotosSection from "../../components/photosSection";
import VideosSection from "../../components/videosSection";
import BlogsSection from "../../components/blogsSection";
import FaqsSection from "../../components/faqsSection";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_WEBSITE,
    APOLLO_BELIAGHATA,
} from "@/constants";
import HomepageLocationBanner from "../../components/homepageLocationBanner";
import {getWebsiteHost} from "@/app/utils/clientOnly/clientUtils";
import ReviewsSection from "@/app/commoncomponents/reviewsSection";
import SectionLayoutAspire from "@/app/aspire/styledComponents/SectionLayoutAspire";
import { useEffect, useState } from "react";
import Loader from "@/app/commoncomponents/loader";
import Footer from "../../footer";
import { AppContext } from "@/app/AppContextLayout";

const getWebsiteData = async (locationCode) => {
    const domainName = getWebsiteHost()
    if (!locationCode) return;
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&domainSlug=${locationCode}`;
    try {
        const res = await fetch(url, {
            cache: "no-store"
        });
        const jsonRes = await res.json();
        return jsonRes.result || {};
    } catch (error) {
        console.log("getWebsiteData", error);
    }
};

export default function Home({params}) {
    const {locationCode = null} = params || {};
     const [websiteData, setWebsiteData] = useState(null);
    
      useEffect(() => {
        const fetchData = async () => {
          const data = await getWebsiteData(locationCode);
          setWebsiteData(data);
        };
        fetchData();
      }, [locationCode]);
    
      if (!websiteData) return <Loader />;
    const {
        banners = [],
        multiMedia = [],
        testimonials = [],
        blogs = [],
        faqs = [],
    } = websiteData || {};
    const {enterprise_code: enterpriseCode = null} = websiteData || {};

    return (
        <main>
            <AppContext.Provider value={{websiteData}}>
            {banners && banners.length > 0 && <HomepageBanner banners={banners}/>}
            <WidgetsSection/>
            {multiMedia && multiMedia.length > 0 && <PhotosSection multiMedia={multiMedia}/>}
            {/* <PhotosSection multiMedia={multiMedia}/> */}
            {multiMedia && multiMedia.length > 0 && <VideosSection multiMedia={multiMedia}/>}
            {/* <VideosSection multiMedia={multiMedia}/> */}
            {testimonials && testimonials.length > 0 && <SectionLayoutAspire>
                <ReviewsSection enterpriseCode={enterpriseCode} showDefaultReviews={true} testimonials={testimonials} />
            </SectionLayoutAspire>}
            {/* <SectionLayoutAspire>
                <ReviewsSection enterpriseCode={enterpriseCode} showDefaultReviews={true} testimonials={testimonials} />
            </SectionLayoutAspire> */}
            {blogs && blogs.length > 0 &&  <BlogsSection blogs={blogs} />}
            {/* <BlogsSection blogs={blogs} /> */}
            {faqs && faqs.length > 0 && <FaqsSection faqs={faqs}/>}
            {/* <FaqsSection faqs={faqs}/> */}
            <Footer/>
            </AppContext.Provider>
        </main>
    );
}
