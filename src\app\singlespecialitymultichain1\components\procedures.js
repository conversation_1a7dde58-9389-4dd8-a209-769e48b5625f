"use client";

import {Box, Typography} from "@mui/material";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import React, { useContext, } from "react";
import {NextArrow, PrevArrow} from "@/app/singlespecialitymultichain1/components/arrows";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
import {AppContext} from "@/app/AppContextLayout";
import ProcedureCard from "@/app/singlespecialitymultichain1/components/procedureCard";

const Procedures = ({ params }) => {
    const { websiteData = {} } = useContext(AppContext);
    const {
        procedures = []
    } = websiteData || {};


    const proceduresCarousalSettings = {
        lazyLoad: true,
        speed: 500,
        slidesToShow: 3,
        slidesToScroll: 1,
        dots: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 900,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                    dots: true
                },
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 3500,
                    pauseOnHover: true,
                    dots: false
                },
            },
        ],
    };

    if (procedures.length === 0) return <></>
    return (
        <SectionLayout>

            {procedures.length > 0 &&
                (procedures.length >= 3 ? (
                    <Box
                        sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
                    >
                        <Typography
                            variant="h5"
                            style={{ fontSize: "2.5rem" }}
                            align="center"
                            sx={{color: "#333333"}}
                        >
                            Procedures
                        </Typography>
                        <Slider {...proceduresCarousalSettings}>
                            {procedures.map((procedure, index) => {
                                const {
                                    code = "",
                                    speciality = {}
                                } = procedure || {};

                                const {
                                    code: specialityCode = "",
                                } = speciality || {};

                                return (
                                    <Box id={`speciality-${specialityCode}-procedure${index}Box`} key={code} sx={{ padding: "8px 8px" }}>
                                        <ProcedureCard
                                            id={`speciality-${specialityCode}-procedure${index}`}
                                            procedure={procedure}
                                        />
                                    </Box>
                                );
                            })}
                        </Slider>
                    </Box>
                ) : (
                    <Box
                        sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
                    >
                        <Typography
                            variant="h5"
                            style={{ fontSize: "2.5rem" }}
                            align="center"
                        >
                            Procedures
                        </Typography>
                        <Box
                            sx={{
                                display: "grid",
                                gridTemplateColumns: {
                                    xs: "1fr",
                                    sm: "1fr 1fr",
                                    md: "repeat(3, 1fr)",
                                },
                            }}
                        >
                            {procedures.map((procedure, index) => {
                                const {
                                    code = "",
                                    speciality = {}
                                } = procedure || {};

                                const {
                                    code: specialityCode = "",
                                } = speciality || {};

                                return (
                                    <Box id={`speciality-${specialityCode}-procedure${index}Box`} key={code} sx={{ padding: "8px 8px" }}>
                                        <ProcedureCard
                                            id={`speciality-${specialityCode}-procedure${index}`}
                                           procedure={procedure}
                                        />
                                    </Box>
                                );
                            })}
                        </Box>
                    </Box>
                ))}
        </SectionLayout>
    );
};

export default Procedures;
