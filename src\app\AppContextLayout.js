"use client";

import React, { useState } from "react";
import { useMediaQuery } from "@mui/material";
import MySnackBar from "./oasis/components/mySnackBar";

export const AppContext = React.createContext();

const AppContextLayout = ({ children, websiteData = {} }) => {
  const [viewSnackbar, setViewSnackbarMain] = useState({
    message: null,
    type: null,
  });

  const mobileView = useMediaQuery('(max-width:600px)');
  const tabletView = useMediaQuery("(max-width:1200px)");
  const desktopView = useMediaQuery('(min-width:1200px)');

  const value = {
    websiteData,
    setViewSnackbarMain,
    mobileView,
    desktopView
  };

  return (
    <AppContext.Provider value={value}>
      {Boolean(viewSnackbar.message) && (
        <MySnackBar
          variant={viewSnackbar.type}
          message={viewSnackbar.message}
          setViewSnackbar={setViewSnackbarMain}
        />
      )}
      {children}
    </AppContext.Provider>
  );
};

export default AppContextLayout;
