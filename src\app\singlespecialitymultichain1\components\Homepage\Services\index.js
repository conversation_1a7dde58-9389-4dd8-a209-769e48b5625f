"use client"
import React, {useState} from "react";
import {alpha, Box, Typography, useTheme} from "@mui/material";
import useStyles from "../../../styles";
import homepageStyles from "../styles";
import Image from "next/image";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
import PrimaryButton from "@/app/singlespecialitymultichain1/styledComponents/primaryButton";
import {getThumborUrl} from "@/app/utils/getThumborUrl";

const Services = ({services = []}) => {
    const commonClasses = useStyles();
    const classes = homepageStyles();
    const [selectedService, setSelectedService] = useState({});
    const [open, setOpen] = useState(false);
    const theme = useTheme();

    const closeModal = () => setOpen(false);
    const openModal = (service) => {
        setSelectedService({
            svcName: service.name,
            svcDescription: service.short_description,
        });
        setOpen(true);
    };

    return (

        <SectionLayout
            id="services">
            <Box
                sx={{
                    display: "flex",
                    marginTop: { lg: "40px"},
                    flexDirection: {
                        xs: "column", // Stack on mobile (xs)
                        md: "row",    // Side-by-side on desktop (md and up)
                    },
                    gap: "16px", // Space between containers
                    width: "100%", // Ensure full width
                }}
            >
                {/* Container 1 - 30% on desktop, 100% on mobile */}
                <Box
                    sx={{
                        flexBasis: { xs: "100%", md: "30%" }, // 30% on desktop, 100% on mobile
                        flexShrink: 0, // Prevents the box from shrinking
                        display: "flex",
                        flexDirection: "column", // Keeps the child items in a column
                        gap: "8px", // Space between child items
                    }}
                >
                    <Typography
                        variant="h5"
                        sx={{
                            textTransform: "uppercase",
                            color: "primary.main",
                        }}
                    >
                        Services
                    </Typography>
                    <Typography
                        sx={{
                            color: "#333333",
                        }}
                        variant="h3"
                    >
                        Our Healthcare Services
                    </Typography>
                    <Box
                        sx={{
                            height: "200px", // Set your dimensions
                            marginTop: "16px",
                            backgroundColor: "secondary.main", // Set the desired color
                            mask: "url(/services.svg) no-repeat", // Apply the SVG as a mask
                            WebkitMask: "url(/services.svg) no-repeat", // For webkit-based browsers
                            maskSize: "contain",
                            WebkitMaskSize: "contain",
                            maskPosition: "left top", // Align the image to the start (top-left)
                            WebkitMaskPosition: "left top", // Same for webkit-based browsers
                            display: {xs: "none", md:"block"}
                        }}
                    />
                </Box>

                {/* Container 2 - 70% on desktop, 100% on mobile */}
                <Box
                    sx={{
                        flexBasis: { xs: "100%", md: "70%" }, // 70% on desktop, 100% on mobile
                        display: 'grid',
                        gridTemplateColumns: {
                            xs: 'repeat(1, 1fr)', // Stack vertically on mobile
                            md: 'repeat(2, 1fr)', // 50% 50% on desktop
                        },
                        gap: '16px', // Space between grid items
                        alignContent: 'center',
                    }}
                >
                    {services.map((item, index) => {
                        const {service = {}} = item || {};
                        const {
                            name: serviceName = null,
                            short_description: serviceDescription = null,
                            image_url: serviceImg = "",
                        } = service || {};
                        return (
                            <Box key={index} className={classes.servicesGridItem} sx={{ flex: 1, "&:hover": {
                                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                                }, cursor: "pointer", }}
                            onClick={() => openModal(service)}>
                                <Box sx={{ flex: 1 }}>
                                    <Typography
                                        variant="subtitle1"
                                        sx={{color: "#333333"}}
                                        className={classes.servicesGridItemHeading}
                                    >
                                        {serviceName || ""}
                                    </Typography>
                                    <Typography
                                        variant="subtitle2"
                                        className={classes.servicesGridItemSubHeading}
                                    >
                                        {serviceDescription || ""}
                                    </Typography>
                                </Box>
                                <Box className={classes.servicesGridItemImageBox}>
                                    {Boolean(serviceImg) ? (
                                        <Image
                                            alt={serviceName || "Service"}
                                            width={64}
                                            height={64}
                                            src={getThumborUrl(serviceImg, 0, 0)}
                                            style={{
                                                objectFit: "cover",
                                                objectPosition: "center",
                                            }}
                                        />
                                    ) : (
                                        <svg
                                            width="64"
                                            height="64"
                                            viewBox="0 0 24 24"
                                            fill={theme.palette.primary.main}
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            {/* SVG content */}
                                            <path d="..." stroke="black" stroke-width="1.5" stroke-linecap="round" />
                                        </svg>
                                    )}
                                </Box>
                            </Box>
                        );
                    })}
                </Box>
            </Box>
            <Dialog
                open={open}
                onClose={closeModal}
                keepMounted={false}
                aria-describedby="alert-dialog-slide-description"
                sx={{ ".MuiDialog-paper": { width: { xs: "100%", sm: "600px" } } }}
            >
                <DialogTitle  className={classes.servicesGridItemHeading} sx={{color: "#333333"}}>{selectedService.svcName || ""}</DialogTitle>
                <DialogContent>
                    <Typography
                        variant="subtitle2"
                        className={classes.servicesGridItemSubHeading}
                    >
                        {selectedService.svcDescription || ""}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <PrimaryButton onClick={closeModal}>Close</PrimaryButton>
                </DialogActions>
            </Dialog>
        </SectionLayout>

    );
};

export default Services;
