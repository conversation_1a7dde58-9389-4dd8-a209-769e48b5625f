"use client";
import React, { useState, useEffect } from "react";
import { Box, Typography, Grid, Container, useMediaQuery, useTheme, CircularProgress } from "@mui/material";
import Link from "next/link";
import CategoryCard from "./CategoryCard";
import { useRouter } from "next/navigation";

export default function PopularCategories({ categories, isLoading = false }) {

  // Use the data directly from the API response
  const categoriesData = categories?.data ? categories.data : [];

  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // Use useEffect to update displayedChecks when categoriesData changes
  const [displayedChecks, setDisplayedChecks] = useState([]);

  // Update displayedChecks when categoriesData or isMobile changes
  useEffect(() => {
    if (isMobile) {
      setDisplayedChecks(categoriesData.slice(0, 6));
    } else {
      setDisplayedChecks(categoriesData.slice(0, 12));
    }
  }, [isMobile, categoriesData]);

  const handleCategoryClick = (category) => {
    const categoryParam = category.seoSlug;
    router.push(`/tests?category=${categoryParam}`);
  };

  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
          <CircularProgress size={40} />
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Loading categories...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="h2" component="h2" fontWeight="bold" color="text.black" sx={{ fontSize: { xs: '1.5rem', md: '1.8rem' } }}>
          Popular Categories {categories?.totalCount ? `(${categories.totalCount})` : categories?.availableCount ? `(${categories.availableCount})` : categoriesData.length > 0 ? `(${categoriesData.length})` : ''}
        </Typography>
        <Link href="/categories" passHref aria-label="View all categories">
          <Typography
            component="span"
            sx={{
              textWrap: "nowrap",
              color: theme.palette.primary.main,
              cursor: "pointer",
              fontWeight: 500,
              fontSize: "0.9rem",
            }}
          >
            View All
          </Typography>
        </Link>
      </Box>

      {displayedChecks.length > 0 ? (
        <Grid container spacing={2}>
          {displayedChecks.map((check) => (
            <Grid item xs={6} sm={4} md={3} lg={2} key={check.code || check.id}>
              <CategoryCard
                category={check}
                onClick={() => handleCategoryClick(check)}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Typography variant="body1" sx={{ py: 4, textAlign: 'center' }}>
          No categories available at the moment.
        </Typography>
      )}
    </Container>
  );
}
