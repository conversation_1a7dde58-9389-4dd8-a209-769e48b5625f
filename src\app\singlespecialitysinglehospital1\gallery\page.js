"use client";

import { useContext, useState } from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardMedia,
  Modal,
  IconButton,
  useMediaQuery,
  Tabs,
  Tab,
  styled,
  Dialog,
  DialogContent,
} from "@mui/material";
import { <PERSON>ForwardIos, ArrowBackIos, Close } from "@mui/icons-material";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import CloseIcon from "@mui/icons-material/Close";
import VideosSection from "../components/videosSection";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";
import { AppContext } from "@/app/AppContextLayout";

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  overflow: "hidden",
  height: "280px",
  transition: "transform 0.3s ease-in-out",
  "&:hover": {
    transform: "scale(1.02)",
  },
}));

const ModalContent = styled(Box)(({ theme }) => ({
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "90%",
  height: "90%",
  maxWidth: "1200px",
  maxHeight: "800px",
  backgroundColor: "black",
  boxShadow: theme.shadows[24],
  outline: "none",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
}));

export default function GalleryPage() {
  const [open, setOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, multiMedia = [] } =
    websiteData || {};

  // Filter out items with video_url and assign column sizes based on position
  if (multiMedia.length === 0) {
    return (
      <>
        <Typography>No Gallery Available</Typography>
      </>
    );
  }
  const galleryImages = multiMedia
    .filter((item) => item.video_url === null)
    .map((item, index) => ({
      url: item.image_url,
      title: item.title,
      cols: index < 3 ? 4 : 3, // First row gets 4 columns each, rest get 3
    }));

  const handleOpen = (index) => {
    setCurrentImageIndex(index);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handlePrev = (event) => {
    if (event) event.stopPropagation();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? galleryImages.length - 1 : prevIndex - 1
    );
  };

  const handleNext = (event) => {
    if (event) event.stopPropagation();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === galleryImages.length - 1 ? 0 : prevIndex + 1
    );
  };

  // const handleCategoryChange = (event, newValue) => {
  //   setCategory(newValue);
  // };

  const MobileSlider = () => (
    <Box sx={{ position: "relative", width: "100%", height: "300px" }}>
      <CardMedia
        component="img"
        height="300"
        image={galleryImages[currentImageIndex].url}
        alt={
          galleryImages[currentImageIndex].title ||
          `Gallery image ${currentImageIndex + 1}`
        }
        sx={{ objectFit: "cover", cursor: "pointer" }}
        onClick={() => handleOpen(currentImageIndex)}
      />
      <IconButton
        onClick={handlePrev}
        sx={{
          position: "absolute",
          left: 2,
          top: "50%",
          transform: "translateY(-50%)",
          color: "white",
        }}
      >
        <ArrowBackIos />
      </IconButton>
      <IconButton
        onClick={handleNext}
        sx={{
          position: "absolute",
          right: 2,
          top: "50%",
          transform: "translateY(-50%)",
          color: "white",
        }}
      >
        <ArrowForwardIos />
      </IconButton>
    </Box>
  );

  return (
    <Box sx={{ backgroundColor: "#f8f8f8" }}>
      <SectionLayoutSingleSpecialitySingleHospital>
        <Box>
          {/* Header Section */}
          <Box>
            <Box>
              <Box sx={{ textAlign: "center", pb: 0 }}>
                <Typography
                  variant="h5"
                  sx={{
                    color: "primary.main",
                    mb: 1,
                    display: "block",
                  }}
                >
                  OUR GALLERY
                </Typography>
                <Typography
                  variant="h3"
                  component="h2"
                  sx={{
                    color: "#1a1a1a",
                    mb: 2,
                  }}
                >
                  Delhi Eye Care Gallery
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#666",
                    maxWidth: "600px",
                    mx: "auto",
                  }}
                >
                  Comprehensive eye care procedures delivered with expertise and
                  compassion
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Gallery Section */}
          <Box sx={{ py: 6 }}>
            {isMobile ? (
              <MobileSlider />
            ) : (
              <Grid container spacing={3}>
                {galleryImages.map((image, index) => (
                  <Grid item xs={12} sm={6} md={image.cols} key={index}>
                    <StyledCard
                      sx={{ cursor: "pointer" }}
                      onClick={() => handleOpen(index)}
                    >
                      <CardMedia
                        component="img"
                        height="280"
                        image={image.url}
                        alt={image.title || `Gallery image ${index + 1}`}
                        sx={{
                          objectFit: "cover",
                          cursor: "pointer",
                        }}
                      />
                    </StyledCard>
                  </Grid>
                ))}
              </Grid>
            )}

            {/* Replaced Modal with Dialog from GallerySection.js */}
            <Dialog
              open={open}
              onClose={handleClose}
              maxWidth="xl"
              fullScreen
              PaperProps={{
                sx: {
                  backgroundColor: "rgba(0, 0, 0, 0.49)",
                  backgroundImage:
                    "linear-gradient(rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.13))",
                },
              }}
            >
              {/* Close button */}
              <IconButton
                onClick={handleClose}
                sx={{
                  position: "absolute",
                  right: "20px",
                  top: "20px",
                  zIndex: 2,
                  color: "white",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(4px)",
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                  },
                }}
              >
                <CloseIcon />
              </IconButton>

              {/* Image container */}
              <DialogContent
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: { xs: 2, sm: 4 },
                  height: "100vh",
                  overflow: "hidden",
                }}
              >
                <Box
                  sx={{
                    position: "relative",
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {/* Main Image */}
                  <Box
                    component="img"
                    src={galleryImages[currentImageIndex]?.url || ""}
                    alt={
                      galleryImages[currentImageIndex]?.title ||
                      `Image ${currentImageIndex + 1}`
                    }
                    sx={{
                      maxWidth: "90%",
                      maxHeight: "85vh",
                      objectFit: "contain",
                      borderRadius: "8px",
                      boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
                      transition: "transform 0.3s ease-in-out",
                    }}
                  />

                  {/* Image counter */}
                  <Typography
                    variant="body2"
                    sx={{
                      position: "absolute",
                      bottom: "20px",
                      left: "50%",
                      transform: "translateX(-50%)",
                      color: "white",
                      backgroundColor: "rgba(0, 0, 0, 0.6)",
                      padding: "8px 16px",
                      borderRadius: "20px",
                      backdropFilter: "blur(4px)",
                    }}
                  >
                    {currentImageIndex + 1} / {galleryImages.length}
                  </Typography>
                </Box>
              </DialogContent>

              {/* Navigation buttons */}
              <Box
                sx={{
                  position: "absolute",
                  top: "50%",
                  left: 0,
                  right: 0,
                  transform: "translateY(-50%)",
                  display: "flex",
                  justifyContent: "space-between",
                  px: { xs: 2, sm: 4, md: 6 },
                  pointerEvents: "none",
                }}
              >
                <IconButton
                  onClick={handlePrev}
                  sx={{
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    backdropFilter: "blur(4px)",
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                    },
                    pointerEvents: "auto",
                  }}
                >
                  <ArrowBackIcon sx={{ fontSize: 30 }} />
                </IconButton>

                <IconButton
                  onClick={handleNext}
                  sx={{
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    backdropFilter: "blur(4px)",
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                    },
                    pointerEvents: "auto",
                  }}
                >
                  <ArrowForwardIcon sx={{ fontSize: 30 }} />
                </IconButton>
              </Box>
            </Dialog>
          </Box>
        </Box>
      </SectionLayoutSingleSpecialitySingleHospital>
      {/* Videos Section */}
      <VideosSection multiMedia={multiMedia} />
    </Box>
  );
}
