
"use client";

import {Typography, Box} from "@mui/material";
import Image from "next/image";
import {getThumborUrl} from "../../utils/getThumborUrl";
import {NextArrow, PrevArrow} from "./photosSection";
import Slider from "react-slick";
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import HighlightsWidgetChainTemp2 from "@/app/hospitalchaintemplate2apollo/components/highlights/highlightsWidgetChainTemp2";
import {useContext} from "react";
import {AppContext} from "@/app/AppContextLayout";

const HomepageBanner = () => {
    const {websiteData} = useContext(AppContext);
    const {
        banners = [],
    } = websiteData || {};
  const desktopSettings = {
    dots: true,
    infinite: true,
    adaptiveHeight: true,
    lazyLoad: false,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    speed: 500,
    autoplaySpeed: 3000,
    cssEase: "linear",
    pauseOnHover: true,
    arrows: false,
    nextArrow: <NextArrow/>,
    prevArrow: <PrevArrow/>,
  };
    const mobileSettings = {
        dots: true,
        infinite: true,
        adaptiveHeight: true,
        lazyLoad: "ondemand", // Load images when they enter the viewport
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        speed: 500,
        autoplaySpeed: 3000,
        cssEase: "linear",
        pauseOnHover: true,
        arrows: false,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
    };

  return (

      <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            bgcolor: "#f6f6f6",
            marginBottom: {xs: "0px", md: "40px" },
          }}
          style={{
            position: "relative",
            display: "flex",
            justifyContent: "center",
            width: "100%",
          }}
      >
          <Box sx={{
              display: {xs: "block", md: "none"}
          }}>
              <Slider {...mobileSettings} style={{width: "100%", height: "100%"}}>
                  {banners.map((banner, index) => {
                      const {
                          image_url: bannerImg = "",
                          title = "",
                          text = "",
                      } = banner || {};
                      return (
                          <Box sx={{position: "relative", width: "100%"}}>

                                  <Image
                                      alt="slider1"
                                      src={getThumborUrl(bannerImg)}
                                      layout="responsive"
                                      width={1920}
                                      height={1080}
                                      objectFit="cover"
                                      priority={index === 0}
                                      loading={index === 0 ? "eager" : "lazy"}
                                      style={{
                                          height: "auto",
                                          width: "100%",
                                      }}
                                  />
                              <Box sx={{
                                  position: "absolute",
                                  left: "50%",
                                  top: "50%",
                                  transform: "translate(-50%, -50%)",
                                  textAlign: 'center'
                              }}>
                                  <Typography
                                      variant="h3"
                                      sx={{
                                          fontSize: {xs: '1.5rem', sm: '2rem', md: '3rem'},
                                          color: 'text.primary',
                                          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                                      }}
                                  >
                                      {title || ""}
                                  </Typography>
                                  <Typography
                                      variant="subtitle1"
                                      sx={{
                                          fontSize: {xs: '1rem', sm: '1.25rem', md: '1.5rem'},
                                          color: 'text.primary',
                                          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                                      }}
                                  >
                                      {text || ""}
                                  </Typography>
                              </Box>
                          </Box>

                      );
                  })}
              </Slider>
          </Box>

          <Box sx={{
              display: {xs: "none", md: "block"}
          }}>
              <Slider {...desktopSettings} style={{width: "100%", height: "100%"}}>
                  {banners.map((banner, index) => {
                      const {
                          image_url: bannerImg = "",
                          title = "",
                          text = "",
                      } = banner || {};
                      return (
                          <Box sx={{position: "relative", width: "100%"}}>
                              <Box sx={{
                                  display: {xs: "none", md: "block"}
                              }}>
                                  <Image
                                      alt="slider1"
                                      src={getThumborUrl(bannerImg)}
                                      layout="responsive"
                                      width={1920}
                                      height={1080}
                                      objectFit="cover"
                                      priority={true}
                                      style={{
                                          height: "auto",
                                          width: "100%",
                                      }}
                                  />
                              </Box>
                              <Box sx={{
                                  position: "absolute",
                                  left: "50%",
                                  top: "50%",
                                  transform: "translate(-50%, -50%)",
                                  textAlign: 'center'
                              }}>
                                  <Typography
                                      variant="h3"
                                      sx={{
                                          fontSize: {xs: '1.5rem', sm: '2rem', md: '3rem'},
                                          color: 'text.primary',
                                          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                                      }}
                                  >
                                      {title || ""}
                                  </Typography>
                                  <Typography
                                      variant="subtitle1"
                                      sx={{
                                          fontSize: {xs: '1rem', sm: '1.25rem', md: '1.5rem'},
                                          color: 'text.primary',
                                          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                                      }}
                                  >
                                      {text || ""}
                                  </Typography>
                              </Box>
                          </Box>

                      );
                  })}
              </Slider>
          </Box>

        <HighlightsWidgetChainTemp2/>
      </Box>
  );
};

export default HomepageBanner;