"use client";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
} from "@/constants";
import { getWebsiteHost } from "@/app/utils/clientOnly/clientUtils";
import ReviewsSection from "@/app/commoncomponents/reviewsSection";
import Carousal from "@/app/singlespecialitymultichain1/Carousal";
import Services from "@/app/singlespecialitymultichain1/components/Homepage/Services";
import Gallery from "@/app/singlespecialitymultichain1/components/Homepage/Gallery";
import VideosSection from "@/app/singlespecialitymultichain1/components/videosSection";
import BlogsSection from "@/app/singlespecialitymultichain1/components/Homepage/Blogs";
import FaqSection from "@/app/singlespecialitymultichain1/components/Homepage/Faq";
import React, { useEffect, useState } from "react";
import Loader from "@/app/commoncomponents/loader";
import Footer from "../../Footer";
import { AppContext } from "@/app/AppContextLayout";

const getWebsiteData = async (locationCode) => {
  const domainName = getWebsiteHost();
  if (!locationCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&domainSlug=${locationCode}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default function Home({ params }) {
  const { locationCode = null } = params || {};
  const [websiteData, setWebsiteData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const data = await getWebsiteData(locationCode);
      setWebsiteData(data);
    };
    fetchData();
  }, [locationCode]);

  if (!websiteData) return <Loader />;

  const {
    banners = [],
    multiMedia = [],
    emails = [],
    phoneNumbers = [],
    testimonials = [],
    websiteServices = [],
    blogs = [],
    faqs = [],
  } = websiteData || {};
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  return (
    // <main>
    //     <HomepageLocationBanner banners={banners} enterpriseCode ={enterpriseCode} locationCode={locationCode}/>
    //     <WidgetsSection/>
    //     <PhotosSection multiMedia={multiMedia}/>
    //     <VideosSection multiMedia={multiMedia}/>
    //     <SectionLayoutAspire>
    //         <ReviewsSection enterpriseCode={enterpriseCode} showDefaultReviews={true} testimonials={testimonials} />
    //     </SectionLayoutAspire>
    //     <BlogsSection blogs={blogs} />
    //     <FaqsSection faqs={faqs}/>
    // </main>
    <>
      <AppContext.Provider value={{ websiteData, mobileView: false }}>
        <Carousal
          banners={banners}
          emails={emails}
          phoneNumbers={phoneNumbers}
        />
        {websiteServices.length > 0 && <Services services={websiteServices} />}
        {multiMedia.length > 0 && <Gallery gallery={multiMedia} />}
        <VideosSection multiMedia={multiMedia} />
        {blogs.length > 0 && <BlogsSection blogs={blogs} />}
        <ReviewsSection testimonials={testimonials} />
        {faqs.length > 0 && <FaqSection faqs={faqs} />}
        <Footer />
      </AppContext.Provider>
    </>
  );
}
