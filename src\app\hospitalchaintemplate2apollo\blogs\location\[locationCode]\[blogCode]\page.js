"use client";

import { Box, Typography } from "@mui/material";
import axios from "axios";
import Image from "next/image";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_BLOGS,
  LEAD_SOURCES,
} from "@/constants";
import { useContext, useEffect, useState } from "react";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useRouter } from "next/navigation";
import QuickEnquiry from "@/app/commoncomponents/quickEnquiry";
import SectionLayoutAspire from "@/app/aspire/styledComponents/SectionLayoutAspire";

export default function BlogDetail({ params }) {
  const { blogCode = null, locationCode = null } = params || {};
  const router = useRouter();
  const [blogDetail, setBlogDetail] = useState({});
  const [enterpriseCode, setEnterpriseCode] = useState(null);
  const { title = "", content = "", imageUrl = "" } = blogDetail || {};

  const getEnterpriseCode = async () => {
    const domainName = window.location.hostname;
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true&domainSlug=${locationCode}`;
    try {
      const res = await fetch(url, {
        cache: "no-store",
      });
      const jsonRes = await res.json();
      const { result = {} } = jsonRes || {};
      const { enterprise_code: enterpriseCode = null } = result || {};
      setEnterpriseCode(enterpriseCode);
    } catch (error) {
      console.log("getWebsiteData", error);
    }
  };
  const getBlogDetails = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?code=${blogCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        const { seoSlug = "" } = result[0] || {};
        if (blogCode !== seoSlug) {
          router.replace(`/blogs/location/${locationCode}/${seoSlug}`);
        }
        setBlogDetail(result[0] || {});
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    getEnterpriseCode();
  }, []);

  useEffect(() => {
    if (enterpriseCode) getBlogDetails();
  }, [enterpriseCode]);

  return (
    <Box>
      <SectionLayoutAspire>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
          {imageUrl && (
            <Box
              sx={{
                minHeight: { xs: "240px", sm: "320px", md: "400px" },
                position: "relative",
              }}
            >
              <Image
                alt="blog-banner"
                fill
                src={getThumborUrl(imageUrl)}
                style={{
                  height: "100%",
                  width: "100%",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  objectFit: "contain",
                  borderRadius: "10px",
                }}
              />
            </Box>
          )}
          <Typography
            variant="h3"
            align="center"
            sx={{
              fontWeight: "500",
              color: "primary.main",
              fontSize: { xs: "1.75rem", sm: "40px" },
            }}
          >
            {title || ""}
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", lg: "row" }, // Stack on small screens, side-by-side on large screens
              gap: { xs: "24px", lg: "24px" },
            }}
          >
            <Box
              sx={{
                flex: { lg: "7", xs: "1" }, // 70% width for large screens, full width for small screens
                flexBasis: { lg: "70%", xs: "100%" }, // Explicitly set the percentage width
                maxWidth: { lg: "70%", xs: "100%" }, // Prevent overflow on small screens
              }}
            >
              <div
                className="ck-content"
                dangerouslySetInnerHTML={{ __html: content }} // Render HTML safely
              />
            </Box>

            <Box
              sx={{
                flex: { lg: "3", xs: "1" }, // 30% width for large screens, full width for small screens
                flexBasis: { lg: "30%", xs: "100%" }, // Explicitly set the percentage width
                maxWidth: { lg: "30%", xs: "100%" }, // Prevent overflow on small screens
                position: "sticky", // Sticky positioning for larger screens
                top: "200px", // Adjust how far from the top it sticks
                alignSelf: { xs: "center", lg: "flex-start" }, // Center on small screens, top align on large
                flexShrink: 0, // Prevent shrinking of the box
              }}
            >
              {/*<QuickEnquiry leadSource={LEAD_SOURCES.BLOG_DETAIL_PAGE} productCode={blogCode}/>*/}
              <QuickEnquiry
                pageData={{
                  leadSource: LEAD_SOURCES.BLOG_DETAIL_PAGE,
                  productCode: blogCode,
                  pageTitle: title,
                  enterpriseCode: enterpriseCode,
                }}
              />
            </Box>
          </Box>
        </Box>
      </SectionLayoutAspire>
    </Box>
  );
}
