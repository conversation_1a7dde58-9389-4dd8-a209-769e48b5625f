import {
    Box, Button, CircularProgress, DialogContent,
    FormControl, FormHelperText, IconButton, InputBase,
    MenuItem, Select, Typography, useTheme
} from "@mui/material";
import {
    API_ENDPOINT_GENERATE,
    API_SECTION_API,
    API_SECTION_ENTERPRISE, API_SECTION_LEADS,
    API_SECTION_PUBLIC,
    API_VERSION, COUNTRIES, countryToFlag,
    HARBOR_API_DOCFYN_DOMAIN
} from "@/constants";
import axios from "axios";
import React, {useContext, useState} from "react";
import {isEmptyObject} from "@/app/utils/isEmptyObject";
import {useRouter} from "next/navigation";
import Dialog from '@mui/material/Dialog';
import CloseIcon from "@mui/icons-material/Close";
import {AppContext} from "@/app/AppContextLayout";


const ModalForm = ({enterpriseCode = null, campaignName = null, isModalOpen = false, setIsModalOpen, productCode= null, requestMappings = []}) => {
    const router = useRouter();
    const theme = useTheme();
    const {mobileView} = useContext(AppContext);
    const initialInput = {dialCode: "+91"};
    const [input, setInput] = useState(initialInput);
    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const handleInputChange = (e) => {
        if (errors[e.target.name]) {
            const copyOfErrors = {...errors};
            delete copyOfErrors[e.target.name];
            setErrors(copyOfErrors);
        }
        setInput((prev) => ({...prev, [e.target.name]: e.target.value}));
    };

    const handleValidation = () => {
        const error = {};
        if (!input["firstName"] || input["firstName"].length === 0)
            error["firstName"] = "Cannot be empty";
        if (!input["phone"] || input["phone"].length === 0)
            error["phone"] = "Cannot be empty";
        if (input["phone"]?.length !== 10)
            error["phone"] = "Enter a 10 digit phone number";
        if (!isEmptyObject(error)) {
            setErrors(error);
            return false;
        }
        return true;
    };

    const extractCodeFromSlug = (slug) => {
        const segments = slug.split('-');
        return segments[segments.length - 1];
    }

    const handleLeadGeneration = async () => {
        if (!enterpriseCode || isLoading) return;
        if (!handleValidation()) return;
        setIsLoading(true);
        const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}${productCode ? `?productCode=${extractCodeFromSlug(productCode)}` : ''}`;
        const reqBody = {...input, leadSource: 6, lastName: "", type: 2, requestMappings};
        if (campaignName) reqBody["campaignName"] = campaignName;
        try {
            const response = await axios.post(url, reqBody, {
                headers: {
                    "Content-Type": "application/json",
                    source: mobileView ? "mweb" : "website",
                },
            });
            const {status = null} = response || {};
            if (status >= 200 && status < 300) {
                // Success, redirect to thank you page
                router.push("/thank-you");
                setInput({...initialInput});
            }
        } catch (error) {
            console.error(error)
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Dialog
            open={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            PaperProps={{
                sx: {
                    borderRadius: "12px",
                    overflow: "hidden",
                    boxShadow: "0 10px 30px rgba(0,0,0,0.15)",
                    width: { xs: "85%", sm: "100%" },
                    maxWidth: { xs: "320px", sm: "380px" },
                    margin: { xs: "16px", md: 0 },
                    backgroundColor: "white",
                }
            }}
            TransitionProps={{
                style: {
                    transition: 'all 0.3s ease-out'
                }
            }}
        >
            {/* Header */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: { xs: "14px 20px", sm: "16px 24px" },
                    borderBottom: "1px solid rgba(0,0,0,0.06)",
                    backgroundColor: "rgba(0,0,0,0.01)",
                }}
            >
                <Typography
                    variant="h6"
                    sx={{
                        fontWeight: 600,
                        color: "primary.main",
                        fontSize: "1.2rem",
                    }}
                >
                    Book an Appointment
                </Typography>
                <IconButton
                    edge="end"
                    color="inherit"
                    onClick={() => setIsModalOpen(false)}
                    aria-label="close"
                    size="small"
                    sx={{
                        color: "text.secondary",
                        transition: "all 0.2s ease",
                        "&:hover": {
                            backgroundColor: "rgba(0,0,0,0.04)",
                            transform: "rotate(90deg)",
                        }
                    }}
                >
                    <CloseIcon fontSize="small" />
                </IconButton>
            </Box>

            {/* Form Content */}
            <DialogContent sx={{ padding: { xs: "16px 20px", sm: "20px 24px" } }}>
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "16px",
                    }}
                >
                    {/* Name Field */}
                    <FormControl fullWidth>
                        <Typography
                            variant="body2"
                            sx={{
                                mb: 0.75,
                                fontWeight: 500,
                                color: "text.secondary",
                                fontSize: "0.875rem",
                            }}
                        >
                            Your Name
                        </Typography>
                        <InputBase
                            name="firstName"
                            id="leadName"
                            sx={{
                                borderRadius: "8px",
                                height: { xs: "40px", sm: "42px" },
                                padding: "8px 12px",
                                fontSize: { xs: "0.9rem", sm: "0.95rem" },
                                width: "100%",
                                color: "#333",
                                transition: "all 0.2s ease",
                                backgroundColor: "rgba(0,0,0,0.02)",
                                border: errors["firstName"] ? "1px solid #d32f2f" : "1px solid rgba(0,0,0,0.08)",
                                "& .MuiInputBase-input": {
                                    height: "100%",
                                    padding: "0 14px",
                                    display: "flex",
                                    alignItems: "center",
                                },
                                "&:hover": {
                                    backgroundColor: "rgba(0,0,0,0.03)",
                                    border: errors["firstName"] ? "1px solid #d32f2f" : "1px solid rgba(0,0,0,0.12)",
                                },
                                "&:focus-within": {
                                    backgroundColor: "#fff",
                                    border: errors["firstName"] ? "1px solid #d32f2f" : `1px solid ${theme.palette.primary.main}`,
                                    boxShadow: errors["firstName"] ? "none" : `0 0 0 2px ${theme.palette.primary.main}10`,
                                }
                            }}
                            value={input["firstName"] || ""}
                            onChange={handleInputChange}
                            placeholder="Enter your full name"
                            inputProps={{
                                "aria-label": "enter your name",
                                style: { height: "100%", padding: "0" }
                            }}
                        />
                        {Boolean(errors["firstName"]) && (
                            <FormHelperText
                                error={true}
                                sx={{
                                    ml: 0.5,
                                    mt: 0.5,
                                    fontSize: "0.7rem",
                                }}
                            >
                                {errors["firstName"]}
                            </FormHelperText>
                        )}
                    </FormControl>

                    {/* Phone Field */}
                    <FormControl fullWidth>
                        <Typography
                            variant="body2"
                            sx={{
                                mb: 0.75,
                                fontWeight: 500,
                                color: "text.secondary",
                                fontSize: "0.875rem",
                            }}
                        >
                            Phone Number
                        </Typography>
                        <Box sx={{ display: "flex", gap: "8px", alignItems: "center" }}>
                            <Select
                                id="quickEnquiryCountryCode"
                                value={input["dialCode"]}
                                onChange={(e) =>
                                    setInput((prev) => ({ ...prev, dialCode: e.target.value }))
                                }
                                sx={{
                                    height: { xs: "40px", sm: "42px" },
                                    minWidth: "80px",
                                    color: "#333",
                                    padding: "8px 0px",
                                    backgroundColor: "rgba(0,0,0,0.02)",
                                    borderRadius: "8px",
                                    border: "1px solid rgba(0,0,0,0.08)",
                                    "& .MuiSelect-select": {
                                        paddingY: "10px",
                                        paddingX: "10px",
                                        fontSize: { xs: "0.9rem", sm: "0.95rem" },
                                        display: "flex",
                                        alignItems: "center",
                                    },
                                    "&:hover": {
                                        backgroundColor: "rgba(0,0,0,0.03)",
                                        border: "1px solid rgba(0,0,0,0.12)",
                                    },
                                    "&.Mui-focused": {
                                        backgroundColor: "#fff",
                                        border: `1px solid ${theme.palette.primary.main}`,
                                        boxShadow: `0 0 0 2px ${theme.palette.primary.main}10`,
                                    }
                                }}
                                MenuProps={{
                                    PaperProps: {
                                        sx: {
                                            maxHeight: "300px",
                                            borderRadius: "8px",
                                            mt: 0.5,
                                            boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                                        }
                                    }
                                }}
                            >
                                {COUNTRIES.map((country, index) => {
                                    const {
                                        dial_code: dialCode = "",
                                        code = "IN",
                                        value = "",
                                    } = country || {};
                                    return (
                                        <MenuItem
                                            key={index}
                                            value={dialCode}
                                            sx={{
                                                color: "#333",
                                                py: 0.75,
                                                fontSize: "0.9rem",
                                                "&:hover": {
                                                    backgroundColor: "rgba(0,0,0,0.04)",
                                                }
                                            }}
                                        >
                                            {`${countryToFlag(code)} ${value}`}
                                        </MenuItem>
                                    );
                                })}
                            </Select>
                            <InputBase
                                name="phone"
                                id="leadPhone"
                                sx={{
                                    borderRadius: "8px",
                                    height: { xs: "40px", sm: "42px" },
                                    fontSize: { xs: "0.9rem", sm: "0.95rem" },
                                    width: "100%",
                                    color: "#333",
                                    padding: "8px 12px",
                                    transition: "all 0.2s ease",
                                    backgroundColor: "rgba(0,0,0,0.02)",
                                    border: errors["phone"] ? "1px solid #d32f2f" : "1px solid rgba(0,0,0,0.08)",
                                    "& .MuiInputBase-input": {
                                        height: "100%",
                                        padding: "0 14px",
                                        display: "flex",
                                        alignItems: "center",
                                    },
                                    "&:hover": {
                                        backgroundColor: "rgba(0,0,0,0.03)",
                                        border: errors["phone"] ? "1px solid #d32f2f" : "1px solid rgba(0,0,0,0.12)",
                                    },
                                    "&:focus-within": {
                                        backgroundColor: "#fff",
                                        border: errors["phone"] ? "1px solid #d32f2f" : `1px solid ${theme.palette.primary.main}`,
                                        boxShadow: errors["phone"] ? "none" : `0 0 0 2px ${theme.palette.primary.main}10`,
                                    }
                                }}
                                type="tel"
                                value={input["phone"] || ""}
                                onChange={handleInputChange}
                                placeholder="Enter your phone number"
                                inputProps={{
                                    "aria-label": "enter your phone",
                                    style: { height: "100%", padding: "0" }
                                }}
                            />
                        </Box>
                        {Boolean(errors["phone"]) && (
                            <FormHelperText
                                error={true}
                                sx={{
                                    ml: 0.5,
                                    mt: 0.5,
                                    fontSize: "0.7rem",
                                }}
                            >
                                {errors["phone"]}
                            </FormHelperText>
                        )}
                    </FormControl>

                    {/* Submit Button */}
                    <Button
                        id="leadGenSubmit"
                        variant="contained"
                        color="primary"
                        sx={{
                            textTransform: "none",
                            padding: "10px 0",
                            fontSize: { xs: "0.9rem", sm: "0.95rem" },
                            fontWeight: 500,
                            width: "100%",
                            borderRadius: "8px",
                            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                            mt: 1.5,
                            transition: "all 0.3s ease",
                            "&:hover": {
                                boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                                transform: "translateY(-2px)",
                            }
                        }}
                        onClick={handleLeadGeneration}
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <CircularProgress size={20} sx={{ color: "white" }} />
                        ) : (
                            "Submit"
                        )}
                    </Button>
                </Box>
            </DialogContent>
        </Dialog>
    );
}

export default ModalForm;