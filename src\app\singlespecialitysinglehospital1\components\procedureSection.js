"use client";

import { useState, useEffect, useRef, useContext } from "react";
import { Box, Typography,CircularProgress } from "@mui/material";
import { useTheme } from "@emotion/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import ProcedureCard from "./procedureCard";
import SectionLayoutOasis from "@/app/oasis/styledComponents/SectionLayoutOasis";
import { getProcedures } from "@/api/harbor.service";
import { AppContext } from "@/app/AppContextLayout";


const ProcedureSection = () => {
  const [procedures, setProcedures] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const swiperRef = useRef(null);
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const theme = useTheme()

  useEffect(() => {
    const getProcedureItems = async () => {
      setIsLoading(true);
      try { 
        const data = await getProcedures(enterpriseCode);
        // console.log(data?.result.procedures);
        setProcedures(data?.result.procedures || []);
      } catch (error) {
        setViewSnackbarMain({
          message: "Something went wrong!",
          type: "error",
        });
      } finally {
        setIsLoading(false);
      }
    };
    if (enterpriseCode) getProcedureItems();
  }, [enterpriseCode, setViewSnackbarMain]);
  
  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper && !isLoading) {
      setTimeout(() => {
        swiperRef.current.swiper.update();
        swiperRef.current.swiper.autoplay.start();
      }, 0);
    }
  }, [procedures, isLoading]);

  return (
    <Box sx={{
      // background: `
      //   linear-gradient(
      //     180deg,
      //     ${theme.palette.primary.main}20 0%,
      //     rgba(255, 255, 255, 1) 50%,
      //     ${theme.palette.secondary.main}20 100%
      //   )
      // `,
      backgroundColor:"#f8f8f8",
    }}>
      {procedures && procedures.length > 0 && <SectionLayoutOasis>
        <Box
          component="section"
          sx={{
            position: "relative",
            py: { xs: 4, md: 0 },
            px: { xs: 2, md: 0 },
            maxWidth: "2150px",
            mx: "auto",
            width: "100%",
          }}
        >
          {/* Section Heading */}
       <Box 
            sx={{ 
              textAlign: "center", 
              mb: { xs: 4, sm: 5, md: 3 },
              maxWidth: "800px",
              mx: "auto"
            }}
          >
            <Typography
              variant="h5"
              sx={{
                color: "primary.main",
                mb: 1,
                display: "block",
                fontSize: { xs: "1.125rem", md: "1.25rem" },
              }}
            >
              OUR PROCEDURES
            </Typography>
            <Typography
              variant="h3"
              sx={{
                color: "#1a1a1a",
                mb: 2,
                fontSize: { xs: "1.875rem", sm: "2.25rem", md: "2.5rem" },
                lineHeight: 1.2,
              }}
            >
              {/* Comprehensive Eye Treatment Procedures */}
              Comprehensive Eye Care
            </Typography>
          </Box>
      

          {/* Swiper Section */}

          <Box
            // ref={swiperRef}
            sx={{
              mt: 2,
              px: { xs: 1, sm: 2, md: 0 },
              width: "100%",
              // opacity: swiperVisible ? 1 : 0,
              // transform: swiperVisible ? "translateY(0)" : "translateY(40px)",
              transition: "opacity 0.8s ease-out, transform 0.8s ease-out",
            }}
          >
           {isLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                <CircularProgress />
              </Box>
            ) : procedures.length > 0 ? (
              <Swiper
                ref={swiperRef}
                modules={[Pagination, Autoplay]}
                spaceBetween={20}
                slidesPerView={1}
                loop={true}
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true,
                }}
                pagination={{
                  clickable: true,
                  dynamicBullets: true,
                }}
                breakpoints={{
                  600: { slidesPerView: 2 },
                  1024: { slidesPerView: 4 },
                }}
                style={{ padding: "10px 0 50px 0", width: "100%" }}
              >
                {procedures.map((procedure) => (
                  <SwiperSlide style={{marginRight:0}} key={procedure.id}>
                    <div style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center'
                }}>
                    <ProcedureCard
                      title={procedure.displayName}
                      icon={procedure.bannerUrl || procedure.speciality.bannerUrl}
                      description={procedure.shortDescription}
                      procedureCode={procedure.code}
                      seoSlug={procedure.seoSlug}
                      specialitySlug={procedure.speciality.seoSlug}
                    />
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            ) : (
              <Typography>No procedures available.</Typography>
            )}
          </Box>
        </Box>
      </SectionLayoutOasis>}
      
    </Box>
  );
};

export default ProcedureSection;
