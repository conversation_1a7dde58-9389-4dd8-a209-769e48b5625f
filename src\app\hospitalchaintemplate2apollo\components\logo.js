"use client";

import { AppContext } from "@/app/AppContextLayout";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useContext } from "react";

const Logo = () => {
  const { websiteData } = useContext(AppContext);
  const { logo_url: logoUrl = "" } = websiteData || {};

  return (
    <img
      alt="logo"
      src={getThumborUrl(logoUrl)}
      style={{
        width: "135px",
        height: "40px",
        objectFit: "contain",
      }}
    />
  );
};

export default Logo;
