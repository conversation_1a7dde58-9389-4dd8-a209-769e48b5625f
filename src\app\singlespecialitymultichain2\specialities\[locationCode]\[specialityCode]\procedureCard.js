import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useTheme } from "@emotion/react";
import { Box, Typography } from "@mui/material";
import { alpha } from "@mui/material/styles";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";

const ProcedureCard = ({ procedure = {}, id = null }) => {
  const theme = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  const {
    code: procedureCode = null,
    displayName = "",
    iconUrl = "",
    bannerUrl = "",
    seoSlug = ""
  } = procedure || {};

  const handleProcedureClick = () => {
    router.push(`${pathname}/procedures/${seoSlug}`);
  };

  return (
    <Box
      id={id}
      style={{
        height: "200px",
        position: "relative",
        boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
        borderRadius: "8px",
        overflow: "hidden",
        cursor: "pointer",
      }}
      onClick={handleProcedureClick}
    >
      <Image
        alt="procedure"
        src={bannerUrl ? getThumborUrl(bannerUrl, 0, 0) : "/default-procedure.jpg"}
        fill
        style={{
          objectPosition: "center",
          // filter: "blur(1px)",
          objectFit: "cover",
        }}
      />
      {/* <Box sx={{height: "100%", width: "100%", bgcolor: "primary.main"}}></Box> */}
      <Box
        sx={{
          border: "1px solid #fff",
          background: "rgba(255, 255, 255, .5)",
          padding: "4px 8px",
          borderRadius: "4px",
          position: "absolute",
          left: "16px",
          bottom: "16px",
          right: "16px"
        }}
      >
        <Typography variant="subtitle2" sx={{ fontSize: "20px" }}>
          {displayName || ""}
        </Typography>
      </Box>
    </Box>
  );
};

export default ProcedureCard;
