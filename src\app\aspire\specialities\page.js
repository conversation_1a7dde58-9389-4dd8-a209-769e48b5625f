"use client";

import Box from "@mui/material/Box";
import SectionLayoutAspire from "../styledComponents/SectionLayoutAspire";
import FaqsSection from "../components/faqsSection";
import {
  FormControl,
  InputBase,
  InputLabel,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  Skeleton,
  Typography,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import SpecialityCard from "./SpecialityCard";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import { useContext, useEffect, useState } from "react";
import { AppContext } from "../../AppContextLayout";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function Specialities() {
  const router = useRouter();
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const [specialities, setSpecialities] = useState([]);
  const [searchInput, setSearchInput] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [filteredSpecialities, setFilteredSpecialities] = useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const { enterprise_code: enterpriseCode = null, centers = [] } =
    websiteData || {};

  const getSpecialities = async (locationCode) => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${locationCode || enterpriseCode}/${API_ENDPOINT_SPECIALITY}?list=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { specialities = [] } = result || {};
        setSpecialities(specialities);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationChange = (event) => {
    const value = event.target.value;
    setSelectedLocation(value);
    getSpecialities(value);
  };

  const handleSpecialitySearch = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    const searchedSpecialities = specialities.filter((speciality) => {
      const { displayName = "" } = speciality || {};
      return displayName.toLowerCase().includes(value.toLowerCase());
    });
    setFilteredSpecialities(searchedSpecialities);
  };

  // const handleSpecialityRedirection = (code) => {
  //   router.push(`/specialities/${code}`);
  // };

  // const handleBlur = () => {
  //   setTimeout(() => {
  //     setFilteredSpecialities([]);
  //   }, 200);
  // };

  useEffect(() => {
    if (enterpriseCode) {
      getSpecialities();
    }
  }, [enterpriseCode]);

  useEffect(() => {
    setFilteredSpecialities(specialities);
  }, [specialities]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayoutAspire>
        <Typography variant="h4">All Specialities</Typography>
        <Box
          sx={{ display: "flex", flexDirection: "column", gap: "48px", mt: 2 }}
        >
          <Box sx={{ display: "flex", gap: "24px" }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                position: "relative",
                width: { xs: "100%", sm: "400px" },
              }}
              // onBlur={handleBlur}
            >
              <Box
                sx={{
                  backgroundColor: "#fff",
                  border: "1px solid #d3d4db",
                  padding: "8px 12px",
                  borderRadius: "6px",
                  display: "flex",
                  alignItems: "center",
                  gap: "16px",
                  width: { xs: "100%" },
                }}
              >
                <InputBase
                  placeholder="Search speciality or condition"
                  value={searchInput || ""}
                  onChange={handleSpecialitySearch}
                  sx={{ width: { xs: "100%" } }}
                />
                <SearchIcon />
              </Box>
              <Box
                sx={{
                  padding: "0 4px",
                  background: "#fff",
                  position: "absolute",
                  width: "100%",
                  maxHeight: "400px",
                  overflowY: "auto",
                  boxShadow: "0 2px 20px rgba(0, 0, 0, .1)",
                  top: "50px",
                }}
              >
                {/* <Box>
                  {filteredSpecialities.length > 0 && (
                    <List
                      subheader={
                        <ListSubheader
                          component="div"
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                          }}
                        >
                          Specialities
                        </ListSubheader>
                      }
                    >
                      {filteredSpecialities.map((speciality) => {
                        const {
                          displayName = "",
                          code = null,
                          seoSlug = "",
                        } = speciality || {};
                        return (
                          <ListItemButton
                            key={code}
                            onClick={() => handleSpecialityRedirection(seoSlug)}
                          >
                            <ListItemIcon sx={{ minWidth: "32px", mr: 2 }}>
                              <Image
                                alt="speciality-icon"
                                src="/speciality-icon.svg"
                                height={32}
                                width={32}
                              />
                            </ListItemIcon>
                            <ListItemText primary={displayName} />
                          </ListItemButton>
                        );
                      })}
                    </List>
                  )}
                </Box> */}
              </Box>
            </Box>
            <FormControl
              sx={{
                width: { xs: "100%", sm: "366px" },
              }}
            >
              <InputLabel id="filter-by-location">
                Filter By Location
              </InputLabel>
              <Select
                labelId="filter-by-location"
                id="demo-simple-select"
                value={selectedLocation}
                label="Filter By Location"
                onChange={handleLocationChange}
                // input={<SpecialityFilterInput />}
              >
                <MenuItem value={enterpriseCode}>All</MenuItem>
                {centers.map((center) => {
                  const { code, name = "", area = {} } = center || {};
                  const { name: areaName = "" } = area || {};
                  return (
                    <MenuItem key={code} value={code}>
                      {`${name || ""} - ${areaName || ""}`}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
          <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  lg: "repeat(3, 1fr)",
                },
                columnGap: "32px",
                rowGap: "64px",
              }}
            >
              {isLoading ? (
                [1, 2, 3, 4, 5, 6].map((_, index) => {
                  return (
                    <Skeleton
                      key={index}
                      variant="rounded"
                      animation="wave"
                      height={200}
                    />
                  );
                })
              ) : filteredSpecialities.length > 0 ? (
                filteredSpecialities.map((speciality, index) => {
                  const { code = null, centers } = speciality || {};
                  return (
                    <SpecialityCard
                      id={`speciality${index}`}
                      key={code}
                      speciality={speciality}
                      centers={centers}
                      selectedLocation={selectedLocation}
                      chainCode={enterpriseCode}
                    />
                  );
                })
              ) : (
                <Typography variant="h4" align="center">
                  No Specialities Available
                </Typography>
              )}
            </Box>
            {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Pagination count={10} variant="outlined" shape="rounded" />
            </Box> */}
          </Box>
        </Box>
      </SectionLayoutAspire>
      {/* <FaqsSection /> */}
    </Box>
  );
}
