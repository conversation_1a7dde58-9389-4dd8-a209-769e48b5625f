"use client";

import { Box, Rating, Typography } from "@mui/material";
import useStyles from "../styles";
import { getInitials } from "../../utils/getInitials";

const ReviewCard = ({ backgroundColor, testimonial = {} }) => {
  const classes = useStyles();
  const { name = "", rating = null, text = "" } = testimonial || {};

  return (
    <Box className={classes.reviewsSectionCarousalBoxItem}>
      <Box className={classes.reviewsSectionContentBox}>
        <Box className={classes.reviewsSectionReviewBox}>
          <Rating name="simple-controlled" value={rating} readOnly precision={0.5} />
          <Typography
            variant="body1"
            className={classes.reviewsSectionReviewBoxText}
          >
            {text || ""}
          </Typography>
        </Box>
      </Box>
      <Box className={classes.reviewsSectionProfileBox}>
        <Box className={classes.reviewsSectionProfileImg}>
          {getInitials(name)}
        </Box>
        <Box className={classes.reviewsSectionProfileDetailsBox}>
          <Typography className={classes.reviewsSectionProfileName}>
            {name || ""}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default ReviewCard;
