export const getEnterpriseCode = async (customDomain) => {
    const domainName = headers().get("host");
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${customDomain || "hospital-chain-staging.mydocsite.com"}&websiteOnly=true`;
    try {
        const res = await fetch(url,{
        cache: "no-store"
        });
        const jsonRes = await res.json();
        const { result = {} } = jsonRes || {};
        const { enterprise_code: enterpriseCode = null } = result || {};
        return enterpriseCode;
    } catch (error) {
        console.log("getWebsiteData", error);
    }
}