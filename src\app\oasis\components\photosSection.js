"use client";

import { Box, Typography } from "@mui/material";
import SectionLayout from "../styledComponents/SectionLayout";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useTheme } from "@emotion/react";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import { getThumborUrl } from "../../utils/getThumborUrl";
import SectionHeading from "./sectionHeading";
import PhotoCard from "./photoCard";
import SectionLayoutOasis from "../styledComponents/SectionLayoutOasis";

export const NextArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <ArrowForwardIosIcon
      className={className}
      onClick={onClick}
      sx={{
        color: "black",
        ...style,
        "&:hover": { color: "black" },
        display: { xs: "none", sm: "block" },
      }}
    />
  );
};

export const PrevArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <ArrowBackIosIcon
      className={className}
      onClick={onClick}
      sx={{
        color: "black",
        ...style,
        "&:hover": { color: "black" },
        display: { xs: "none", sm: "block" },
      }}
    />
  );
};

const PhotosSection = ({ multiMedia }) => {
  const theme = useTheme();
  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const photos = multiMedia.filter((media) => media.image_url) || [];

  return (
    <SectionLayoutOasis>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
        <SectionHeading>Healthcare Moments</SectionHeading>
        <Slider {...settings}>
          {photos.map((photo) => {
            const { image_url: imgUrl = "", code = "" } = photo || {};
            return <PhotoCard code={code} imgUrl={imgUrl} />;
          })}
        </Slider>
      </Box>
    </SectionLayoutOasis>
  );
};

export default PhotosSection;
