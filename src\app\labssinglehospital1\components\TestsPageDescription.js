'use client'
import { Box, Container, Typography, Paper } from '@mui/material';
import { useState, useEffect } from 'react';

const TestsPageDescription = ({ categories, selectedCategory }) => {
  const [description, setDescription] = useState('');

  useEffect(() => {
    // Find the selected category in the categories data
    if (categories?.data && selectedCategory && selectedCategory !== 'all') {
      const category = categories.data.find(cat =>
        cat.seoSlug === selectedCategory || cat.code === selectedCategory
      );

      if (category && category.description) {
        setDescription(category.description);
      } else {
        // Try to find the category in the URL parameters
        const categoryCode = selectedCategory.split('-').pop();

        // Look for the category again with just the code
        const categoryByCode = categories.data.find(cat => cat.code === categoryCode);
        if (categoryByCode && categoryByCode.description) {
          setDescription(categoryByCode.description);
        } else {
          setDescription('<p>This category offers a range of diagnostic tests designed to provide comprehensive health insights. Our tests are conducted using state-of-the-art equipment and analyzed by experienced professionals to ensure accurate results.</p><p>Please select a specific test to learn more about preparation instructions, sample requirements, and expected turnaround time.</p>');
        }
      }
    } else {
      // Default description for 'all' category
      setDescription('<p>Our comprehensive range of diagnostic tests and health packages are designed to provide you with accurate insights about your health. Each test is conducted with precision using state-of-the-art equipment and analyzed by experienced professionals.</p><p>Browse through our collection to find the tests that best suit your health monitoring needs.</p>');
    }
  }, [categories, selectedCategory]);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Paper
        elevation={0}
        sx={{
          p: 3,
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
        }}
      >
        <Typography
          variant="h5"
          component="h2"
          sx={{
            fontWeight: 'bold',
            mb: 2,
            color: 'text.black',
          }}
        >
          {selectedCategory !== 'all' ? (() => {
            // Try to find the category in the categories data
            if (categories?.data) {
              const category = categories.data.find(cat =>
                cat.seoSlug === selectedCategory || cat.code === selectedCategory
              );

              if (category) {
                return `About ${category.name}`;
              }

              // Try with just the code
              const categoryCode = selectedCategory.split('-').pop();
              const categoryByCode = categories.data.find(cat => cat.code === categoryCode);

              if (categoryByCode) {
                return `About ${categoryByCode.name}`;
              }
            }

            return 'About This Category';
          })() : 'About Our Tests & Packages'}
        </Typography>

        <Box
          className="ck-content"
          sx={{
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
            wordBreak: 'break-word',
            color: 'text.black',
            maxWidth: '100%'
          }}
          dangerouslySetInnerHTML={{ __html: description }}
        />
      </Paper>
    </Container>
  );
};

export default TestsPageDescription;
