"use client";

import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Rating from "@mui/material/Rating";
import useStyles from "../styles";
import { getInitials } from "../../utils/getInitials";

const ReviewBox = ({ name = "", rating = "", text = "", key }) => {
  const classes = useStyles();
  return (
    <Box key={key}>
      <Box className={classes.reviewsSectionCarousalBoxItem}>
        <Box className={classes.reviewsSectionContentBox}>
          <Box className={classes.reviewsSectionReviewBox}>
            <Rating name="simple-controlled" value={rating} readOnly />
            <Typography
              variant="body1"
              className={classes.reviewsSectionReviewBoxText}
            >
              {text || ""}
            </Typography>
          </Box>
        </Box>
        <Box className={classes.reviewsSectionProfileBox}>
          <Box className={classes.reviewsSectionProfileImg}>
            {getInitials(name) || ""}
          </Box>
          <Box className={classes.reviewsSectionProfileDetailsBox}>
            <Typography className={classes.reviewsSectionProfileName}>
              {name || ""}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ReviewBox;
