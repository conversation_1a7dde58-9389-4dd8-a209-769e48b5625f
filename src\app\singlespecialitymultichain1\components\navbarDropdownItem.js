import React, { useState } from "react";
import Box from "@mui/material/Box";
import HealthAndSafetyOutlinedIcon from "@mui/icons-material/HealthAndSafetyOutlined";
import KeyboardArrowDownOutlinedIcon from "@mui/icons-material/KeyboardArrowDownOutlined";
import { useRouter } from "next/navigation";
import {Typography} from "@mui/material";

const NavbarDropdownItem = ({ section = {}, setAnchorEl, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const {
    displayName = "",
    sections = [],
    redirection = {},
    type = 1,
  } = section || {};

  const handleClick = () => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setIsOpen((prev) => !prev);
  };

  return (
      <Box>
          {/* Main Item */}
          <Box
              sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "8px 16px",
                  paddingRight: "48px",
                  fontSize: "14px",
                  cursor: "pointer",
                  color: "#333333",
                  transition: "all 0.3s",
                  "&:hover": { color: "primary.main", backgroundColor: "#f5f5f5" },
                  borderBottom: sections?.length && isOpen ? "1px solid #e0e0e0" : "none",
              }}
              onClick={handleClick}
          >
              <Box sx={{ display: "flex", alignItems: "center", gap: "0px" }}>
                  <Typography fontSize="16px">{displayName || ""}</Typography>
              </Box>
              {sections?.length > 0 && <KeyboardArrowDownOutlinedIcon />}
          </Box>

          {/* Dropdown Section */}
          {sections?.length > 0 && isOpen && (
              <Box
                  sx={{
                      display: "flex",
                      flexDirection: "column",
                      backgroundColor: "#ffffff",
                      boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                      borderLeft: "1px solid #e0e0e0",
                      marginLeft: "16px", // Indent dropdown items for better hierarchy
                  }}
              >
                  {sections.map((section, index) => {
                      const { displayName = "" } = section || {};
                      return (
                          <NavbarDropdownItem
                              key={`${displayName}${index}`}
                              section={section}
                              setAnchorEl={setAnchorEl}
                              isDrawerOpen={isDrawerOpen}
                              handleCloseDrawer={handleCloseDrawer}
                          />
                      );
                  })}
              </Box>
          )}
      </Box>
  );
};

export default NavbarDropdownItem;
