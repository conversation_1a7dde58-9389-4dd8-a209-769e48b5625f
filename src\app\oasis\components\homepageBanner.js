import { InputBase, Typography, Box } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import SectionLayout from "../styledComponents/SectionLayout";
import Image from "next/image";
import { getThumborUrl } from "../../utils/getThumborUrl";
import HomepageSearch from "./homepageSearch";
import FeaturedSpecialities from "./featuredSpecialities";

const HomepageBanner = ({ banners = [] }) => {
  const { image_url: bannerImg = "", title = "", text = "" } = banners[0] || {};
  return (
    <div
      style={{
        position: "relative",
        minHeight: "400px",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <Image
        alt={title || "banner"}
        src={bannerImg ? getThumborUrl(bannerImg) : "/banner.webp"}
        fill
        priority
        sizes="100vw"
        style={{
          width: "100%",
          height: "100%",
          // filter: "blur(1px)",
          objectFit: "cover",
          zIndex: -1,
        }}
      />
      <Box
        sx={{
          // position: "absolute",
          // bottom: "50%",
          // left: "50%",
          // transform: "translate(-50%, 50%)",
          width: { xs: "100%" },
          zIndex: 1,
        }}
      >
        <SectionLayout
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "32px",
            alignItems: "center",
            background: "transparent",
          }}
        >
          <Box
            sx={{
              // width: "fit-content",
              padding: "16px",
              background: "rgba(255,255,255,.5)",
              borderRadius: "8px",
              margin: "auto",
              border: "1px solid #fff",
              width: { xs: "100%", md: "75%" },
            }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{ fontWeight: "500", fontSize: { xs: "2rem", sm: "3rem" } }}
            >
              {title || ""}
            </Typography>
          </Box>
          <Box
            sx={{
              maxWidth: "900px",
              width: "100%",
              display: "flex",
              // jusitfyContent: "center",
              flexDirection: "column",
              // alignItems: "center",
              padding: "24px",
              borderRadius: "10px",
              gap: "32px",
            }}
          >
            <Box
              sx={{
                background: "rgba(255,255,255,1)",
                width: "100%",
                padding: "32px",
                boxShadow: "0 1px 6px rgba(0,0,0,.15)",
                borderRadius: "10px"
              }}
            >
              <HomepageSearch />
            </Box>
            <FeaturedSpecialities />
          </Box>
        </SectionLayout>
      </Box>
    </div>
  );
};

export default HomepageBanner;
