"use client";
import { useContext } from "react";
import {
  alpha,
  Box,
  List,
  ListItemText,
  ListItem,
  Typography,
} from "@mui/material";
import { useTheme } from "@emotion/react";
import { AppContext } from "@/app/AppContextLayout";
import SectionLayout from "../components/SectionLayout";

const TermsAndConditions = () => {
  const theme = useTheme();
  const { websiteData } = useContext(AppContext);
  const { domainName, centers = [], enterpriseName = "" } = websiteData;
  const websiteUrl = `https://${domainName}`;

  return (
    <Box sx={{ display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          position: "relative",
          minHeight: { xs: "120px", md: "200px" },
          bgcolor: alpha(theme.palette.primary.main, 0.7),
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "16px",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h3"
            align="center"
            sx={{ color: "#fff", fontSize: "40px" }}
          >
            Terms and Conditions
          </Typography>
        </Box>
      </Box>

      <SectionLayout>
        <Box>
          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Welcome to {enterpriseName}!{" "}
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            These terms and conditions outline the rules and regulations for the
            use of {enterpriseName}’s Website, located at {websiteUrl}. By
            accessing this website, we assume you accept these terms and
            conditions. Do not continue to use {enterpriseName} if you do not
            agree to take all of the terms and conditions stated on this page.
          </Typography>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Terminology
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "8px" }}
          >
            The following terminology applies to these Terms and Conditions,
            Privacy Statement, Disclaimer Notice, and all Agreements:
          </Typography>

          <List>
            <ListItem>
              <ListItemText
                primary={`1. "Client", "You", and "Your" refers to you, the person logging on this website and compliant with the Company's terms and conditions.`}
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={`2. "The Company", "Ourselves", "We", "Our", and "Us" refers to ${enterpriseName}.`}
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={`3. "Party", "Parties", or "Us" refers to both the Client and ourselves.`}
              />
            </ListItem>
          </List>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            All terms refer to the offer, acceptance, and consideration of
            payment necessary to undertake the process of our assistance to the
            Client in the most appropriate manner for the express purpose of
            meeting the Client's needs in respect of provision of the Company’s
            stated services, in accordance with and subject to prevailing law.
          </Typography>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Cookies
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            We employ the use of cookies. By accessing {enterpriseName}, you
            agree to use cookies in accordance with {enterpriseName}'s Privacy
            Policy. Most interactive websites use cookies to let us retrieve the
            user's details for each visit. Cookies are used by our website to
            enable functionality in certain areas.
          </Typography>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            License
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "8px" }}
          >
            Unless otherwise stated, {enterpriseName} and/or its licensors own
            the intellectual property rights for all material on{" "}
            {enterpriseName}. All intellectual property rights are reserved. You
            may access this from {enterpriseName} for your own personal use
            subject to restrictions set in these terms and conditions. You must
            not:
          </Typography>
          <List>
            <ListItem>
              <ListItemText
                primary={`1. Republish material from ${enterpriseName}`}
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={`2. Sell, rent or sub-license material from ${enterpriseName}`}
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={`3. Reproduce, duplicate or copy material from ${enterpriseName}`}
              />
            </ListItem>

            <ListItem>
              <ListItemText
                primary={`4. Redistribute content from ${enterpriseName}`}
              />
            </ListItem>
          </List>
          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            Parts of this website offer an opportunity for users to post and
            exchange opinions and information in certain areas. {enterpriseName}{" "}
            does not filter, edit, publish or review Comments prior to their
            presence on the website. Comments reflect the views of the person
            who posts them.
          </Typography>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Hyperlinking to Our Content
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "8px" }}
          >
            The following organizations may link to our Website without prior
            written approval:
          </Typography>

          <List sx={{ marginBottom: "32px" }}>
            <ListItem>
              <ListItemText primary={`1. Government agencies`} />
            </ListItem>
            <ListItem>
              <ListItemText primary={`2. Search engines`} />
            </ListItem>
            <ListItem>
              <ListItemText primary={`3. News organizations`} />
            </ListItem>

            <ListItem>
              <ListItemText primary={`4. Online directory distributors`} />
            </ListItem>
          </List>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Content Liability
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            We shall not be held responsible for any content that appears on
            your website. You agree to protect and defend us against all claims
            arising on your website.
          </Typography>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Reservation of Rights
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            We reserve the right to request that you remove all links or any
            particular link to our website. You agree to immediately remove all
            links upon request.
          </Typography>

          <Typography
            variant="h5"
            align="start"
            sx={{ color: "#333", marginBottom: "16px" }}
          >
            Disclaimer
          </Typography>

          <Typography
            variant="body1"
            align="start"
            sx={{ color: "#333", marginBottom: "32px" }}
          >
            To the maximum extent permitted by applicable law, we exclude all
            representations, warranties, and conditions relating to our website
            and its use. Nothing in this disclaimer will limit or exclude our or
            your liability for death or personal injury; limit or exclude our or
            your liability for fraud; or exclude any liabilities that may not be
            excluded under applicable law. This template allows you to replace
            placeholders such as {enterpriseName}, {enterpriseName}, and{" "}
            {websiteUrl} with specific details relevant to each client, ensuring
            a personalized approach while maintaining legal integrity.
          </Typography>
        </Box>
      </SectionLayout>
    </Box>
  );
};
export default TermsAndConditions;
