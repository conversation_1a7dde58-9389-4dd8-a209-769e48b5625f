"use client";

import { Box, Typography, Container, Paper } from "@mui/material";
import { useState, useEffect } from "react";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import { keyframes } from "@emotion/react";

// Animation keyframes
const pulse = keyframes`
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7); }
  70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255, 255, 255, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
`;

// Helper function to get YouTube thumbnail
const getThumbnailUrl = (videoId) => {
  if (!videoId) return null;
  return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
};

const Video = ({ value = [], title = "" }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [videoData, setVideoData] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Get the first video from the array
    if (value && value.length > 0) {
      setVideoData(value[0]);
    }

    // Add a slight delay for the animation
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 300);

    return () => clearTimeout(timer);
  }, [value]);

  const handleVideoClick = () => {
    setIsPlaying(true);
  };

  if (!videoData) return null;

  const { videoUrl = "", title: videoTitle = "" } = videoData;

  return (
    <Box
      sx={{
        backgroundColor: 'primary.main',
        py: { xs: 4, md: 4 },
        px: { xs: 2, md: 4 },
        overflow: 'hidden',
        position: 'relative',
      }}
    >
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        <Box
          sx={{
            textAlign: 'center',
            mb: { xs: 4, md: 4 },
            opacity: isLoaded ? 1 : 0,
            transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'opacity 0.6s ease, transform 0.6s ease',
          }}
        >
          <Typography
            variant="h3"
            sx={{
              color: '#fff',
              fontSize: { xs: "1.75rem", sm: "2.25rem", md: "2.5rem" },
              fontWeight: '400',
              position: 'relative',
              mb: 3,
              display: 'inline-block',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '-12px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80px',
                height: '3px',
                background: '#fff',
                borderRadius: '3px',
              }
            }}
          >
            {title || "Featured Video"}
          </Typography>
        </Box>

        {/* Video Title
        {videoTitle && (
          <Box
            sx={{
              mb: 3,
              opacity: isLoaded ? 1 : 0,
              transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
              transition: 'opacity 0.6s ease, transform 0.6s ease',
              transitionDelay: '0.1s',
              textAlign: 'center',
            }}
          >
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#002147',
                display: 'inline-block',
                position: 'relative',
                px: 2,
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '40px',
                  height: '3px',
                  backgroundColor: 'primary.main',
                  borderRadius: '3px',
                }
              }}
            >
              {videoTitle}
            </Typography>
          </Box>
        )} */}

        {/* Video Player */}
        <Box
          sx={{
            width: '100%',
            maxWidth: '900px',
            mx: 'auto',
            position: 'relative',
            opacity: isLoaded ? 1 : 0,
            transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'opacity 0.8s ease, transform 0.8s ease',
            transitionDelay: '0.2s',
          }}
        >
          <Paper
            elevation={0}
            sx={{
              borderRadius: '16px',
              overflow: 'hidden',
              boxShadow: '0 16px 40px rgba(0, 0, 0, 0.15)',
              position: 'relative',
              backgroundColor: '#000',
              // border: '1px solid rgba(0,0,0,0.05)',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '2px',
                background: 'linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.02) 50%, rgba(255,255,255,0) 100%)',
                zIndex: 2,
              }
            }}
          >
            {isPlaying ? (
              <Box
                sx={{
                  position: "relative",
                  paddingTop: "56.25%", // 16:9 aspect ratio
                  width: "100%",
                }}
              >
                <Box
                  component="iframe"
                  src={`https://www.youtube.com/embed/${videoUrl}?autoplay=1&mute=0&rel=0&showinfo=0&controls=1`}
                  style={{ border: 0 }}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    border: 0,
                  }}
                />
              </Box>
            ) : (
              <Box
                onClick={handleVideoClick}
                sx={{
                  position: "relative",
                  cursor: "pointer",
                  paddingTop: "56.25%", // 16:9 aspect ratio
                  width: "100%",
                  "&:hover": {
                    "& .play-overlay": {
                      background: "rgba(0, 0, 0, 0.2)",
                    },
                    "& .play-button": {
                      animation: `${pulse} 1.5s infinite`,
                    },
                    "& img": {
                      transform: "scale(1.03)"
                    }
                  },
                }}
              >
                <Box
                  component="img"
                  src={getThumbnailUrl(videoUrl)}
                  alt="Video Thumbnail"
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    transition: "transform 0.5s ease-in-out",
                  }}
                  onError={(e) => {
                    // Fallback to a default image if thumbnail fails to load
                    e.target.src = "https://via.placeholder.com/1280x720?text=Featured+Video";
                  }}
                />
                <Box
                  className="play-overlay"
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    background: "rgba(0, 0, 0, 0.18)",
                    transition: "background 0.3s ease",
                  }}
                >
                  <Box
                    className="play-button"
                    sx={{
                      width: { xs: 60, md: 80 },
                      height: { xs: 60, md: 80 },
                      borderRadius: "50%",
                      background: "rgba(255, 255, 255, 0.9)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      transition: "transform 0.3s ease",
                      // boxShadow: "0 0 0 8px rgba(255, 255, 255, 0.2)",
                    }}
                  >
                    <PlayArrowIcon
                      sx={{
                        fontSize: { xs: 36, md: 48 },
                        color: "primary.main",
                        marginLeft: "5px"
                      }}
                    />
                  </Box>
                </Box>

                {/* Watch Now Label */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 20,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    color: 'white',
                    py: 1,
                    px: 3,
                    borderRadius: '30px',
                    fontWeight: '400',
                    fontSize: { xs: '0.9rem', md: '1rem' },
                    letterSpacing: '1px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                    transition: 'transform 0.3s ease',
                    '&:hover': {
                      transform: 'translateX(-50%) translateY(-3px)',
                    }
                  }}
                >
                  WATCH NOW
                </Box>
              </Box>
            )}
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default Video;

// "use client";

// import { Box, Typography, Container } from "@mui/material";
// import { useState, useRef } from "react";
// import PlayArrowIcon from "@mui/icons-material/PlayArrow";
// import { Swiper, SwiperSlide } from 'swiper/react';
// import { Pagination, Autoplay } from 'swiper/modules';
// import 'swiper/css';
// import 'swiper/css/pagination';
// import { keyframes } from "@emotion/react";

// // Animation keyframes
// const fadeIn = keyframes`
//   from { opacity: 0; transform: translateY(10px); }
//   to { opacity: 1; transform: translateY(0); }
// `;

// // Helper function to get YouTube thumbnail
// const getThumbnailUrl = (videoId) => {
//   if (!videoId) return null;
//   return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
// };

// const Video = ({value=[], title=""}) => {
//   const [playingVideoId, setPlayingVideoId] = useState(null);
//   const swiperRef = useRef(null);

//   const handleVideoClick = (videoId) => {
//     setPlayingVideoId(videoId);

//     // Stop the swiper autoplay when a video is playing
//     if (swiperRef.current && swiperRef.current.swiper) {
//       swiperRef.current.swiper.autoplay.stop();
//     }
//   };

//   return (
//     <Box
//       sx={{
//         backgroundColor: '#f8f8f8',
//         py: { xs: 6, md: 4 },
//         px: { xs: 2, md: 4 },
//         overflow: 'hidden',
//         position: 'relative',
//       }}
//     >
//       <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
//         <Box sx={{ textAlign: 'center', mb: 5 }}>
//           <Typography
//             variant="h3"
//             sx={{
//               color: 'primary.main',
//               fontSize: { xs: "1.75rem", sm: "2.25rem", md: "2.5rem" },
//               fontWeight: 'bold',
//               position: 'relative',
//               display: 'inline-block',
//             }}
//           >
//             {title || "Health Videos"}
//           </Typography>
//         </Box>

//         <Box sx={{
//           animation: `${fadeIn} 0.8s ease-out`,
//           mt: 4
//         }}>
//           <Swiper
//             ref={swiperRef}
//             style={{ padding: "5px 5px", paddingBottom: "50px" }}
//             modules={[Autoplay, Pagination]}
//             loop={true}
//             spaceBetween={18}
//             slidesPerView={1}
//             pagination={{ dynamicBullets: true, clickable: true }}
//             autoplay={{
//               delay: 3000,
//               disableOnInteraction: true,
//               pauseOnMouseEnter: true,
//               stopOnLastSlide: false
//             }}
//             breakpoints={{
//               768: {
//                 slidesPerView: 2,
//               },
//               1024: {
//                 slidesPerView: 3,
//               },
//             }}
//           >
//             {value.map((item, index) => {
//               const {videoUrl = "", position = null } = item || {};
//               const videoId = position || `video-${index}`;
//               const isPlaying = playingVideoId === videoId;

//               return (
//                 <SwiperSlide key={videoId}>
//                   <Box
//                     sx={{
//                       height: "100%",
//                       boxShadow: "0 8px 24px rgba(0, 0, 0, 0.12)",
//                       borderRadius: "10px",
//                       overflow: "hidden",
//                       position: "relative",
//                       backgroundColor: "#000",
//                       mb: 2
//                     }}
//                   >
//                     {isPlaying ? (
//                       <Box
//                         sx={{
//                           position: "relative",
//                           paddingTop: "56.25%", // 16:9 aspect ratio
//                           width: "100%",
//                         }}
//                       >
//                         <Box
//                           sx={{
//                             position: "absolute",
//                             top: 0,
//                             left: 0,
//                             width: "100%",
//                             height: "100%",
//                           }}
//                         >
//                           <Box
//                             component="iframe"
//                             src={`https://www.youtube.com/embed/${videoUrl}?autoplay=1&mute=0&rel=0&showinfo=0&controls=1`}
//                             style={{ border: 0 }}
//                             allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
//                             allowFullScreen
//                             sx={{
//                               width: "100%",
//                               height: "100%",
//                               border: 0,
//                             }}
//                           />
//                         </Box>
//                       </Box>
//                     ) : (
//                       <Box
//                         onClick={() => handleVideoClick(videoId)}
//                         sx={{
//                           position: "relative",
//                           cursor: "pointer",
//                           paddingTop: "56.25%", // 16:9 aspect ratio
//                           width: "100%",
//                           "&:hover": {
//                             "& .play-overlay": {
//                               background: "rgba(0, 0, 0, 0.5)",
//                             },
//                             "& .play-button": {
//                               transform: "scale(1.1)",
//                             },
//                             "& img": {
//                               transform: "scale(1.05)"
//                             }
//                           },
//                         }}
//                       >
//                         <Box
//                           component="img"
//                           src={getThumbnailUrl(videoUrl)}
//                           alt={`Video ${index + 1}`}
//                           sx={{
//                             position: "absolute",
//                             top: 0,
//                             left: 0,
//                             width: "100%",
//                             height: "100%",
//                             objectFit: "cover",
//                             transition: "transform 0.3s ease-in-out",
//                           }}
//                           onError={(e) => {
//                             // Fallback to a default image if thumbnail fails to load
//                             e.target.src = "https://via.placeholder.com/640x360?text=Video";
//                           }}
//                         />
//                         <Box
//                           className="play-overlay"
//                           sx={{
//                             position: "absolute",
//                             top: 0,
//                             left: 0,
//                             right: 0,
//                             bottom: 0,
//                             display: "flex",
//                             alignItems: "center",
//                             justifyContent: "center",
//                             background: "rgba(0, 0, 0, 0.3)",
//                             transition: "background 0.3s ease",
//                           }}
//                         >
//                           <Box
//                             className="play-button"
//                             sx={{
//                               width: { xs: 50, md: 60 },
//                               height: { xs: 50, md: 60 },
//                               borderRadius: "50%",
//                               background: "rgba(255, 255, 255, 0.9)",
//                               display: "flex",
//                               alignItems: "center",
//                               justifyContent: "center",
//                               transition: "transform 0.3s ease",
//                               boxShadow: "0 4px 12px rgba(0, 0, 0, 0.2)",
//                             }}
//                           >
//                             <PlayArrowIcon
//                               sx={{
//                                 fontSize: { xs: 30, md: 40 },
//                                 color: "#002147",
//                                 marginLeft: "5px"
//                               }}
//                             />
//                           </Box>
//                         </Box>
//                       </Box>
//                     )}
//                   </Box>
//                 </SwiperSlide>
//               );
//             })}
//           </Swiper>
//         </Box>
//       </Container>
//     </Box>
//   );
// };

// export default Video;

