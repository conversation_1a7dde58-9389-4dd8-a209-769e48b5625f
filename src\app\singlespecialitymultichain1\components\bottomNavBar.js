import {BottomNavigation, BottomNavigationAction, Paper} from "@mui/material";
import Box from "@mui/material/Box";
import {useContext} from "react";
import formatForWhatsApp from "@/app/utils/phoneUtils";
import {AppContext} from "@/app/AppContextLayout";

const BottomNavBar = ({}) => {
    const {websiteData = {}} = useContext(AppContext);
    const {
        phoneNumbers = [],
    } = websiteData || {};

    const primaryWhatsappNo = phoneNumbers.filter((number) => number.is_whatsapp)[0] || {};
    const {phone: primaryWhatsapp = null} = primaryWhatsappNo || {};

    return (
        <Paper
            sx={{
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
                display: {xs: "block", md: "none"},
                zIndex: 2,
            }}
            elevation={3}>
            <BottomNavigation
                showLabels
                sx={{
                    borderTopLeftRadius: "16px",
                    borderTopRightRadius: "16px",
                }}
            >
                <BottomNavigationAction
                    id="navbarDoctors"
                    label={
                        <Box sx={{ mt: 0.5 }}>
                            Doctors
                        </Box>
                    }
                    href="/doctors"
                    sx={{
                        color: "primary.main",
                    }}
                    icon={
                        <Box
                            sx={{
                                backgroundColor: "primary.main",
                                mask: "url(/doctor.svg) no-repeat center / contain",
                                height: "30px",
                                width: "30px",
                                marginTop: "2px"
                            }}
                        />
                    }
                />
                <BottomNavigationAction
                    id="navbarBookAppointment"
                    label="Book Appt."
                    href="/contact-us"
                    sx={{
                        color: "primary.main",
                    }}
                    icon={
                        <Box
                            sx={{
                                backgroundColor: "primary.main",
                                mask: "url(/appointment_2.svg) no-repeat center / contain",
                                height: "36px",
                                width: "36px",
                            }}
                        />
                    }
                />
                <BottomNavigationAction
                    id="navbarRequestCallback"
                    label="Contact"
                    href="/contact-us"
                    sx={{
                        color: "primary.main",
                    }}
                    icon={
                        <Box
                            sx={{
                                backgroundColor: "primary.main",
                                // mask: "url(/request-call.svg) no-repeat center / contain",
                                mask: "url(/call_2.svg) no-repeat center / contain",
                                height: "36px",
                                width: "36px",
                            }}
                        ></Box>
                    }
                />
                {primaryWhatsapp &&
                    <BottomNavigationAction
                        id="navbarChat"
                        label="Chat"
                        sx={{
                            color: "primary.main",
                        }}
                        href={`https://wa.me/${formatForWhatsApp(primaryWhatsapp)}`}
                        icon={
                            <Box
                                sx={{
                                    height: "36px",
                                    width: "36px",
                                }}
                            >
                                <img src="/whatsapp.svg" alt="Chat Icon" style={{width: "100%", height: "100%"}}/>
                            </Box>
                        }
                    />
                }
            </BottomNavigation>
        </Paper>
    );
}

export default BottomNavBar;