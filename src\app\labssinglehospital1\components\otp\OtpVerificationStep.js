"use client";

import React from "react";
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Paper,
  Avatar,
  Fade,
} from "@mui/material";
import LockIcon from "@mui/icons-material/Lock";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

const OtpVerificationStep = ({
  phone,
  dialCode,
  otp,
  error,
  isLoading,
  otpInputRef,
  handleOtpChange,
  handleOtpBoxClick,
  handleVerifyOtp,
  setStep,
  theme,
  formatPhoneNumber,
}) => {
  return (
    <Fade in={true} timeout={500}>
      <Box>
        {/* Lock Icon and Header */}
        <Box sx={{ textAlign: "center", mb: 4 }}>
          <Avatar
            sx={{
              width: 80,
              height: 80,
              bgcolor: `${theme.palette.primary.main}15`,
              color: theme.palette.primary.main,
              margin: "0 auto 16px",
              p: 2,
            }}
          >
            <LockIcon sx={{ fontSize: 40 }} />
          </Avatar>

          <Typography
            variant="h5"
            sx={{ fontWeight: 700, mb: 1.5, color: "text.black" }}
          >
            Enter Verification Code
          </Typography>

          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ maxWidth: 400, mx: "auto" }}
          >
            We've sent a 6-digit code to{" "}
            <strong>
              {dialCode} {formatPhoneNumber(phone)}
            </strong>
          </Typography>
        </Box>

        {/* OTP Input Form */}
        <form
          onSubmit={(e) => {
            e.preventDefault();
            // Only submit if we have all 6 digits and not already loading
            if (otp.length === 6 && !isLoading) {
              // Force clear any errors if needed
              handleVerifyOtp();
            }
          }}
        >
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 3,
              border: "1px solid",
              borderColor: "divider",
              "&:hover": {
                borderColor: "text.black",
              },
              bgcolor: "background.paper",
              mb: 3,
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                mb: 2,
                color: "text.black",
                textAlign: "center",
              }}
            >
              Enter the 6-digit code
            </Typography>

            <Box sx={{ mb: 2 }}>
              {/* Individual OTP input boxes */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  gap: { xs: 1, sm: 2 },
                  mb: 2,
                }}
              >
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <Box
                    key={index}
                    sx={{
                      width: { xs: 40, sm: 50 },
                      height: { xs: 50, sm: 60 },
                      border: "2px solid",
                      borderColor: otp[index]
                        ? theme.palette.primary.main
                        : "rgba(0, 0, 0, 0.1)",
                      borderRadius: 2,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      fontSize: "1.5rem",
                      fontWeight: 600,
                      color: "text.black",
                      transition: "all 0.2s ease",
                      boxShadow: otp[index]
                        ? `0 0 0 2px ${theme.palette.primary.main}20`
                        : "none",
                      position: "relative",
                      cursor: "pointer",
                      "&:hover": {
                        borderColor: "text.black",
                        boxShadow: `0 0 0 2px rgba(0, 0, 0, 0.1)`,
                      },
                      // Blinking cursor for active input
                      "&::after":
                        index === otp.length
                          ? {
                              content: '""',
                              position: "absolute",
                              right: "50%",
                              height: "60%",
                              width: "2px",
                              backgroundColor: theme.palette.primary.main,
                              animation: "blink 1s infinite",
                              "@keyframes blink": {
                                "0%, 100%": { opacity: 1 },
                                "50%": { opacity: 0 },
                              },
                            }
                          : {},
                    }}
                    onClick={() => handleOtpBoxClick(index)}
                  >
                    {otp[index] || ""}
                  </Box>
                ))}
              </Box>

              {/* Hidden input field for actual input */}
              <Box sx={{ position: "absolute", left: "-9999px" }}>
                <input
                  ref={otpInputRef}
                  type="text"
                  value={otp}
                  onChange={(e) => {
                    handleOtpChange(e);
                  }}
                  onKeyDown={(e) => {
                    // Handle Enter key to submit form when 6 digits are entered
                    if (e.key === "Enter" && otp.length === 6) {
                      e.preventDefault();
                      handleVerifyOtp();
                      return;
                    }

                    // Handle backspace to clear last digit
                    if (e.key === "Backspace" && otp.length > 0) {
                      // Don't prevent default - let the input handle backspace normally
                      // This allows the backspace key to work as expected
                    }

                    // Handle paste event
                    if (e.key === "v" && (e.ctrlKey || e.metaKey)) {
                      navigator.clipboard
                        .readText()
                        .then((text) => {
                          const pastedText = text.trim();
                          if (/^\d{1,6}$/.test(pastedText)) {
                            // Set the OTP value directly with the pasted data (limited to 6 digits)
                            const validOtp = pastedText.substring(0, 6);
                            // Use the parent's handleOtpChange by simulating an input event
                            handleOtpChange({ target: { value: validOtp } });
                          }
                        })
                        .catch((err) =>
                          console.error("Failed to read clipboard", err)
                        );
                    }
                  }}
                  onPaste={(e) => {
                    e.preventDefault(); // Prevent default to handle it manually
                    const pastedData = e.clipboardData
                      .getData("text/plain")
                      .trim();
                    if (/^\d{1,6}$/.test(pastedData)) {
                      // Set the OTP value directly with the pasted data (limited to 6 digits)
                      const validOtp = pastedData.substring(0, 6);
                      // Use the parent's handleOtpChange by simulating an input event
                      handleOtpChange({ target: { value: validOtp } });
                    }
                  }}
                  maxLength={6}
                  inputMode="numeric"
                  pattern="[0-9]*"
                  autoComplete="one-time-code"
                  autoFocus
                />
              </Box>
            </Box>
          </Paper>

          {/* Verify Button */}
          <Button
            type="submit"
            variant="contained"
            fullWidth
            onClick={() => {
              // Force clear any errors before verification
              if (error) {
                console.log("Error present when button clicked:", error);
                // This will be handled in the parent component
              }
              handleVerifyOtp();
            }}
            disabled={isLoading || otp.length !== 6}
            sx={{
              py: 1.5,
              borderRadius: 2,
              fontSize: "1rem",
              fontWeight: 600,
              textTransform: "none",
              boxShadow: "none",
              "&:hover": {
                boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
              },
              mb: 2,
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              "Verify Code"
            )}
          </Button>
        </form>

        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Button
            variant="text"
            color="primary"
            onClick={() => setStep(1)}
            disabled={isLoading}
            startIcon={<ArrowBackIcon />}
            sx={{
              textTransform: "none",
              fontWeight: 500,
              color: "text.secondary",
            }}
          >
            Change Number
          </Button>
        </Box>
      </Box>
    </Fade>
  );
};

export default OtpVerificationStep;
