"use client";

import { Box } from "@mui/material";
import Navbar from "../navbar";
import Footer from "../footer";
import { useEffect, useState } from "react";
import Chatbot from "@/app/commoncomponents/chatbot";
import WhatsappFAB from "@/app/commoncomponents/WhatsappFAB";
import FloatingMenuButton from "./FloatingSelftestButton";

const Structure = ({ children }) => {
  const noNavbarPaths = ["landing-page", "chat"];
  const [showNavbar, setShowNavbar] = useState(true);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const isShow = !noNavbarPaths.includes(window.location.pathname.split("/")[1]);
    setShowNavbar(isShow);
    setIsChecking(false);
  }, []);

  if (isChecking) return <></>
  return (
    <>
      {showNavbar && <Navbar />}
      <Box sx={{ minHeight: "calc(100vh - 150px)" }}>{children}</Box>
      {showNavbar && <Footer />}
      <WhatsappFAB/>
      <Chatbot/>
      <FloatingMenuButton />
    </>
  );
};

export default Structure;
