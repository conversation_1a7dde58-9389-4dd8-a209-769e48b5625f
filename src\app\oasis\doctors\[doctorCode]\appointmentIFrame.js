"use client";

import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import Slide from "@mui/material/Slide";
import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import { Box, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function AppointmentIFrame({ redirectionUrl }) {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <React.Fragment>
      <Button
        // color="primary"
        id="appointmentIFrameBtn"
        onClick={handleClickOpen}
        sx={{
          background: "#fff",
          boxShadow: "none",
          textTransform: "none",
          color: "primary.main",
          fontWeight: 600,
          "&:hover": {
            background: "#fff",
            color: "primary.main",
          },
          width: { xs: "100%", sm: "auto" },
        }}
      >
        <Box
          sx={{
            bgcolor: "primary.main",
            mask: "url(/appointment.svg) no-repeat center / contain",
            height: "32px",
            width: "32px",
            marginRight: "2px",
          }}
        ></Box>{" "}
        Book an appointment
      </Button>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        // keepMounted
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        fullScreen
      >
        <Box
          sx={{
            display: "flex",
            jusitfyContent: "center !important",
            padding: "4px 24px",
          }}
        >
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleClose}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <iframe
          id="inlineFrameExample"
          title="Inline Frame Example"
          style={{ height: "100%", width: "100%" }}
          src={redirectionUrl}
        ></iframe>
      </Dialog>
    </React.Fragment>
  );
}
