"use client";

import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_BLOGS,
} from "@/constants";
import { useContext, useEffect, useState } from "react";
import axios from "axios";
import { Skeleton } from "@mui/material";
import SectionLayout from "@/app/labssinglehospital1/components/SectionLayout";
import BlogCard from "@/app/aspire/components/blogCard";
import { AppContext } from "@/app/AppContextLayout";

export default function Blogs() {
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const [blogs, setBlogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  const getBlogs = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?list=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        setBlogs(result);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getBlogs();
  }, [enterpriseCode]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayout>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: { xs: "32px", md: "64px" },
          }}
        >
          <Typography variant="h4">Blogs</Typography>
          <Box
            sx={
              {
                // display: "flex",
                // flexDirection: { xs: "column-reverse", md: "row" },
                // gap: "48px",
              }
            }
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: "48px",
                flex: 1,
              }}
            >
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: {
                    xs: "1fr",
                    sm: "1fr 1fr",
                    md: "repeat(3, 1fr)",
                    lg: "repeat(4, 1fr)",
                  },
                  gap: "32px",
                }}
              >
                {isLoading
                  ? [1, 2, 3, 4, 5, 6].map((_, index) => {
                      return (
                        <Skeleton
                          key={index}
                          variant="rounded"
                          animation="wave"
                          height={200}
                        />
                      );
                    })
                  : blogs.map((blog, index) => {
                      const { code = null } = blog || {};
                      return <BlogCard id={`blog${index}`} key={code} blog={blog} />;
                    })}
              </Box>

            </Box>

          </Box>
        </Box>
      </SectionLayout>
    </Box>
  );
}
