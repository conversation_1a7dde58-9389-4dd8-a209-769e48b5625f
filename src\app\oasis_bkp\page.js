import HomepageBanner from "./components/homepageBanner";
// import WidgetsSection from "./components/widgetsSection";
// import PhotosSection from "./components/photosSection";
// import VideosSection from "./components/videosSection";
// import ReviewsSection from "./components/reviewsSection";
import dynamic from 'next/dynamic';
const BlogsSection = dynamic(() => import('./components/blogsSection'));
const FaqsSection = dynamic(() => import('./components/faqsSection'));
const ReviewsSection = dynamic(() => import('../commoncomponents/reviewsSection'));
const Chatbot = dynamic(() => import('../commoncomponents/chatbot'));
const VideosSection = dynamic(() => import('./components/videosSection'));
const PhotosSection = dynamic(() => import('./components/photosSection'));
const WidgetsSection = dynamic(() => import('./components/widgetsSection'));
// import BlogsSection from "./components/blogsSection";
// import FaqsSection from "./components/faqsSection";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  APOLLO_BELIAGHATA,
} from "@/constants";
import WebsiteStructureDataScript from "@/app/commoncomponents/websiteStructureData";
import {isEmptyObject} from "@/app/utils/isEmptyObject";
import {getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
// import Chatbot from "@/app/commoncomponents/chatbot";
// import ReviewsSection from "../commoncomponents/reviewsSection";

const getWebsiteData = async () => {
  const domainName = getWebsiteHost()
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
        cache: "no-store"
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function Home() {
    const websiteData = await getWebsiteData();
    const {
        banners = [],
        multiMedia = [],
        testimonials = [],
        blogs = [],
        faqs = [],
    } = websiteData || {};
    const {enterprise_code: enterpriseCode = null} = websiteData || {};

  return (
    <main>
        {!isEmptyObject(websiteData) && <WebsiteStructureDataScript websiteData={websiteData}/>}
      <HomepageBanner banners={banners} />
      <WidgetsSection />
      <PhotosSection multiMedia={multiMedia} />
      <VideosSection multiMedia={multiMedia} />
        <SectionLayout>
            <ReviewsSection enterpriseCode={enterpriseCode} testimonials={testimonials} showDefaultReviews={true} />
        </SectionLayout>
      <BlogsSection blogs={blogs} />
        {(faqs && faqs.length > 0) && <FaqsSection faqs={faqs} />}
    </main>
  );
}
