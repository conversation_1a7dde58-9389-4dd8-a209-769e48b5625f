"use client";

import { useContext, useState } from "react";
import { AppContext } from "../../AppContextLayout";
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Rating, 
  Select, 
  MenuItem, 
  FormControl, 
  Pagination,
  Stack
} from "@mui/material";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

export default function Reviews() {
  const { websiteData } = useContext(AppContext);
  const { testimonials = [] } = websiteData || {};

  return (
    <Box sx={{backgroundColor:"#f8f8f8"}}>
    <SectionLayoutSingleSpecialitySingleHospital>
    <Box sx={{
      pb: 8, 
    }}>
      <Box>
        {/* Header Section */}
        <Box
          sx={{
            textAlign: "center",
            mb: 6,
          }}
        >
          <Typography
            variant="h5"
            sx={{
              color: "primary.main",
              mb: 1,
            }}
          >
            OUR TESTIMONIALS
          </Typography>
          <Typography
            variant="h3"
            component="h2"
            sx={{
              color: "#1a1a1a",
              mb: 2,
            }}
          >
            Patient Reviews & Testimonials
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "#666",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Comprehensive eye care procedures delivered with expertise and
            compassion.
          </Typography>
        </Box>

        {/* Testimonials Grid */}
        <Grid container spacing={3}>
          {testimonials.map((testimonial, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Box
                sx={{
                  p: 4,
                  // minHeight: {xs:"400px",md:"300px"},
                  display: "flex",
                  flexDirection: "column",
                  alignItems:"center",
                  backgroundColor:"#fff",
                  borderRadius: "16px",
                  border:"0.1px solid rgba(26, 26, 26, 0.12)",
                  boxShadow: "0 4px 20px rgba(0,0,0,0.05)",
                  position: "relative",
                  overflow: "visible",
                }}
              >
                <Rating value={testimonial.rating} readOnly sx={{ mb: 2 }} />
                <FormatQuoteIcon
                  sx={{
                    fontSize: "4rem",
                    color: "#f0f0f0",
                    position: "absolute",
                    top: "2rem",
                    right: "2rem",
                    zIndex: 0,
                    opacity: 0.5,
                  }}
                />
                <FormatQuoteIcon
                  sx={{
                    fontSize: "4rem",
                    color: "#f0f0f0",
                    position: "absolute",
                    top: "2rem",
                    left: "2rem",
                    rotate:"180deg",
                    zIndex: 0,
                    opacity: 0.5,
                  }}
                />
                <Typography
                  sx={{
                    mb: 3,
                    lineHeight: 1.8,
                    color: "#666",
                    position: "relative",
                    zIndex: 1,
                  }}
                >
                  {testimonial.text}
                </Typography>
                <Box sx={{ mt: "auto" }}>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      color: "#333",
                    }}
                  >
                    {testimonial.name}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* Pagination */}
        {/* <Stack 
          direction="row" 
          justifyContent="center" 
          sx={{ mt: 6 }}
        >
          <Pagination 
            count={3} 
            color="primary" 
            shape="rounded"
            size="large"
          />
        </Stack> */}
      </Box>
    </Box>
    </SectionLayoutSingleSpecialitySingleHospital>
    </Box>
  );
}