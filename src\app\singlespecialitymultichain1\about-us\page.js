"use client";

import React, { useContext } from "react";
import parse from 'html-react-parser';
import useStyles from "../styles";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
import {AppContext} from "@/app/AppContextLayout";

const AboutUs = () => {
  const commonClasses = useStyles();
  const { websiteData } = useContext(AppContext);
  const { about_us: aboutUsHTML = '' } = websiteData || {};
  return (
      <SectionLayout>
        <div
          className="ck-content"
          dangerouslySetInnerHTML={{__html: aboutUsHTML}} />
      </SectionLayout>
  );
};

export default AboutUs;
