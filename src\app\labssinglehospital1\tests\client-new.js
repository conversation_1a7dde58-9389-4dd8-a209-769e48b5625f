"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Typography,
  Grid,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Paper,
  Divider,
  Button,
  useMediaQuery,
  useTheme,
  Pagination,
} from "@mui/material";
import TestCard from "../components/TestCard";
import PackageCard from "../components/PackageCard";
import FilterListIcon from "@mui/icons-material/FilterList";
import TestsPageDescription from "../components/TestsPageDescription";

// Item types
const itemTypes = [
  { id: "all", name: "All Types" },
  { id: "test", name: "Tests Only" },
  { id: "package", name: "Packages Only" },
];

export default function TestsPageClient({ data, initialCategory, initialType, initialPage }) {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // State for categories
  const [allCategories, setAllCategories] = useState([]);

  // State for tests and packages
  const [allTests, setAllTests] = useState([]);
  const [allPackages, setAllPackages] = useState([]);

  // State for API metadata
  const [testsMetadata, setTestsMetadata] = useState({
    totalCount: 0,
    currentPage: 1,
    totalPages: 1
  });
  const [packagesMetadata, setPackagesMetadata] = useState({
    totalCount: 0,
    currentPage: 1,
    totalPages: 1
  });

  // State for filters
  const [selectedCategory, setSelectedCategory] = useState(initialCategory || "all");
  const [selectedType, setSelectedType] = useState(initialType || "all");
  const [showFilters, setShowFilters] = useState(!isMobile);
  const [showAllCategories, setShowAllCategories] = useState(false);
  const categoriesPerPage = 10;

  // State for pagination
  const [page, setPage] = useState(initialPage || 1);
  const [itemsPerPage] = useState(12);

  // Process data when it changes
  useEffect(() => {
    if (data) {
      // Process categories
      const processedCategories = processCategories(data.categories);
      setAllCategories([{ id: "all", name: "All Categories" }, ...processedCategories]);

      // Process tests
      const processedTests = processTests(data.tests);
      setAllTests(processedTests);

      // Set tests metadata if available
      if (data.tests) {
        setTestsMetadata({
          totalCount: data.tests.totalCount || 0,
          currentPage: data.tests.currentPage || 1,
          totalPages: data.tests.totalPages || 1
        });
      }

      // Process packages
      const processedPackages = processPackages(data.packages);
      setAllPackages(processedPackages);

      // Set packages metadata if available
      if (data.packages) {
        setPackagesMetadata({
          totalCount: data.packages.totalCount || 0,
          currentPage: data.packages.currentPage || 1,
          totalPages: data.packages.totalPages || 1
        });
      }
    }
  }, [data]);

  // Process categories
  const processCategories = (categoriesData) => {
    if (!categoriesData || !categoriesData.data) return [];

    return categoriesData.data.map(category => {
      return {
        id: category.seoSlug || category.code,
        name: category.name,
        code: category.code
      };
    });
  };

  // Process tests
  const processTests = (testsData) => {
    if (!testsData) {
      return [];
    }

    // Handle different data structures based on whether we're viewing a category or all tests
    let testsArray = [];

    if (testsData.data) {
      // Standard data structure for all tests
      testsArray = testsData.data;
    } else if (testsData.labTests) {
      // Data structure when viewing a specific category
      testsArray = testsData.labTests;
    } else if (testsData.labTest) {
      // Alternative structure for category view
      testsArray = testsData.labTest;
    } else {
      // If we're viewing a category directly, the data might be the tests array itself
      if (Array.isArray(testsData)) {
        testsArray = testsData;
      } else {
        return [];
      }
    }

    // Check if the array is empty
    if (testsArray.length === 0) {
      return [];
    }

    const allTests = [];
    const processedCodes = new Set(); // Track processed test codes to avoid duplicates

    // Try to log the first item's structure
    if (testsArray.length > 0) {
    }

    // Process array of tests
    testsArray.forEach(test => {
      // Skip items that look like categories or invalid tests
      if (test.labTests ||
          test.originalPrice === undefined ||
          test.discountedPrice === undefined ||
          test.type === 'category' ||
          test.isCategory === true ||
          (test.type && test.type !== 'test')) {

        return;
      }

      // Skip items with missing essential data
      if (!test.name || !test.code) {

        return;
      }

      if (!processedCodes.has(test.code)) {
        processedCodes.add(test.code);
        // Add category information if available
        const testItem = {
          ...test,
          id: test.code,
          title: test.name,
          description: test.shortDescription || test.alternativeNames,
          isPackage: false
        };

        // If we're viewing a specific category, add that info to the item
        if (data.categorySlug) {
          testItem.categorySlug = data.categorySlug;
        }

        allTests.push(testItem);
      }
    });

    return allTests;
  };

  // Process packages
  const processPackages = (packagesData) => {
    if (!packagesData) {
      return [];
    }

    // Handle different data structures based on whether we're viewing a category or all packages
    let packagesArray = [];

    if (packagesData.data) {
      // Standard data structure for all packages
      packagesArray = packagesData.data;
    } else if (packagesData.labPackages) {
      // Data structure when viewing a specific category
      packagesArray = packagesData.labPackages;
    } else if (packagesData.labPackage) {
      // Alternative structure for category view
      packagesArray = packagesData.labPackage;
    } else {
      // If we're viewing a category directly, the data might be the packages array itself
      if (Array.isArray(packagesData)) {
        packagesArray = packagesData;
      } else {
        return [];
      }
    }

    // Check if the array is empty
    if (packagesArray.length === 0) {
      return [];
    }

    const allPackages = [];
    const processedCodes = new Set(); // Track processed package codes to avoid duplicates

    // Try to log the first item's structure
    if (packagesArray.length > 0) {
    }

    // Process array of packages
    packagesArray.forEach(pkg => {
      // Skip items that look like categories or invalid packages
      if (pkg.labPackages ||
          pkg.originalPrice === undefined ||
          pkg.discountedPrice === undefined ||
          pkg.type === 'category' ||
          pkg.isCategory === true ||
          (pkg.type && pkg.type !== 'package')) {

        return;
      }

      // Skip items with missing essential data
      if (!pkg.name || !pkg.code) {

        return;
      }

      if (!processedCodes.has(pkg.code)) {
        processedCodes.add(pkg.code);
        // Add category information if available
        const packageItem = {
          ...pkg,
          id: pkg.code,
          title: pkg.name,
          description: pkg.shortDescription || pkg.alternativeNames,
          isPackage: true
        };

        // If we're viewing a specific category, add that info to the item
        if (data.categorySlug) {
          packageItem.categorySlug = data.categorySlug;
        }

        allPackages.push(packageItem);
      }
    });


    return allPackages;
  };

  // Handle category change
  const handleCategoryChange = (categoryId) => {
    setSelectedCategory(categoryId);
    setPage(1); // Reset to first page when changing category

    // Update URL
    if (categoryId === "all") {
      if (selectedType === "all") {
        router.push('/tests');
      } else {
        router.push(`/tests?type=${selectedType}`);
      }
    } else {
      if (selectedType === "all") {
        router.push(`/tests?category=${categoryId}`);
      } else {
        router.push(`/tests?category=${categoryId}&type=${selectedType}`);
      }
    }
  };

  // Handle type change
  const handleTypeChange = (typeId) => {
    setSelectedType(typeId);
    setPage(1); // Reset to first page when changing type

    // Update URL
    if (selectedCategory === "all") {
      if (typeId === "all") {
        router.push('/tests');
      } else {
        router.push(`/tests?type=${typeId}`);
      }
    } else {
      if (typeId === "all") {
        router.push(`/tests?category=${selectedCategory}`);
      } else {
        router.push(`/tests?category=${selectedCategory}&type=${typeId}`);
      }
    }
  };

  // Filter items based on selected category and type

  const allFilteredItems = [...allTests, ...allPackages].filter(item => {
    // Match by category
    const categoryMatch =
      selectedCategory === "all" ||
      (item.categorySlug && item.categorySlug === selectedCategory) ||
      (data.categorySlug && data.categorySlug === selectedCategory);

    // Match by type
    const typeMatch =
      selectedType === "all" ||
      (selectedType === "test" && !item.isPackage) ||
      (selectedType === "package" && item.isPackage);

    return categoryMatch && typeMatch;
  });

  // Calculate pagination
  let totalItems, totalPages, startIndex, endIndex, filteredItems;

  // For "All Categories" view, use server-side pagination
  if (selectedCategory === 'all') {
    // Use the API metadata for pagination
    if (selectedType === 'all') {
      // Both tests and packages
      totalItems = (testsMetadata.totalCount || 0) + (packagesMetadata.totalCount || 0);
      totalPages = Math.max(testsMetadata.totalPages || 1, packagesMetadata.totalPages || 1);
    } else if (selectedType === 'test') {
      // Only tests
      totalItems = testsMetadata.totalCount || 0;
      totalPages = testsMetadata.totalPages || 1;
    } else if (selectedType === 'package') {
      // Only packages
      totalItems = packagesMetadata.totalCount || 0;
      totalPages = packagesMetadata.totalPages || 1;
    }

    // Use the items we got from the API for this page
    filteredItems = allFilteredItems;
    startIndex = ((page - 1) * itemsPerPage) + 1;
    endIndex = Math.min(startIndex + filteredItems.length - 1, totalItems);
  } else {
    // For specific category, use client-side pagination
    totalItems = allFilteredItems.length;
    totalPages = Math.ceil(totalItems / itemsPerPage);
    startIndex = (page - 1) * itemsPerPage;
    endIndex = startIndex + itemsPerPage;
    filteredItems = allFilteredItems.slice(startIndex, endIndex);
    // Adjust startIndex for display (1-based instead of 0-based)
    startIndex += 1;
  }

  // Toggle filters on mobile
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    setSelectedCategory("all");
    setSelectedType("all");
    setPage(1); // Reset to first page when clearing filters
    router.push('/tests');
  };

  // Handle page change
  const handlePageChange = (_, newPage) => {
    setPage(newPage);

    // Update URL with page parameter
    let url = '/tests';
    const params = [];

    if (selectedCategory !== 'all') {
      params.push(`category=${selectedCategory}`);
    }

    if (selectedType !== 'all') {
      params.push(`type=${selectedType}`);
    }

    // Always include page parameter for server-side pagination
    if (newPage > 1) {
      params.push(`page=${newPage}`);
    }

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    // For server-side pagination, we need to reload the page
    // For client-side pagination, we can just update the URL
    router.push(url);
  };

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 2, md: 4 } }}>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: "bold",
              fontSize: { xs: "1.5rem", md: "2rem" },
              color: "text.black",
            }}
          >
            {selectedCategory !== 'all' ?
              (() => {
                // Try to find the category by ID first
                const category = allCategories.find(cat => cat.id === selectedCategory);
                if (category) return `${category.name} Tests & Packages`;

                // If not found, try to extract the code from the slug
                const categoryCode = selectedCategory.split('-').pop();
                const categoryByCode = allCategories.find(cat => cat.code === categoryCode);
                if (categoryByCode) return `${categoryByCode.name} Tests & Packages`;

                // Default fallback
                return 'Category Tests & Packages';
              })() :
              'All Tests & Packages'}
            {(() => {
              // For "All Categories" view, use the API total count
              if (selectedCategory === 'all') {
                let totalCount = 0;

                if (selectedType === 'all') {
                  // Both tests and packages
                  totalCount = (testsMetadata.totalCount || 0) + (packagesMetadata.totalCount || 0);
                } else if (selectedType === 'test') {
                  // Only tests
                  totalCount = testsMetadata.totalCount || 0;
                } else if (selectedType === 'package') {
                  // Only packages
                  totalCount = packagesMetadata.totalCount || 0;
                }

                return ` (${totalCount})`;
              } else {
                // For specific category, use the filtered items count
                return allFilteredItems.length > 0 ? ` (${allFilteredItems.length})` : '';
              }
            })()}
          </Typography>
        </Box>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{ mb: 2, maxWidth: "800px" }}
        >
          Browse our comprehensive range of diagnostic tests and health packages
        </Typography>

        {isMobile && (
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={toggleFilters}
            sx={{ mb: 2 }}
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        )}
      </Box>

      <Grid container spacing={3}>
        {/* Filters Section */}
        {showFilters && (
          <Grid item xs={12} md={3} lg={2.5}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                border: "1px solid #e0e0e0",
                borderRadius: "8px",
                position: { md: "sticky" },
                top: { md: "20px" }
              }}
            >
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, color: "text.black" }}>
                Filters
              </Typography>

              <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1, color: "text.black" }}>
                Type of Tests
              </Typography>
              <FormGroup sx={{ mb: 2 }}>
                {itemTypes.map((type) => (
                  <FormControlLabel
                    key={type.id}
                    control={
                      <Checkbox
                        checked={selectedType === type.id}
                        onChange={() => handleTypeChange(type.id)}
                        size="small"
                        sx={{
                          color: selectedType === type.id ? 'primary.main' : undefined,
                          '&.Mui-checked': {
                            color: 'primary.main',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: selectedType === type.id ? 'bold' : 'regular',
                          color: selectedType === type.id ? 'primary.main' : 'text.black'
                        }}
                      >
                        {type.name}
                      </Typography>
                    }
                  />
                ))}
              </FormGroup>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1, color: "text.black" }}>
                Categories ({allCategories.length})
              </Typography>
              <FormGroup>
                {/* Always show "All Categories" option */}
                {allCategories.length > 0 && (
                  <FormControlLabel
                    key={allCategories[0].id}
                    control={
                      <Checkbox
                        checked={selectedCategory === allCategories[0].id}
                        onChange={() => handleCategoryChange(allCategories[0].id)}
                        size="small"
                        sx={{
                          color: selectedCategory === allCategories[0].id ? 'primary.main' : undefined,
                          '&.Mui-checked': {
                            color: 'primary.main',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: selectedCategory === allCategories[0].id ? 'bold' : 'regular',
                          color: selectedCategory === allCategories[0].id ? 'primary.main' : 'text.black'
                        }}
                      >
                        {allCategories[0].name}
                      </Typography>
                    }
                  />
                )}

                {/* Show limited categories or all based on state */}
                {allCategories.slice(1, showAllCategories ? undefined : categoriesPerPage + 1).map((category) => (
                  <FormControlLabel
                    key={category.id}
                    control={
                      <Checkbox
                        checked={selectedCategory === category.id}
                        onChange={() => handleCategoryChange(category.id)}
                        size="small"
                        sx={{
                          color: selectedCategory === category.id ? 'primary.main' : undefined,
                          '&.Mui-checked': {
                            color: 'primary.main',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: selectedCategory === category.id ? 'bold' : 'regular',
                          color: selectedCategory === category.id ? 'primary.main' : 'text.black'
                        }}
                      >
                        {category.name}
                      </Typography>
                    }
                  />
                ))}

                {/* Show More/Less button if there are more than 10 categories */}
                {allCategories.length > categoriesPerPage + 1 && (
                  <Box sx={{ mt: 1, textAlign: 'center' }}>
                    <Button
                      size="small"
                      onClick={() => setShowAllCategories(!showAllCategories)}
                      sx={{
                        textTransform: 'none',
                        fontSize: '0.8rem',
                        color: 'primary.main',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      {showAllCategories ? 'Show Less' : `Show More (${allCategories.length - (categoriesPerPage + 1)})`}
                    </Button>
                  </Box>
                )}
              </FormGroup>
            </Paper>
          </Grid>
        )}

        {/* Tests Grid */}
        <Grid item xs={12} md={showFilters ? 9 : 12} lg={showFilters ? 9.5 : 12}>
          <Grid container spacing={2}>
            {filteredItems.length > 0 ? (
              <>
                {filteredItems.map((item) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
                    {item.isPackage ? (
                      <PackageCard packageData={item} />
                    ) : (
                      <TestCard test={item} />
                    )}
                  </Grid>
                ))}

                {/* Pagination */}
                {totalItems > itemsPerPage && (
                  <Grid item xs={12}>
                    <Box sx={{
                      mt: 4,
                      mb: 2,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      borderTop: '1px solid #eaeaea',
                      pt: 3
                    }}>
                      <Pagination
                        count={totalPages}
                        page={page}
                        onChange={handlePageChange}
                        size={isMobile ? "medium" : "large"}
                        showFirstButton
                        showLastButton
                        sx={{
                          '& .MuiPaginationItem-root': {
                            color: 'text.black',
                          },
                          '& .MuiPaginationItem-page.Mui-selected': {
                            backgroundColor: theme.palette.primary.main,
                            color: 'white',
                            '&:hover': {
                              backgroundColor: theme.palette.primary.dark,
                            }
                          }
                        }}
                      />
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        Showing {startIndex}-{endIndex} of {totalItems} {selectedType === "test" ? "tests" : selectedType === "package" ? "packages" : "items"}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </>
            ) : (
              <Grid item xs={12}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 4,
                    textAlign: "center",
                    border: "1px solid #e0e0e0",
                    borderRadius: "8px",
                  }}
                >
                  <Typography variant="h6" color="text.secondary">
                    No tests or packages found matching your filters
                  </Typography>
                  <Button
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={clearFilters}
                  >
                    Clear Filters
                  </Button>
                </Paper>
              </Grid>
            )}
          </Grid>
        </Grid>
      </Grid>

      {/* Description Section */}
      <TestsPageDescription
        categories={data.categories}
        selectedCategory={selectedCategory}
      />
    </Container>
  );
}
