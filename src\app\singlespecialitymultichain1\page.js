import React from "react";
import BlogsSection from "./components/Homepage/Blogs";
import FaqSection from "./components/Homepage/Faq";
import Gallery from "./components/Homepage/Gallery";
import ReviewsSection from "./components/Homepage/Reviews";
import Services from "./components/Homepage/Services";
import Procedures from "@/app/singlespecialitymultichain1/components/procedures";
import VideosSection from "@/app/singlespecialitymultichain1/components/videosSection";
import {isEmptyObject} from "@/app/utils/isEmptyObject";
import WebsiteStructureDataScript from "@/app/commoncomponents/websiteStructureData";
import {getHostForServerComponent} from "@/app/utils/serverOnly/serverUtils";
import {fetchWebsiteData} from "@/api/harbor.service";
import Carousal from "./Carousal";

const getWebsiteData = async () => {
  const domainName = getHostForServerComponent()
  try {
    const data = await fetchWebsiteData({domainName: domainName})

    if (data.code === 200){
      return data.result;
    }
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};
const SereneHomepage = async () => {

  const websiteData = await getWebsiteData();
  //const { websiteData = {} } = useContext(AppContext);
  const {
    banners = [],
    emails = [],
    phoneNumbers = [],
    websiteServices = [],
    multiMedia = [],
    doctors = [],
    blogs = [],
    faqs = [],
    testimonials = []
  } = websiteData || {};


  return (
      <>
        {!isEmptyObject(websiteData) && <WebsiteStructureDataScript websiteData={websiteData}/>}
        <Carousal banners={banners}/>
        {websiteServices.length > 0 && <Services services={websiteServices}/>}
        {/*<Specialities />*/}
        {/*{doctors.length === 1 ? <SingleDoctorSection doctors={doctors}/> : <Doctors doctors={doctors}/> }*/}
        <Procedures/>
        {multiMedia.length > 0 && <Gallery gallery={multiMedia}/>}
        <VideosSection multiMedia={multiMedia}/>
        {blogs.length > 0 && <BlogsSection blogs={blogs}/>}
        <ReviewsSection testimonials={testimonials}/>
        {faqs.length > 0 && <FaqSection faqs={faqs}/>}
      </>
  );
};

export default SereneHomepage;
