"use client";

import { Button, Typography, Box, Container } from "@mui/material";
import { styled } from "@mui/material/styles";

// Modern styled button with clean design
const ActionButton = styled(Button)(({ customcolor, customtextcolor }) => ({
  borderRadius: '8px',
  padding: '12px 24px',
  fontWeight: '400',
  fontSize: '0.95rem',
  textTransform: 'none',
  backgroundColor: customcolor || 'primary.main',
  color: customtextcolor || '#fff',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    border: `1px solid ${customcolor || 'primary.main'}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: '0 8px 8px rgba(0, 0, 0, 0.15)',
    // backgroundColor: customcolor || 'primary.main',
    backgroundColor: 'transparent',
    filter: 'brightness(1.1)',
    border: `1px solid ${customcolor || 'primary.main'}`,
  }
}));

const NeedHelp = ({ value = [], title = "", setIsModalOpen = null }) => {
  return (
    <Box
      sx={{
        py: { xs: 4, md: 4 },
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#f8f9fa',
        // borderTop: '1px solid rgba(0,0,0,0.05)',
        borderBottom: '2px solid rgba(0, 0, 0, 0.14)',
      }}
    >
      <Box sx={{
        maxWidth: '1400px',
        mx: 'auto',
        px: { xs: 2, md: 4 },
        position: 'relative',
        zIndex: 1,
      }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
          }}
        >
          {title && (
            <Typography
              variant="h4"
              sx={{
                fontSize: { xs: '1.75rem', md: '2.25rem' },
                fontWeight: '400',
                color: '#002147',
                mb: 5,
                position: 'relative',
                display: 'inline-block',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -10,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 80,
                  height: 3,
                  backgroundColor: 'primary.main',
                  borderRadius: 2,
                }
              }}
            >
              {title}
            </Typography>
          )}

          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              flexWrap: "wrap",
              gap: 3,
              width: '100%',
              maxWidth: '800px',
            }}
          >
            {value.map((item, index) => {
              const { label = "", redirection = {}, theme = {}, position = null } = item || {};
              const { redirectionUrl = "" } = redirection || {};
              const { primaryColor = "", primaryTextColor = "" } = theme || {};

              return (
                <ActionButton
                  id={`ctaWidget${index}Pos${position}`}
                  key={index}
                  customcolor={primaryColor}
                  customtextcolor={primaryTextColor}
                  variant="contained"
                  sx={{
                    flex: { xs: '1 1 100%', sm: '0 1 auto' },
                    minWidth: { xs: '100%', sm: '200px' },
                  }}
                  onClick={() => {
                    if (!redirectionUrl || !setIsModalOpen) return;
                    redirectionUrl.includes("pop1")
                      ? setIsModalOpen(true)
                      : window.open(redirectionUrl || "#", "_blank");
                  }}
                >
                  {label || "Contact Us"}
                </ActionButton>
              );
            })}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default NeedHelp;
