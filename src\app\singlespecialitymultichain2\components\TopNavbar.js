'use client'
import { alpha, Box, Container, Typography, useMediaQuery, useTheme } from "@mui/material";
import Link from "next/link";

import axios from "axios";
import { useContext, useEffect, useState } from "react";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_NAVBAR,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";

const TopNavbar = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const { websiteData } = useContext(AppContext);
  const {
    phoneNumbers = [],
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};
  const primaryPhone = phoneNumbers?.[0].phone || ""
  // console.log(primaryPhone) 
  // console.log(phoneNumbers)
  const [navbarItemsList, setNavbarItemsList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const getNavbarItems = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_NAVBAR}${enterpriseCode}/`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      // console.log(data)
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        setNavbarItemsList(result);
      }
    } catch (error) {
      console.error("Error fetching navbar items:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getNavbarItems();
  }, [enterpriseCode]);
  if (isMobile) return null;

  return (
    <Box
      sx={{
        backgroundColor: alpha(theme.palette.primary.main, 0.3),
        py: 0.8,
        borderBottom: "1px solid #e0e0e0",
        display: "flex",
        alignItems: "center",
        justifyContent: "end",
      }}
    >
      <Container maxWidth="xl" sx={{ display: "flex", justifyContent: "end", alignItems: "center" }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 4 }}>
          {!isMobile && (
            <>
              {
              isLoading
                ? Array.from({ length: 1 }).map((_, index) => (
                    <Typography key={index} variant="body2" sx={{ color: "#ccc" }}>Loading...</Typography>
                  ))
                : 
                (navbarItemsList[0]?.sections || []).map((item, index) => {
                    const { displayName = "", redirection = {} } = item || {};
                    const { redirectionUrl = "" } = redirection || {};
                    return (
                      <Link key={index} href={redirectionUrl} style={{ textDecoration: "none" }}>
                        <Typography
                          variant="body2"
                          component="span"
                          sx={{ color: "#222", "&:hover": { color: "primary.main" } }}
                        >
                          {displayName}
                        </Typography>
                      </Link>
                    );
                  })}
            </>
          )}


          {/* <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
            <PhoneIcon sx={{ fontSize: 16, color: "#222", "&:hover": { color: "primary.main" } }} />
            <Typography
              variant="body2"
              component="a"
              href={`tel:${primaryPhone}`}
              sx={{ color: "#222", fontWeight: 500, textDecoration: "none", "&:hover": { color: "primary.main" } }}
            >
              {primaryPhone}
            </Typography>
          </Box> */}
        </Box>
      </Container>
    </Box>
  );
};

export default TopNavbar;
