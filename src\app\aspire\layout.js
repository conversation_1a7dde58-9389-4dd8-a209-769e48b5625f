import { AppRouter<PERSON>acheProvider } from "@mui/material-nextjs/v14-appRouter";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import {AR_One_Sans, <PERSON>ra_Sans, Inter} from "next/font/google";
import "../globals.css";
import "../fonts.css";
import 'ckeditor5/ckeditor5.css';

import Navbar from "../aspire/navbar";
import Footer from "../aspire/footer";
import Box from "@mui/material/Box";
import { headers } from "next/headers";
import CustomThemeProvider from "@/CustomThemeProvider";
import AppContextLayout from "../AppContextLayout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PLUGINS,
} from "@/constants";
import { getThumborUrl } from "../utils/getThumborUrl";
import localFont from "next/font/local";
import Head from "next/head";
import { GoogleTagManager, GoogleAnalytics } from "@next/third-parties/google";
import Structure from "../aspire/components/structure";
import Script from "next/script";
import {getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";

// const ar_one_sans = AR_One_Sans({ subsets: ["latin"], display: "swap" });
import { Roboto } from 'next/font/google';
import CustomThemeProviderAspire from "@/app/aspire/CustomThemeProviderAspire";

// const roboto = Roboto({
//   weight: ['300', '400', '500', '700'],
//   subsets: ['latin'],
//   display: 'swap',
//   variable: '--font-roboto',
// });

const inter = Inter({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});
export const generateMetadata = async () => {
  // const domainName = headers().get("host");
  const domainName = getWebsiteHost()
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
      cache: "no-store"
    });
    const jsonRes = await res.json();
    const { seoData = [] } = jsonRes?.result || {};
    const { title = "", meta_description: metaDescription = "" } =
      seoData[0] || {};
    return {
      title: title || "My Hospital",
      description:
        metaDescription ||
        "A trusted platform for personalized patient care, offering tailored solutions and comprehensive services, prioritising comfort and satisfaction.",
    };
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

const getWebsiteData = async () => {
  const domainName = getWebsiteHost()
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
      cache: "no-store"
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

const getPlugins = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PLUGINS}?details=true`;
  try {
    const res = await fetch(url);
    const jsonRes = await res.json();
    return jsonRes?.result;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

// const myFont = localFont({
//   src: fontCdn,
//   display: 'swap',
// })

export default async function RootLayout({ children }) {
  const websiteData = await getWebsiteData();
  const {
    template = {},
    favicon_url: faviconUrl = "",
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};
  const {
    primary_font_family_cdn: fontFamilyCdn = "",
    primary_font_family: fontFamily = "",
  } = template || {};
  const configData = await getPlugins(enterpriseCode);
  const { ga = [], gtm = [] } = configData || {};
  const { configValue: gaConfigValue = null } = ga[0] || {};
  const { configValue: gtmConfigValue = null } = gtm[0] || {};

  return (
    <html lang="en">
      <link rel="icon" href={getThumborUrl(faviconUrl, 32, 32)} sizes="any" />
      <body style={{ height: "100%" }} className={inter.variable}>
        <AppRouterCacheProvider options={{ key: "css" }}>
          <AppContextLayout websiteData={websiteData}>
            <CustomThemeProviderAspire template={template} fontFamily={fontFamily}>
              <Structure children={children} />
            </CustomThemeProviderAspire>
          </AppContextLayout>
        </AppRouterCacheProvider>
        {gaConfigValue && <GoogleAnalytics gaId={gaConfigValue} />}
        {gtmConfigValue && <GoogleTagManager gtmId={gtmConfigValue} />}
      </body>
    </html>
  );
}
