"use client";

import { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  Tabs,
  Tab,
  <PERSON>po<PERSON>,
  Button,
  Container,
  CircularProgress,
  useTheme,
} from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import { useRouter } from "next/navigation";

// Fetch Speciality Details API
const getSpecialityDetails = async ({ enterpriseCode, specialityCode }) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?code=${specialityCode}`;
  try {
    const response = await axios.get(url);
    const { status, data } = response || {};
    if (status >= 200 && status < 300) {
      const { specialities = [] } = data?.result || {};
      return specialities[0] || null;
    }
  } catch (error) {
    console.error("Error fetching speciality details:", error);
    return null;
  }
};

export default function Specialities({ specialities, enterpriseCode, isChainSpeciality }) {
  const [activeSpeciality, setActiveSpeciality] = useState(null);
  const [specialityDetails, setSpecialityDetails] = useState(null);
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const swiperRef = useRef(null);
  useEffect(() => {
    if (specialities && specialities.length > 0) {
      const firstSpeciality = specialities[0];
      setActiveSpeciality(firstSpeciality);
      fetchSpecialityDetails(firstSpeciality.seoSlug);
    }
  }, [specialities]);

  const fetchSpecialityDetails = async (seoSlug) => {
    setLoading(true);
    const details = await getSpecialityDetails({
      enterpriseCode,
      specialityCode: seoSlug,
    });
    setSpecialityDetails(details);
    setLoading(false);
  };

  const handleTabChange = (event, newValue) => {
    const selectedSpeciality = specialities[newValue];
    setActiveSpeciality(selectedSpeciality);
    fetchSpecialityDetails(selectedSpeciality.seoSlug);
  };

  // Manually trigger autoplay after Swiper is initialized
  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.autoplay.start(); // Manually start autoplay
    }
  }, [specialityDetails]);

  return (
    <Box
      sx={{
        background: `linear-gradient(to bottom, ${theme.palette.primary.main}05, ${theme.palette.primary.main}10)`,
        py: 4,
        overflow: "hidden",
        position: "relative",
      }}
    >
      <Container maxWidth="lg">
        <Typography
          align="center"
          variant="h4"
          sx={{
            fontWeight: 400,
            fontSize: {
              xs: "24px",
              md: "36px",
            },
            color: "text.black",
          }}
          mb={2}
        >
          Our Specialities
        </Typography>

        <Box sx={{ mb: 3, display: "flex", justifyContent: "center" }}>
          <Tabs
            value={specialities.indexOf(activeSpeciality)}
            onChange={handleTabChange}
            variant="scrollable"
            sx={{ mb: 1 }}
          >
            {specialities.map((speciality, index) => (
              <Tab
                key={index}
                label={speciality.displayName}
                sx={{
                  color:
                    activeSpeciality === speciality
                      ? theme.palette.primary.main
                      : "black",
                  "&.Mui-selected": {
                    color: "text.black",
                  },
                }}
              />
            ))}
          </Tabs>
        </Box>

        {loading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100px",
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          activeSpeciality && (
            <Box sx={{ textAlign: "center", mb: 4, mx: "auto", width: "80%" }}>
              <Typography variant="body1" sx={{ mt: 1, color: "text.black" }}>
                {activeSpeciality.shortDescription}
              </Typography>
            </Box>
          )
        )}

        <Box sx={{ position: "relative" }}>
          <Swiper
            ref={swiperRef}
            style={{ padding: "10px 5px", paddingBottom: "50px" }}
            modules={[Pagination, Autoplay]}
            spaceBetween={18}
            slidesPerView={1}
            pagination={{ dynamicBullets: true, clickable: true }}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            loop={true}
            initialSlide={0}
            breakpoints={{
              640: { slidesPerView: 2 },
              1024: { slidesPerView: 3 },
            }}
          >
            {specialityDetails?.procedures?.length > 0 ? (
              specialityDetails.procedures.map((procedure, index) => (
                <SwiperSlide key={index}>
                  <Box
                    sx={{
                      position: "relative",
                      borderRadius: "10px",
                      overflow: "hidden",
                      cursor: "pointer",
                      boxShadow: 2,
                      bgcolor: "white",
                      textAlign: "center",
                    }}
                    // onClick={() => {
                    //   router.push(
                    //     `/specialities/${activeSpeciality?.centers[0]?.domain_slug}/${activeSpeciality.seoSlug}/procedures/${procedure.seoSlug}`
                    //   );
                    // }}
                    onClick={() => {
                      if (isChainSpeciality){
                        router.push(
                            `/specialities/${activeSpeciality.seoSlug}/procedures/${procedure.seoSlug}`
                        );
                      }else {
                        router.push(
                            `/specialities/${activeSpeciality?.centers[0]?.domain_slug}/${activeSpeciality.seoSlug}/procedures/${procedure.seoSlug}`
                        );
                      }
                    }}
                  >
                    <Box
                      component="img"
                      src={procedure.bannerUrl || "/default-procedure.jpg"}
                      alt={procedure.displayName}
                      sx={{
                        width: "100%",
                        transition: "transform 0.3s ease-in-out",
                        "&:hover": { transform: "scale(1.02)" },
                        height: 240,
                        objectFit: "cover",
                      }}
                    />
                    <Box sx={{ p: 1 }}>
                      <Typography
                        sx={{ color: "text.black" }}
                        variant="h6"
                        gutterBottom
                        fontSize="18px"
                      >
                        {procedure.displayName || ""}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.black"
                        fontSize="14px"
                        sx={{
                          height: "40px",
                          mb: 2,
                          display: "-webkit-box",
                          WebkitBoxOrient: "vertical",
                          WebkitLineClamp: 2,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                        }}
                      >
                        {procedure.shortDescription ||
                          "No description available"}
                      </Typography>
                      <Button
                        sx={{ color: "text.primary" }}
                        variant="contained"
                        fullWidth
                      >
                        Read More
                      </Button>
                    </Box>
                  </Box>
                </SwiperSlide>
              ))
            ) : (
              <Box sx={{ textAlign: "center", p: 3 }}>
                <Typography variant="body1" color="text.black">
                  No procedures available for this speciality.
                </Typography>
              </Box>
            )}
          </Swiper>
        </Box>
      </Container>
    </Box>
  );
}
