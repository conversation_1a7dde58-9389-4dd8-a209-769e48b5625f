"use client"
import {useRouter} from "next/navigation";
import {useEffect} from "react";

const BlogUrlReplacer = ({ seoSlug, blogCode }) => {
    const router = useRouter();
    useEffect(() => {
        // If doctorCode doesn't match seoSlug, redirect to the correct slug
        if (blogCode !== seoSlug) {
            router.replace(`/blogs/${seoSlug}`);
        }
    }, [blogCode, seoSlug, router]);

    return null
};

export default BlogUrlReplacer;