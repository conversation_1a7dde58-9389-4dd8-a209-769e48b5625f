// import HomepageBanner from "./components/homepageBanner";
// import WidgetsSection from "./components/widgetsSection";
// import PhotosSection from "./components/photosSection";
// import VideosSection from "./components/videosSection";
// import ReviewsSection from "../commoncomponents/reviewsSection";
// import BlogsSection from "./components/blogsSection";
// import FaqsSection from "./components/faqsSection";

import dynamic from 'next/dynamic';
const BlogsSection = dynamic(() => import('./components/blogsSection'));
const FaqsSection = dynamic(() => import('./components/faqsSection'));
const ReviewsSection = dynamic(() => import('../commoncomponents/reviewsSection'));
const VideosSection = dynamic(() => import('./components/videosSection'));
const Chatbot = dynamic(() => import('../commoncomponents/chatbot'));
const PhotosSection = dynamic(() => import('./components/photosSection'));
const WidgetsSection = dynamic(() => import('./components/widgetsSection'));
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_WEBSITE,
    APOLLO_BELIAGHATA,
} from "@/constants";
import {headers} from "next/headers";

import {getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";
import WebsiteStructureDataScript from "@/app/commoncomponents/websiteStructureData";
import {isEmptyObject} from "@/app/utils/isEmptyObject";
import SectionLayoutAspire from "@/app/aspire/styledComponents/SectionLayoutAspire";
import Carousal from "./Carousal";
// import Chatbot from "@/app/commoncomponents/chatbot";

const getWebsiteData = async () => {
    const domainName = getWebsiteHost()
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
    try {
        const res = await fetch(url, {
            cache: "no-store"
        });
        const jsonRes = await res.json();
        return jsonRes.result || {};
    } catch (error) {
        console.log("getWebsiteData", error);
    }
};

export default async function Home() {
    const websiteData = await getWebsiteData();
    const {
        banners = [],
        multiMedia = [],
        testimonials = [],
        blogs = [],
        faqs = [],
    } = websiteData || {};
    const {enterprise_code: enterpriseCode = null} = websiteData || {};

    return (
        <main>
            {!isEmptyObject(websiteData) && <WebsiteStructureDataScript websiteData={websiteData}/>}
            {/* <HomepageBanner banners={banners}/> */}
            <Carousal banners={banners}/>
            <WidgetsSection/>
            <PhotosSection multiMedia={multiMedia}/>
            <VideosSection multiMedia={multiMedia}/>
            <SectionLayoutAspire>
                <ReviewsSection enterpriseCode={enterpriseCode} testimonials={testimonials} showDefaultReviews={true} />
            </SectionLayoutAspire>
            <BlogsSection blogs={blogs}/>
            <FaqsSection faqs={faqs}/>
        </main>
    );
}
