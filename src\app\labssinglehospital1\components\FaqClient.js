"use client";
import React from "react";
import { useState } from "react";
import Image from "next/image";
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper,
  Grid,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { ArrowDownward } from "@mui/icons-material";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  border: "none",
  boxShadow: "none",
  "&:before": {
    display: "none",
  },
  "&:not(:last-child)": {
    borderBottom: "1px solid rgba(0, 0, 0, 0.08)",
  },
  backgroundColor: "transparent",
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  padding: theme.spacing(3, 0),
  "& .MuiAccordionSummary-content": {
    margin: theme.spacing(0),
  },
  "& .MuiAccordionSummary-expandIconWrapper": {
    color: theme.palette.text.black,
  },
}));

const StyledAccordionDetails = styled(AccordionDetails)(({ theme }) => ({
  padding: theme.spacing(0, 3, 3, 0),
}));
const ImageWrapper = styled(Box)(({ theme }) => ({
  position: "relative",
  width: "100%",
  height: "100%",
  minHeight: "400px",
  aspectRatio: "auto",
  borderRadius: theme.spacing(2),
  overflow: "hidden",
  boxShadow: "0 10px 40px -10px rgba(0,0,0,0.1)",
}));

const FaqClient = ({ faqs, iconUrl }) => {
  const [expanded, setExpanded] = useState();

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <Grid
      container
      spacing={4}
      alignItems="flex-start"
      sx={{ height: "calc(100% - 150px)" }}
    >
      <Grid
        item
        xs={12}
        md={6}
        sx={{
          height: { md: "100%" },
          display: { xs: "none", md: "block" },
        }}
      >
        <ImageWrapper>
          <Image
            src={iconUrl || "/faq.jpg"}
            alt="FAQ Section Image"
            fill
            style={{ objectFit: "cover" }}
            loading="lazy"
            placeholder="blur"
            blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNlMmUyZTIiLz48L3N2Zz4="
          />
        </ImageWrapper>
      </Grid>

      <Grid item xs={12} md={6} sx={{ height: { md: "100%" } }}>
        <Paper
          elevation={0}
          sx={{
            px: 4,
            py: 3,
            backgroundColor: "rgb(255,255,255)",
            borderRadius: 2,
            boxShadow: "0 4px 20px rgba(0,0,0,0.05)",
            height: "80%",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {faqs.map((faq, index) => (
            <StyledAccordion
              key={index}
              expanded={expanded === `panel${index}`}
              onChange={handleChange(`panel${index}`)}
            >
              <StyledAccordionSummary
                expandIcon={<ArrowDownward fontSize="small" />}
                aria-controls={`panel${index}-content`}
                id={`panel${index}-header`}
              >
                <Typography
                  sx={{
                    color:
                      expanded === `panel${index}` ? "text.black" : "text.black",
                  }}
                >
                  {faq.question}
                </Typography>
              </StyledAccordionSummary>
              <StyledAccordionDetails>
                <Typography
                  sx={{
                    color: "text.black",
                    lineHeight: 1.3,
                  }}
                >
                  {faq.answer}
                </Typography>
              </StyledAccordionDetails>
            </StyledAccordion>
          ))}
        </Paper>
      </Grid>
    </Grid>
  );
};

export default FaqClient;
