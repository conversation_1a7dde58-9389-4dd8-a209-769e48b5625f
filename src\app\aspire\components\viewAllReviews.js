"use client";

import { useRouter } from "next/navigation";
import OutlinedButton from "../styledComponents/OutlinedButton";

const ViewAllReviews = () => {
  const router = useRouter();

  const handleViewAllReview = () => {
    router.push("/reviews");
  };
  
  return (
    <OutlinedButton
      id="viewAllReviews"
      style={{ width: "fit-content" }}
      onClick={handleViewAllReview}
    >
      View all
    </OutlinedButton>
  );
};

export default ViewAllReviews;
