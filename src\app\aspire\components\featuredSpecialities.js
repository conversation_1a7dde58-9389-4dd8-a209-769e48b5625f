"use client";

import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { getThumborUrl } from "../../utils/getThumborUrl";
import { AppContext } from "../../AppContextLayout";
import { useRouter } from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import Skeleton from "@mui/material/Skeleton";

const FeaturedSpecialities = ({enterpriseCode, locationCode}) => {
  const router = useRouter();
  const [specialities, setSpecialities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { websiteData = {}, setViewSnackbarMain } = useContext(AppContext);
  //const { enterprise_code: enterpriseCode = null } = websiteData || {};

  const getFeaturedSpecialities = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?isFeatured=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { specialities = [] } = result || {};
        setSpecialities(specialities);
      }
    } catch (error) {
      //   setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getFeaturedSpecialities();
  }, [enterpriseCode]);

  return !isLoading ? (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: {
          xs: "1fr 1fr",
          md: "repeat(3, 1fr)",
          lg: "repeat(4, 1fr)",
          xl: "repeat(5, 1fr)",
        },
        gap: "24px",
      }}
    >
      {specialities.map((speciality) => {
        const {
          code = null,
          displayName = "",
          iconUrl = "",
          seoSlug = "",
        } = speciality || {};
        return (
          <Box
            id={`homepageBannerSpeciality-${seoSlug}`}
            key={code}
            sx={{
              boxShadow: "0 2px 20px rgba(0,0,0,.1)",
              padding: "8px",
              background: "#fff",
              borderRadius: "10px",
              display: "flex",
              flexDirection: "column",
              gap: "8px",
              alignItems: "center",
              cursor: "pointer",
            }}
            onClick={() => router.push(`/specialities/${locationCode}/${seoSlug}`)}
          >
            {iconUrl ? (
              <Image
                alt="featured-speciality"
                src={getThumborUrl(iconUrl, 48, 48)}
                height={48}
                width={48}
              />
            ) : (
              <MedicationIcon
                sx={{ fontSize: "48px", color: "primary.main" }}
              />
            )}
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: "1",
                WebkitBoxOrient: "vertical",
                fontSize: { xs: "14px", sm: "16px" },
              }}
            >
              {displayName || ""}
            </Typography>
          </Box>
        );
      })}
    </Box>
  ) : (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: {
          xs: "1fr 1fr",
          md: "repeat(3, 1fr)",
          lg: "repeat(4, 1fr)",
          xl: "repeat(5, 1fr)",
        },
        gap: "24px",
      }}
    >
      {[1, 2, 3, 4].map((_, index) => {
        return (
          <Skeleton
            key={index}
            variant="rounded"
            height={100}
            sx={{ width: "100%" }}
          />
        );
      })}
    </Box>
  );
};

export default FeaturedSpecialities;
