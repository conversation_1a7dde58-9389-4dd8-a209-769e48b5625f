import { Typography } from "@mui/material";

const SpecialitiesPara = ({ enterpriseSpecialities = []}) => {
    return (
        <Typography variant="subtitle1" sx={{ fontSize: "14px" }}>
            {(enterpriseSpecialities || []).map((speciality, index) => {
                const { displayName = "", code = null } = speciality || {};
                return (
                    <span
                        key={code}
                    >{`${index > 0 ? ` ${displayName || ""}` : (displayName || "")} ${index < enterpriseSpecialities.length - 1 ? "|" : ""
                        }`}</span>
                );
            })}
        </Typography>
    )
}

export default SpecialitiesPara