import { HARBOR_API_DOCFYN_DOMAIN, API_SECTION_API, API_VERSION, API_SECTION_PUBLIC, API_SECTION_WEBSITE } from "@/constants";
import React from "react";
import dynamic from "next/dynamic";
import WebsiteStructureDataScript from "@/app/commoncomponents/websiteStructureData";
import CarousalWrapper from "./components/CarousalWrapper";
import ServicesWrapper from "./components/ServicesWrapper";
import GalleryWrapper from "./components/GalleryWrapper";
import Footer from "./Footer";
import { getWebsiteHost } from "../utils/serverOnly/serverUtils";
const BlogsSection = dynamic(() => import("./components/Homepage/Blogs"));
const FaqSection = dynamic(() => import("./components/Homepage/Faq"));
// const Gallery = dynamic(() => import("./components/Homepage/Gallery"));
const ReviewsSection = dynamic(() => import("./components/Homepage/Reviews"));
const VideosSection = dynamic(() => import("./components/videosSection"));
const Centres = dynamic(() => import("./components/Homepage/Centres"));
const SpecialitiesWrapper = dynamic(() => import("./components/SpecialitiesWrapper"));

export const getWebsiteData = async () => {
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};


const SereneHomepage = async () => {
  const websiteData = await getWebsiteData();

  return (
    <>
      <WebsiteStructureDataScript />
      <CarousalWrapper />
      <ServicesWrapper />
      <SpecialitiesWrapper/>
      {/* <Gallery /> */}
      <GalleryWrapper/>
      <VideosSection/>
      <BlogsSection/>
      <Centres/>
      <ReviewsSection />
      <FaqSection />
      <Footer websiteData={websiteData}/>
    </>
  );
};

export default SereneHomepage;
