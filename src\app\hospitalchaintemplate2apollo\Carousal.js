"use client";

import { Box, useMediaQuery } from "@mui/material";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useRouter } from "next/navigation";
import HighlightsWidgetChainTemp2 from "@/app/hospitalchaintemplate2apollo/components/highlights/highlightsWidgetChainTemp2";

const Carousal = ({ banners = [] }) => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const router = useRouter();
  const desktopBanners = banners.filter(
    (banner) => banner.isMobileCompatible === 0
  );
  const mobileBanners = banners.filter(
    (banner) => banner.isMobileCompatible === 1
  );

  const handleBannerClick = () => {
    router.push("/contact-us");
  };

  // Determine which banners to display
  const bannersToDisplay = isMobile && mobileBanners.length === 0 && desktopBanners.length > 0
    ? desktopBanners
    : isMobile
      ? mobileBanners
      : desktopBanners;

  // Check if we're using desktop banners on mobile as fallback
  const isUsingDesktopOnMobile = isMobile && mobileBanners.length === 0 && desktopBanners.length > 0;

  return (
    <Box key={isMobile ? "mobile" : "desktop"} style={{ position: "relative" }}>
      {/* Show banners based on screen size with fallback logic */}
      {bannersToDisplay.length > 0 ? (
        bannersToDisplay.length === 1 ? (
          <Swiper
            modules={[Autoplay, Pagination, Navigation]}
            spaceBetween={isMobile ? 10 : 18}
            slidesPerView={1}
            loop={true}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            pagination={{ dynamicBullets: true, clickable: true }}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
            className="mySwiper"
          >
            {bannersToDisplay.map((banner, index) => {
              const { image_url: imgUrl = "" } = banner || {};
              return (
                <SwiperSlide key={index}>
                  <Box
                    onClick={handleBannerClick}
                    sx={{
                      marginTop: isMobile ? "10px" : 0,
                      position: "relative",
                      width: "100%",
                      borderRadius: isMobile ? "12px" : 0,
                      overflow: "hidden",
                      px: isMobile ? "10px" : 0,
                      cursor: "pointer",
                    }}
                  >
                    {isMobile && (
                      <Box
                        sx={{
                          width: "100%",
                          height: "100%",
                          borderRadius: "12px",
                          overflow: "hidden",
                        }}
                      >
                        <Image
                          alt={isUsingDesktopOnMobile ? "desktop-banner-on-mobile" : "mobile-slider"}
                          src={getThumborUrl(imgUrl)}
                          layout="responsive"
                          width={isUsingDesktopOnMobile ? 1920 : 780}
                          height={isUsingDesktopOnMobile ? 1080 : 1000}
                          objectFit="cover"
                          priority
                          style={{ borderRadius: "12px" }}
                        />
                      </Box>
                    )}
                    {!isMobile && (
                      <Image
                        alt="desktop-banner"
                        src={getThumborUrl(imgUrl)}
                        layout="responsive"
                        width={1920}
                        height={1080}
                        objectFit="cover"
                        priority
                      />
                    )}
                  </Box>
                </SwiperSlide>
              );
            })}
          </Swiper>
        ) : (
          <Swiper
            modules={[Autoplay, Pagination, Navigation]}
            slidesPerView={1}
            spaceBetween={10}
            loop={true}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            pagination={{ dynamicBullets: true, clickable: true }}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
          >
            {bannersToDisplay.map((banner, index) => {
              const { image_url: imgUrl = "" } = banner || {};
              return (
                <SwiperSlide key={index}>
                  <Box
                    onClick={handleBannerClick}
                    sx={{
                      marginTop: isMobile ? "16px" : 0,
                      position: "relative",
                      width: "100%",
                      borderRadius: isMobile ? "12px" : 0,
                      overflow: "hidden",
                      paddingX: isMobile ? "16px" : 0,
                      cursor: "pointer",
                    }}
                  >
                    {isMobile && (
                      <Box
                        sx={{
                          width: "100%",
                          height: "100%",
                          borderRadius: "12px",
                          overflow: "hidden",
                        }}
                      >
                        <Image
                          alt={isUsingDesktopOnMobile ? "desktop-banner-on-mobile" : "mobile-slider"}
                          src={getThumborUrl(imgUrl)}
                          layout="responsive"
                          width={isUsingDesktopOnMobile ? 1920 : 780}
                          height={isUsingDesktopOnMobile ? 1080 : 1000}
                          objectFit="cover"
                          priority
                          style={{ borderRadius: "12px" }}
                        />
                      </Box>
                    )}
                    {!isMobile && (
                      <Image
                        alt="desktop-slider"
                        src={getThumborUrl(imgUrl)}
                        layout="responsive"
                        width={1920}
                        height={1080}
                        objectFit="cover"
                        priority
                      />
                    )}
                  </Box>
                </SwiperSlide>
              );
            })}
          </Swiper>
        )
      ) : null}
      <HighlightsWidgetChainTemp2/>
    </Box>
  );
};

export default Carousal;
