"use client"
import { <PERSON>, Button, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import useStyles from "../../../styles";
import homepageStyles from "../styles";
import { useRouter } from "next/navigation";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import {NextArrow, PrevArrow} from "@/app/singlespecialitymultichain1/components/arrows";
import ImageCard from "@/app/singlespecialitymultichain1/components/imageCard";

const Gallery = ({ gallery = [] }) => {
  const commonClasses = useStyles();
  const classes = homepageStyles();
  const router = useRouter();
  const [photos, setPhotos] = useState([]);


  const settings = {
        speed: 500,
        lazyLoad: true,
        slidesToShow: 2,
        slidesToScroll: 1,
        dots: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 900,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    dots: true,
                },
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    infinite: true,
                    autoplay: true,
                    autoplaySpeed: 3500,
                    pauseOnHover: true,
                    dots: false,
                },
            },
        ],
    };

  const handleViewGallery = () => {
    router.push("/gallery");
  };

  useEffect(() => {
    if (gallery.length > 0) {
      const filteredPhotos = gallery.filter((item) => Boolean(item.image_url)) || [];
      setPhotos(filteredPhotos);
    }
  }, [gallery])
    if (photos.length === 0) return <></>
  return (
    <Box className={`${commonClasses.layoutPadding} ${classes.gallerySection}`}
         sx={{
             gap: {xs: "32px", md: "48px"}, color: "#333333"
         }}>

      <Typography
        variant="h3"
        style={{ fontSize: "2.5rem" }}
        className={classes.sectionHeading}
        align="center"
      >
        Gallery
      </Typography>
          <Slider {...settings}>
              {photos.map((photo) => {
                  const { image_url: imgUrl = "", code = "" } = photo || {};
                  return <ImageCard code={code} imgUrl={imgUrl} />;
              })}
          </Slider>
      <Box
        style={{
          display: "flex",
          justifyContent: "center",
          position: "relative",
        }}
      >
      </Box>
    </Box>
  );
};

export default Gallery;
