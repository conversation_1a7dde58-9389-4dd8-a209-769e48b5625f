"use client";

import React, { useContext, useState, useEffect } from "react";

import {
  alpha,
  Box,
  Button,
  InputBase,
  styled,
  Typography,
} from "@mui/material";
import RoomIcon from "@mui/icons-material/Room";
import PhoneIcon from "@mui/icons-material/Phone";
import EmailIcon from "@mui/icons-material/Email";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import useStyles from "../styles";
import homepageStyles from "../components/Homepage/styles";
import useContactPageStyles from "./styles";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_SECTION_LEADS,
  API_ENDPOINT_GENERATE, LEAD_SOURCES, PAGE_NAMES
} from "@/constants";
import formatForWhatsApp from "@/app/utils/phoneUtils";
import {AppContext} from "@/app/AppContextLayout";
import Footer, {SOCIAL_LINKS_ICONS} from "@/app/singlespecialitymultichain2/Footer";
import DoctorAppointmentForm2 from "@/app/singlespecialitymultichain2/components/doctorAppointmentForm";

const initialInput = { dialCode: "+91", type: 1 };

const ContactUs = () => {
  const classes = useContactPageStyles();
  const commonClasses = useStyles();
  const homepageClasses = homepageStyles();
  const { websiteData, setViewSnackbarMain, mobileView } =
    useContext(AppContext);
  const [input, setInput] = useState({ ...initialInput });
  const [isLoading, setIsLoading] = useState(false);
  const {
    enterprise_code: enterpriseCode = null,
    addresses = [],
    emails = [],
    socialMediaLinks = [],
    phoneNumbers = [],
  } = websiteData || {};

  const hasWhatsAppNumber = phoneNumbers?.some(number => number.is_whatsapp);
  const whatsappNumber = phoneNumbers?.find(number => number.is_whatsapp);

  const handleInputChange = (event) => {
    setInput((prev) => ({ ...prev, [event.target.name]: event.target.value }));
  };

  const handleSubmit = () => {
    handleLeadGeneration();
  };

  const handleWhatsAppClick = () => {
    if (whatsappNumber) {
      const phone = whatsappNumber.phone; // Get the phone number
      const whatsappUrl = `https://wa.me/${formatForWhatsApp(phone)}`; // WhatsApp URL format
      window.open(whatsappUrl, "_blank"); // Redirect to WhatsApp in a new tab
    }
  };

  const handleLeadGeneration = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}`;
    const reqBody = { ...input, leadSource: 2 };
    try {
      const response = await axios.post(url, reqBody, {
        headers: {
          "Content-Type": "application/json",
          source: mobileView ? "mweb" : "website",
        },
      });
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { message = "" } = result || {};
        setViewSnackbarMain({
          message: message,
          type: "success",
        });
        setInput({ ...initialInput });
      }
    } catch (error) {
      setViewSnackbarMain({
        message: "Something went wrong. Please try again later!",
        type: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
    <div
      className={`${classes.contactUsSection} ${commonClasses.layoutPadding}`}
    >
      <Box
        className={classes.contactUsSectionBox}
        sx={{
          padding: {xs: "1.5rem", md: "2rem"},
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          gap: "32px",
        }}
      >
        <Box className={classes.contactUsSectionInfo} 
        sx={{
          order: { xs: 2, md: 1 } 
        }}>
          <Typography variant="h5" className={classes.contactUsHeading}>
            Contact us
          </Typography>
          <Box className={commonClasses.footerGridItem}>
            {phoneNumbers.length > 0 && (
              <Box className={classes.contactUsInfoBox}>
                <Typography variant="subtitle1" style={{ fontSize: "1.2rem" }}>
                  Phone
                </Typography>
                <Box className={commonClasses.footerGridItemContentBox}>
                  {phoneNumbers.map((number, index) => {
                    const { phone = "" } = number || {};
                    return (
                      <Typography
                        key={index}
                        variant="subtitle1"
                        className={commonClasses.footerGridItemText}
                      >
                        <PhoneIcon
                          className={commonClasses.footerGridItemAddressIcon}
                        />
                        {phone || ""}
                      </Typography>
                    );
                  })}
                </Box>
              </Box>
            )}
            {emails.length > 0 && (
              <Box className={classes.contactUsInfoBox}>
                <Typography variant="subtitle1" style={{ fontSize: "1.2rem" }}>
                  Email
                </Typography>
                <Box className={commonClasses.footerGridItemContentBox}>
                  {emails.map((item, index) => {
                    const { email = "" } = item || {};
                    return (
                      <Typography
                        key={index}
                        variant="subtitle1"
                        className={commonClasses.footerGridItemText}
                      >
                        <EmailIcon
                          className={commonClasses.footerGridItemAddressIcon}
                        />
                        {email || ""}
                      </Typography>
                    );
                  })}
                </Box>
              </Box>
            )}
            {addresses.length > 0 && (
              <Box className={classes.contactUsInfoBox}>
                <Typography variant="subtitle1" style={{ fontSize: "1.2rem" }}>
                  Address
                </Typography>
                <Box className={commonClasses.footerGridItemContentBox}>
                  {addresses.map((address, index) => {
                    const {
                      line1 = "",
                      line2 = "",
                      city = "",
                      country = "",
                    } = address || {};
                    return (
                      <Typography
                        key={index}
                        variant="subtitle1"
                        className={commonClasses.footerGridItemText}
                      >
                        <RoomIcon
                          className={commonClasses.footerGridItemAddressIcon}
                        />{" "}
                        {`${line1 || ""} ${line2 || ""}, ${city || ""}, ${
                          country || ""
                        }`}
                      </Typography>
                    );
                  })}
                </Box>
              </Box>
            )}
            {socialMediaLinks.length > 0 && (
              <Box style={{ display: "flex", gap: "1rem", marginTop: "2rem"}}>
                {socialMediaLinks.map((mediaLink) => {
                  const { link = "", type = null } = mediaLink || {};
                  const Icon = SOCIAL_LINKS_ICONS[type]?.icon;
                  if (!link) return <></>;
                  else if (type === 3)
                    return (
                      <Box
                        onClick={() => window.open(link, "_blank")}
                        style={{ cursor: "pointer" }}
                        className={commonClasses.twitterIcon}
                      ></Box>
                    );
                  return (
                    <Icon
                      onClick={() => window.open(link, "_blank")}
                      className={commonClasses.footerGridItemAddressIcon}
                      style={{ fontSize: "2rem", cursor: "pointer" }}
                    />
                  );
                })}
              </Box>
            )}
          </Box>
          {hasWhatsAppNumber &&
              <Box style={{ marginTop: "1rem", textAlign: "center" }}>
                <Button
                    variant="contained"
                    color="primary"
                    style={{
                      textTransform: "none",
                      minWidth: "10rem",
                      fontSize: "1rem",
                    }}
                    onClick={handleWhatsAppClick}
                >
                  <WhatsAppIcon style={{ marginRight: "8px" }} /> WhatsApp Now
                </Button>
              </Box>}
        </Box>
        <Box sx={{
          order: { xs: 1, md: 2 } 
        }}> 

          <DoctorAppointmentForm2 pageData={{leadSource: LEAD_SOURCES.CONTACT_US_PAGE, pageTitle: PAGE_NAMES.CONTACT_US_PAGE}}/>
        </Box>
      </Box>
    </div>
    <Footer websiteData={websiteData}/>
    </>
  );
};

export default ContactUs;
