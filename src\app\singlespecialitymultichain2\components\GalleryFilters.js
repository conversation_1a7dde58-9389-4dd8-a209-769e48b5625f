import React, { useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import { Box, Chip, Typography, useTheme } from "@mui/material";

const GalleryFilters = ({ tags =[], onTagSelect }) => {
  const [activeTag, setActiveTag] = useState(null);
  const theme = useTheme();

  const handleTagClick = (tag) => {
    setActiveTag(tag);
    onTagSelect(tag);
  };
  const handleClear = () => {
    setActiveTag(null);
    onTagSelect(null);
  };

  return (
    <>
     
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        gap={2}
        width="100%"
      >
         {activeTag && (
        <Box 
          sx={{ 
            mb: 1,
            alignItems: "center",
            display: "flex", 
            justifyContent: "flex-start",
            position: "relative",
          }}
        >
          <Typography 
            onClick={handleClear} 
            sx={{
              ml:1,
              fontSize: "14px",
              fontWeight: 600,
              color: theme.palette.primary.main,
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              transition: "all 0.2s ease",
              "&::before": {
                content: '"×"',
                fontSize: "16px",
                marginRight: "2px",
                fontWeight: 600,
              }
            }}
          >
            Clear
          </Typography>
        </Box>
      )}
        <Box
          display="flex"
          justifyContent="center"
          flexWrap="wrap"
          gap={2}
          sx={{ width: "100%" }}
        >
          <Swiper
            spaceBetween={8}
            slidesPerView="auto"
            style={{
              position: "relative",
              padding: "0 0px",
            }}
          >
            {tags?.map((tag) => (
              <SwiperSlide key={tag} style={{ width: "auto" }}>
                <Chip
                  label={tag}
                  onClick={() => handleTagClick(tag)}
                  color={activeTag === tag ? "primary" : "default"}
                  sx={{
                    fontSize: "14px",
                    // p:"6px 12px",
                    bgcolor: "white",
                    fontWeight:"600",
                    border: `1px solid ${theme.palette.primary.main}`,
                    borderRadius: "20px",
                    "&.MuiChip-colorPrimary": {
                      bgcolor: theme.palette.primary.main,
                      color: "white",
                      "&:hover": { bgcolor: theme.palette.primary.main },
                    },
                  }}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
      </Box>
    </>
  );
};

export default GalleryFilters;