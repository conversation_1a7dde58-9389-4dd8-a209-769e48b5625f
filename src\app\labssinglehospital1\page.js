import React from "react";
import dynamic from "next/dynamic";
import LabWebsiteStructureDataScript from "./components/labWebsiteStructureData";
const CarousalWrapper = dynamic(() => import("./components/CarousalWrapper"), { ssr: false });
const HighlightsWidget = dynamic(() => import("./components/HighlightsWidget"), { ssr: false });
const PopularCategoriesWrapper = dynamic(() => import("./components/PopularCategoriesWrapper"), { ssr: false });
const TopBookedTestsWrapper = dynamic(() => import("./components/TopBookedTestsWrapper"),{ssr: false});
const PopularPackagesWrapper = dynamic(() => import("./components/PopularPackagesWrapper"),{ssr: false});
const BlogsSection = dynamic(() => import("./components/Homepage/Blogs"),{ssr: false});
const FaqSection = dynamic(() => import("./components/Homepage/Faq"),{ssr: false});
const ReviewsSection = dynamic(() => import("./components/Homepage/Reviews"),{ssr: false});


const SereneHomepage = async () => {

  return (
    <>
      <LabWebsiteStructureDataScript />
      {/* Top carousel - showing banners with isCarouselBanner=1 */}
      <CarousalWrapper isCarouselBanner={1} />
      <HighlightsWidget/>
      <PopularCategoriesWrapper/>
      <TopBookedTestsWrapper/>
      <PopularPackagesWrapper/>

      {/* Second carousel after popular packages - showing banners with isCarouselBanner=0 */}
      <CarousalWrapper isCarouselBanner={0} />
      <BlogsSection/>
      <ReviewsSection />
      <FaqSection />
    </>
  );
};

export default SereneHomepage;
