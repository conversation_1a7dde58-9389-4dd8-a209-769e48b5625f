"use client";

import { Button, Box, useTheme, alpha } from "@mui/material";

const CtaStickyWidget = ({ value = [], setIsModalOpen}) => {
    // Ensure we only use a maximum of two buttons
    const buttons = value.slice(0, 2);
    const theme = useTheme();

    return (
        <Box
            sx={{
                position: "fixed",
                bottom: 0,
                width: "100vw",
                zIndex: 1000,
                display: { xs: "block", sm: "none" },
                background: `linear-gradient(to bottom, ${alpha('#ffffff', 0.95)}, #ffffff)`,
                boxShadow: `0px -8px 20px -5px rgba(0, 0, 0, 0.2)`,
                borderTopLeftRadius: "16px",
                borderTopRightRadius: "16px",
            }}
        >
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    padding: "12px 16px",
                    maxWidth: "1200px",
                    margin: "0 auto",
                }}
            >
                {buttons.length === 1 && (
                    <Button
                        id={`ctaWidget0Pos${buttons[0].position}`}
                        key={0}
                        sx={{
                            width: "100%", // Full width if only one button
                            backgroundColor: buttons[0].theme?.primaryColor || theme.palette.primary.main, // Set background color from theme
                            color: buttons[0].theme?.primaryTextColor || "white", // Set text color from theme
                            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
                            padding: "12px",
                            borderRadius: "8px",
                            fontWeight: 500,
                            textTransform: "none",
                            transition: "all 0.3s ease",
                            "&:hover": {
                                backgroundColor: buttons[0].theme?.primaryColor || theme.palette.primary.main,
                                color: buttons[0].theme?.primaryTextColor || "white",
                                transform: "translateY(-2px)",
                                boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.3)}`,
                            },
                        }}
                        onClick={() => {
                            buttons[0].redirection?.redirectionUrl?.includes("pop1") ? setIsModalOpen(true):
                                window.open(buttons[0].redirection?.redirectionUrl || "#", "_blank")}}
                    >
                        {buttons[0]?.label || "Button 1"}
                    </Button>
                )}

                {buttons.length === 2 && (
                    <>
                        <Button
                            id={`ctaWidget0Pos${buttons[0].position}`}
                            key={0}
                            sx={{
                                width: "50%", // Half width for the first button if there are two
                                backgroundColor: buttons[0].theme?.primaryColor || theme.palette.primary.main, // Set background color from theme
                                color: buttons[0].theme?.primaryTextColor || "white", // Set text color from theme
                                boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
                                padding: "12px",
                                borderRadius: "8px",
                                fontWeight: 500,
                                textTransform: "none",
                                transition: "all 0.3s ease",
                                "&:hover": {
                                    backgroundColor: buttons[0].theme?.primaryColor || theme.palette.primary.main,
                                    color: buttons[0].theme?.primaryTextColor || "white",
                                    transform: "translateY(-2px)",
                                    boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.3)}`,
                                },
                            }}
                            onClick={() => {
                                buttons[0].redirection?.redirectionUrl?.includes("pop1") ? setIsModalOpen(true):
                                    window.open(buttons[0].redirection?.redirectionUrl || "#", "_blank")}}
                        >
                            {buttons[0]?.label || "Button 1"}
                        </Button>

                        <Button
                            id={`ctaWidget1Pos${buttons[1].position}`}
                            key={1}
                            sx={{
                                marginLeft: "16px", // Add space between buttons
                                width: "50%", // Half width for the second button
                                backgroundColor: buttons[1].theme?.primaryColor || theme.palette.primary.main, // Set background color from theme
                                color: buttons[1].theme?.primaryTextColor || "white", // Set text color from theme
                                boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
                                padding: "12px",
                                borderRadius: "8px",
                                fontWeight: 500,
                                textTransform: "none",
                                transition: "all 0.3s ease",
                                "&:hover": {
                                    backgroundColor: buttons[1].theme?.primaryColor || theme.palette.primary.main,
                                    color: buttons[1].theme?.primaryTextColor || "white",
                                    transform: "translateY(-2px)",
                                    boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.3)}`,
                                },
                            }}
                            onClick={() => {
                                buttons[1].redirection?.redirectionUrl?.includes("pop1") ? setIsModalOpen(true):
                                window.open(buttons[1].redirection?.redirectionUrl || "#", "_blank")}}
                        >
                            {buttons[1]?.label || "Button 2"}
                        </Button>
                    </>
                )}
            </Box>
        </Box>
    );
};

export default CtaStickyWidget;
