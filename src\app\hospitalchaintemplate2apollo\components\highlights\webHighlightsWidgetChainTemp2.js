"use client";

import {Box, Typography} from "@mui/material";
import Image from "next/image";
import {useRouter} from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import {getThumborUrl} from "@/app/utils/getThumborUrl";

const WebHighlightsWidgetChainTemp2 = ({highlights}) => {
    const router = useRouter();
    const slicedHighlights = highlights.slice(0, 8);
    const onhighlightItemClick = (type, url) => {
        if (type === 0) {
            // Internal redirect
            router.push(url);
        } else if (type === 1) {
            // External redirect, open in a new tab
            window.open(url, "_blank", "noopener,noreferrer");
        }
    };

    return (
        <Box
            sx={{
                display: "flex",
                boxShadow: "0 2px 20px rgb(0 0 0 / 5%)",
                padding: "8px 30px",
                borderRadius: "8px",
                gap: "24px",
                background: "#fff",
                justifyContent: "row",
            }}
        >
            {slicedHighlights.map((highlightItem) => {
                const {
                    code = null,
                    title: displayName = "",
                    imageUrl: iconUrl = "",
                    highlightsRedirection = {},
                } = highlightItem || {};
                const {redirectionUrl: url, redirectionType: type} =
                    highlightsRedirection;
                return (
                    <Box
                        id={`highlightsCard-${code}`}
                        key={code}
                        sx={{
                            background: "#fff",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            cursor: "pointer",
                            paddingRight: "16px",
                            paddingLeft: "16px",
                            maxWidth: "180px",
                        }}
                        onClick={() => onhighlightItemClick(type, url)}
                    >
                        {iconUrl ? (
                            <Image
                                alt="featured-speciality"
                                src={getThumborUrl(iconUrl, 48, 48)}

                                height={48}
                                width={48}
                            />
                        ) : (
                            <MedicationIcon
                                sx={{fontSize: "48px", color: "primary.main"}}
                            />
                        )}
                        <Typography
                            variant="subtitle1"
                            sx={{
                                fontWeight: 500,
                                overflow: "hidden",
                                maxWidth: "150px",
                                textOverflow: "ellipsis",
                                display: "-webkit-box",
                                color: "#333333",
                                WebkitLineClamp: 2, // Limit to two lines
                                WebkitBoxOrient: "vertical",
                                marginTop: "8px",
                                fontSize: "14px",
                                textAlign: "center",
                                whiteSpace: "normal", // Allow normal word wrapping
                            }}
                        >
                            {displayName || ""}
                        </Typography>
                    </Box>
                );
            })}
        </Box>
    );
};

export default WebHighlightsWidgetChainTemp2;
