import {
  Box,
  Container,
  Typography,
} from "@mui/material";
import FaqClient from "../../FaqClient";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import { getHomeComponentsData, getHomeSectionHeadings } from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";

const getFaqHeadings = async () => {
  try {
      const domainName = getWebsiteHost();
      const data = await getHomeSectionHeadings(
          {domainName: domainName},
          HOME_SECTION_HEADING_TYPE.FAQ
      );

      if (data.code === 200) {
        return data?.result || []
      } else
          return []
  } catch (error) {
      console.error("Error fetching faq data:", error);
      return [];
  }
};

const fetchFaqs = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData({ domainName }, HOME_WIDGET_TYPE.FAQ);
    return data?.code === 200 ? data?.result?.faqs : [];
  } catch (error) {
    console.error("Error fetching FAQ data:", error);
    return [];
  }
};
export default async function FAQSection() {
  const faqs = await fetchFaqs();
  const headings = await getFaqHeadings()

  const heading = headings[0]?.heading
  const iconUrl = headings[0]?.iconUrl
  if (!faqs || faqs.length === 0) {
    return null;
  }

  return (
    <Container maxWidth="xl" sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
          position: "relative",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="h2" component="h2" fontWeight="bold" color="text.black" sx={{ fontSize: { xs: '1.5rem', md: '1.8rem' } }}>
            {heading || "Health FAQs"}
          </Typography>
        </Box>
      </Box>
      <FaqClient faqs={faqs} iconUrl={iconUrl}/>
    </Container>
  );
}
