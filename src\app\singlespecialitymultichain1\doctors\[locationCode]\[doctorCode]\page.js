import { Box, Divider, Typography, alpha } from "@mui/material";
import AppointmentScheduler from "./appointmentScheduler";
import axios from "axios";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_ENDPOINT_PROFILE,
    API_SECTION_WEBSITE,
    DAYS_OF_WEEK,
    convertTo12HourFormat, LEAD_SOURCES, doctorData,
} from "@/constants";
import CurrencyRupeeIcon from "@mui/icons-material/CurrencyRupee";
import SchoolIcon from "@mui/icons-material/School";
import DoctorProfilePic from "./doctorProfilePic";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import dynamic from "next/dynamic";
import SectionLayoutAspire from "@/app/aspire/styledComponents/SectionLayoutAspire";

const EducationDetailsPara = dynamic(() => import('./educationDetailsPara'), {
  loading: () => <p>Loading...</p>,
})

const AwardsAndRecognitionSection = dynamic(() => import('./awardsAndRecognitionSection'), {
  loading: () => <p>Loading...</p>,
})

const MembershipSection = dynamic(() => import('./membershipSection'), {
  loading: () => <p>Loading...</p>,
})

const SpecialitiesPara = dynamic(() => import('./specialitiesPara'), {
    loading: () => <p>Loading...</p>,
})

const QuickEnquiry = dynamic(() => import('@/app/commoncomponents/quickEnquiry'), {
    loading: () => <p>Loading...</p>,
})
const DoctorStructureDataScript = dynamic(() => import('../../../components/doctorsStructureData'))

const AppointmentIFrame = dynamic(() => import('./appointmentIFrame'), {
    loading: () => <p>Loading...</p>,
})

const DoctorServices = dynamic(() => import('./doctorServices'), {
    loading: () => <p>Loading...</p>,
})

const OPDTimings = dynamic(() => import('./opdTimings'), {
    loading: () => <p>Loading...</p>,
})

const SliderInfo = dynamic(() => import('./sliderInfo'), {
    loading: () => <p>Loading...</p>,
})

const EducationAndTrainingSection = dynamic(() => import('./educationAndTrainingSection'), {
  loading: () => <p>Loading...</p>,
})


const CertificationsSection = dynamic(() => import('./certificationsSection'), {
  loading: () => <p>Loading...</p>,
})

const ReviewsSection = dynamic(() => import('@/app/commoncomponents/reviewsSection'), {
  loading: () => <p>Loading...</p>,
})

import ExperienceDetailsSection from "./experienceDetails";
import QuickEnquirySingleSplMultiCain from "@/app/singlespecialitymultichain1/components/quickEnquiry";
import DoctorAppointmentForm from "@/app/singlespecialitymultichain1/components/doctorAppointmentForm";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
import { formatDescriptionText } from "@/app/utils/formatDescriptionText";

const getEnterpriseCode = async (locationCode) => {
  if (!locationCode) return;
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true&domainSlug=${locationCode}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    const { result = {} } = jsonRes || {};
    const { enterprise_code: enterpriseCode = null } = result || {};
    return enterpriseCode;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

const getDoctorDetails = async (doctorCode, enterpriseCode) => {
  if (!doctorCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_ENDPOINT_PROFILE}${doctorCode}?enterpriseCode=${enterpriseCode}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { seoSlug } = result || {};
      // if (doctorCode !== seoSlug) {
      //   router.replace(`/doctors/${locationCode}/${seoSlug}`);
      // }
      return result || {};
      // setDoctorDetails(result);
    }
  } catch (error) {
      console.log("Error "+ error)
    //setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
  }
};

const DoctorDetails = async ({ params }) => {
  const { doctorCode = null, locationCode = null } = params || {};
  const enterpriseCode = await getEnterpriseCode(locationCode);
  const doctorDetails = await getDoctorDetails(doctorCode, enterpriseCode);
  const {
    name = "",
    profilePicture = "",
    specialities = [],
    description = "",
    phone = "",
    email = "",
    addtionalDetails = {},
    isNativeAppointmentModuleEnabled = true,
    entityCode = "",
    educationDetails = [],
    experienceDetails = [],
    awardsAndRecognitions = [],
    memberships = [],
    certificationDetails = [],
    doctorMediaLinks = [],
    practiceTimings = [],
    enterpriseSpecialities = [],
    centers = [],
    seoSlug = "",
  } = doctorDetails || {};

  const {
    workExperience = "",
    consultationFee = "",
    recognitionAndAwards = "",
    connect2ClinicRedirectionUrl = "",
    educationalQualifications = "",
  } = addtionalDetails || {};

  return (
    <Box >
      {Object.keys(doctorDetails).length !== 0 && (
        <DoctorStructureDataScript
          doctor={doctorDetails}
          domainSlug={locationCode}
        />
      )}
      <SectionLayout
        sx={{
          bgcolor: "primary.main",
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          gap: { xs: "48px", lg: "64px", xl: "128px" },
          padding: { xs: "32px 16px", md: "32px 32px", lg: "48px 128px" },
          maxHeight: { xs: "100%", md: "350px" },
        }}
      >
        <Box
          sx={{
            display: "grid",
            gap: "48px",
            gridTemplateColumns: "1fr",
          }}
        >
          <Box
            sx={{
              display: "flex",
              gap: "32px",
              color: "#fff",
              flexDirection: { xs: "column", md: "row" },
              alignItems: { xs: "center", md: "start" },
            }}
          >
            <DoctorProfilePic profilePicture={profilePicture} />
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: "8px",
                width: "100%",
              }}
            >
              <Typography variant="h5">{name || ""}</Typography>
              {Boolean(educationalQualifications) && (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    mt: 1,
                  }}
                >
                  <SchoolIcon />
                  <Typography variant="subtitle1" sx={{ fontSize: "14px" }}>
                    {educationalQualifications}
                  </Typography>
                </Box>
              )}

                <SpecialitiesPara
                  enterpriseSpecialities={enterpriseSpecialities}
                />


                <EducationDetailsPara educationDetails={educationDetails} />

              {Boolean(workExperience) && (
                <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  {/* <WorkHistoryOutlinedIcon /> */}
                  <Typography variant="subtitle1" sx={{ fontSize: "14px" }}>
                    Experience : {workExperience} Years
                  </Typography>
                </Box>
              )}
              {Boolean(consultationFee) && (
                <Typography
                  variant="subtitle1"
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  <CurrencyRupeeIcon fontSize="small" /> {consultationFee} fees
                </Typography>
              )}
              {/*{!isEmptyObject(practiceTimings) &&*/}
              {/*  practiceTimings.availability.length > 0 && (*/}
              {/*    <OPDTimings practiceTimings={practiceTimings} />*/}
              {/*  )}*/}
              {/*{!isNativeAppointmentModuleEnabled &&*/}
              {/*  Boolean(connect2ClinicRedirectionUrl) && (*/}
              {/*    <Box sx={{ mt: 2 }}>*/}
              {/*      <AppointmentIFrame*/}
              {/*        redirectionUrl={connect2ClinicRedirectionUrl}*/}
              {/*      />*/}
              {/*    </Box>*/}
              {/*  )}*/}
            </Box>
          </Box>
        </Box>
          <Box
              sx={{
                  background: "#fff", // Optional background for visibility
                  borderRadius: "8px", // Optional border radius
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)", // Optional box shadow
              }}
          >
              <DoctorAppointmentForm pageData={{leadSource: LEAD_SOURCES.DOCTOR_DETAIL_PAGE, productCode: entityCode, pageTitle: name}}/>
          </Box>
      </SectionLayout>
      <SectionLayoutAspire
      >
        <Box
          sx={{
            display: "grid",
            gap: { xs: "48px", lg: "64px", xl: "128px" },
            gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: "64px" }}>
            {Boolean(description) && (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "24px",
                  fontFamily: "inherit",
                  color: "inherit",
                }}
              >
                <Typography variant="h5">
                  <Box
                    sx={{
                      display: "inline-block",
                      "&::after": {
                        content: "''",
                        width: "50%",
                        borderBottom: `3px solid`,
                        borderColor: "primary.main",
                        display: "block",
                        marginBottom: "-1px",
                        left: "50%",
                        right: "50%",
                      },
                    }}
                  >
                    About {name}
                  </Box>
                </Typography>
                <Box>
                  <Typography
                    variant="body1"
                    sx={{
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      display: "-webkit-box",
                      // WebkitLineClamp: "6",
                      WebkitBoxOrient: "vertical",
                    }}
                  >
                    {formatDescriptionText(description) || ""}
                    {/* {description || ""} */}
                  </Typography>
                  {/* <span style={{ cursor: "pointer", color: "#199fd9" }}>
                    Read more
                  </span> */}
                </Box>
              </Box>
            )}

              <EducationAndTrainingSection
                educationDetails={educationDetails}
              />

              
              <ExperienceDetailsSection
                experienceDetails={experienceDetails}
              />

              <DoctorServices enterpriseSpecialities={enterpriseSpecialities} />


              <AwardsAndRecognitionSection
                awardsAndRecognitions={awardsAndRecognitions}
              />


              <MembershipSection memberships={memberships} />


              <CertificationsSection
                certificationDetails={certificationDetails}
              />

          </Box>
          <Box
            sx={{
              position: "sticky",
              top: "200px",
              display: { xs: "none", md: "flex" },
              alignSelf: "start",
              flexDirection: "column",
              gap: "64px",
              marginTop: "200px",
            }}
          >
            {/*<QuickEnquirySingleSplMultiCain leadSource={LEAD_SOURCES.DOCTOR_DETAIL_PAGE} />*/}
            {/*{practiceTimings?.availability?.length > 0 && (*/}
            {/*  <Box*/}
            {/*    sx={{ display: "flex", flexDirection: "column", gap: "24px" }}*/}
            {/*  >*/}
            {/*    <Typography*/}
            {/*      variant="subtitle1"*/}
            {/*      sx={{ fontSize: "1.5rem", mb: 1 }}*/}
            {/*    >*/}
            {/*      <Box*/}
            {/*        sx={{*/}
            {/*          display: "inline-block",*/}
            {/*          "&::after": {*/}
            {/*            content: "''",*/}
            {/*            width: "50%",*/}
            {/*            borderBottom: `3px solid`,*/}
            {/*            borderColor: "primary.main",*/}
            {/*            display: "block",*/}
            {/*            marginBottom: "-1px",*/}
            {/*            left: "50%",*/}
            {/*            right: "50%",*/}
            {/*          },*/}
            {/*        }}*/}
            {/*      >*/}
            {/*        OPD Timings*/}
            {/*      </Box>*/}
            {/*    </Typography>*/}
            {/*    <Box*/}
            {/*      sx={{*/}
            {/*        // display: "flex",*/}
            {/*        // gap: "16px",*/}
            {/*        // flexWrap: "wrap",*/}
            {/*        border: "1px solid #EEEEEE",*/}
            {/*        borderRadius: "8px",*/}
            {/*        boxShadow: "0 1px 6px rgba(0,0,0,.15)",*/}
            {/*      }}*/}
            {/*    >*/}
            {/*      <Box sx={{ padding: "16px 0" }}>*/}
            {/*        <Box sx={{ padding: "0 20px" }}>*/}
            {/*          <Logo />*/}
            {/*        </Box>*/}
            {/*        <Divider sx={{ mt: 1, mb: 1 }} />*/}
            {/*        <Box sx={{ padding: "0 20px" }}>*/}
            {/*          {practiceTimings?.availability?.map((item) => {*/}
            {/*            const {*/}
            {/*              slotStartTime = "",*/}
            {/*              slotEndTime = "",*/}
            {/*              entries = [],*/}
            {/*            } = item || {};*/}
            {/*            const daysArr = entries.map((entry) => {*/}
            {/*              const { day = null } = entry || {};*/}
            {/*              return DAYS_OF_WEEK[day];*/}
            {/*            });*/}
            {/*            const str = daysArr.join(", ");*/}
            {/*            return (*/}
            {/*              <Box*/}
            {/*                sx={*/}
            {/*                  {*/}
            {/*                    // border: "1px solid #EEEEEE",*/}
            {/*                    // borderRadius: "8px",*/}
            {/*                    // padding: "6px 20px",*/}
            {/*                    // width: "fit-content",*/}
            {/*                  }*/}
            {/*                }*/}
            {/*              >*/}
            {/*                <Typography*/}
            {/*                  variant="subtitle1"*/}
            {/*                  sx={{*/}
            {/*                    fontSize: "14px",*/}
            {/*                    fontWeight: 600,*/}
            {/*                    textTransform: "uppercase",*/}
            {/*                    display: "inline-block",*/}
            {/*                  }}*/}
            {/*                >*/}
            {/*                  {`${str}`}*/}
            {/*                  {" : "}*/}
            {/*                  <Typography*/}
            {/*                    variant="subtitle1"*/}
            {/*                    sx={{*/}
            {/*                      fontSize: "13px",*/}
            {/*                      color: "#373A40",*/}
            {/*                      display: "inline-block",*/}
            {/*                    }}*/}
            {/*                  >{`${convertTo12HourFormat(*/}
            {/*                    slotStartTime*/}
            {/*                  )} - ${convertTo12HourFormat(*/}
            {/*                    slotEndTime*/}
            {/*                  )}`}</Typography>*/}
            {/*                </Typography>*/}
            {/*              </Box>*/}
            {/*            );*/}
            {/*          })}*/}
            {/*        </Box>*/}
            {/*      </Box>*/}
            {/*    </Box>*/}
            {/*  </Box>*/}
            {/*)}*/}
          </Box>
        </Box>
        <SliderInfo doctorMediaLinks={doctorMediaLinks} />
          <ReviewsSection
              enterpriseCode={enterpriseCode}
              entityCode={doctorCode}
              padding={{ xs: "32px 16px", md: "32px 32px", lg: "48px 128px" }}
          />
      </SectionLayoutAspire>
    </Box>
  );
};

export default DoctorDetails;
