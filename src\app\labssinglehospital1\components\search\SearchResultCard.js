'use client'
import { <PERSON>, Typo<PERSON>, Button, Avatar, alpha, useTheme, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
// No need for Redux imports as we're using props for cart actions

const SearchResultCard = ({ result, isInCart, onAddToCart, onRemoveFromCart, onNavigate }) => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  return (
    <Box
      onClick={(e) => {
        // Only navigate if not clicking on a button
        if (!e.target.closest('button')) {
          // Close search modal before navigation if onNavigate is provided
          if (onNavigate) {
            onNavigate();
          }
          router.push(`/tests/${result.seoSlug}`);
        }
      }}
      sx={{
        p: { xs: 2, md: 3 },
        cursor: 'pointer',
        mb: 2,
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', md: 'center' },
        gap: { xs: 2, md: 0 },
        transition: 'all 0.2s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }
      }}
    >
      <Box sx={{
        display: 'flex',
        gap: { xs: 2, md: 3 },
        flex: 1,
        width: '100%'
      }}>
        {result.iconUrl ? (
          <Avatar
            src={result.iconUrl}
            alt={result.title}
            sx={{
              width: { xs: 40, md: 80 },
              height: { xs: 40, md: 80 },
              minWidth: { xs: 40, md: 80 },
              borderRadius: '12px',
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: { xs: '20px', md: '24px' }
            }}
          />
        ) : (
          <Box sx={{
            width: { xs: 40, md: 80 },
            height: { xs: 40, md: 80 },
            minWidth: { xs: 40, md: 80 },
            borderRadius: '12px',
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: { xs: '20px', md: '24px' }
          }}>
            🔬
          </Box>
        )}
        <Box sx={{ flex: 1 }}>
          <Typography variant={isMobile ? "subtitle1" : "h6"} sx={{ mb: 0.5, color: 'text.black' }}>
            {result.title}
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 1,
              display: '-webkit-box',
              overflow: 'hidden',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: 2,
            }}
          >
            {result.description}
          </Typography>
          <Box sx={{
            display: 'flex',
            gap: 2,
            alignItems: 'center',
            flexWrap: 'wrap'
          }}>
            {result.testsIncluded && parseInt(result.testsIncluded.split(' ')[0]) >= 1 && (
              <Typography variant="caption" sx={{
                color: theme.palette.text.primary,
                backgroundColor: theme.palette.primary.main,
                px: 1,
                py: 0.5,
                borderRadius: '4px',
                fontSize: { xs: '0.65rem', md: '0.75rem' }
              }}>
                {result.testsIncluded}
              </Typography>
            )}
            <Typography variant="caption" sx={{
              color: 'white',
              backgroundColor: result.isPackage ? '#9c27b0' : '#2196f3',
              px: 1,
              py: 0.5,
              borderRadius: '4px',
              fontSize: { xs: '0.65rem', md: '0.75rem' }
            }}>
              {result.isPackage ? 'Package' : 'Test'}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: { xs: 'flex-start', md: 'flex-end' },
        minWidth: { xs: '100%', md: 150 },
        mt: { xs: 2, md: 0 },
        borderTop: { xs: '1px solid #f0f0f0', md: 'none' },
        pt: { xs: 2, md: 0 }
      }}>
        <Box sx={{
          textAlign: { xs: 'left', md: 'right' },
          mb: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          flexWrap: 'wrap'
        }}>
          {/* If discount percentage is null or 0, show only original price */}
          {(!result.discountPercentage || result.discountPercentage <= 0) ? (
            <Typography variant="h6" component="span" sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
              ₹{result.originalPrice}
            </Typography>
          ) : (
            // Otherwise show discounted price with strikethrough original price
            <>
              <Typography variant="h6" component="span" sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
                ₹{result.discountedPrice}
              </Typography>
              <Typography
                variant="body2"
                component="span"
                sx={{
                  textDecoration: 'line-through',
                  color: 'text.secondary',
                }}
              >
                ₹{result.originalPrice}
              </Typography>
              <Typography
                variant="body2"
                component="span"
                sx={{
                  color: 'success.main',
                }}
              >
                {result.discount}
              </Typography>
            </>
          )}
        </Box>
        {isInCart ? (
          <Button
            variant="outlined"
            color="error"
            onClick={(e) => {
              e.stopPropagation();
              onRemoveFromCart(result);
            }}
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              minWidth: { xs: '100%', md: 100 },
              px: { xs: 2, md: 3 },
              py: { xs: 0.75, md: 1 },
            }}
          >
            <Typography sx={{ fontWeight: 500 }}>
              Remove
            </Typography>
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={(e) => {
              e.stopPropagation();
              onAddToCart(result);
            }}
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              minWidth: { xs: '100%', md: 100 },
              px: { xs: 2, md: 3 },
              py: { xs: 0.75, md: 1 },
            }}
          >
            <Typography sx={{ fontWeight: 500, color: theme.palette.text.primary }}>
              Add
            </Typography>
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default SearchResultCard;
