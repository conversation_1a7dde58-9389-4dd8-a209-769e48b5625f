import { Alert } from "@mui/material";
import Snackbar from "@mui/material/Snackbar";

const MySnackBar = (props) => {
  return (
    <Snackbar
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      open={Boolean(props.message)}
      onClose={() => props.setViewSnackbar({ message: null, type: null })}
      autoHideDuration={props.setDuration ? props.setDuration : 2000}
    >
      <Alert severity={props.variant || "warning"} variant="filled">
        {props.message || ""}
      </Alert>
    </Snackbar>
  );
};

export default MySnackBar;
