import { alpha } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useGalleryStyles = makeStyles((theme) => ({
  galleryBackgroundHeading: {
    background: `${alpha(theme.palette.primary.main, .02)}`,
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    textTransform: "uppercase",
    fontSize: "10rem",
    letterSpacing: "4rem",
    position: "relative",
    zIndex: -1,
    left: "-1rem",
    fontWeight: "800",
    [theme.breakpoints.down('lg')]: {
      fontSize: "10rem",
    },
    [theme.breakpoints.down('md')]: {
      fontSize: "8rem",
      letterSpacing: "3rem",
    },
    [theme.breakpoints.down('sm')]: {
      fontSize: "6rem",
      letterSpacing: "2rem",
    },
    [theme.breakpoints.down('xs')]: {
      fontSize: "4rem",
      letterSpacing: "1rem",
    },
  },
  galleryHeading: {
    fontSize: "3rem",
    position: "absolute",
    top: "50%",
    transform: "translateY(-50%)",
    left: "120px",
    [theme.breakpoints.down("md")]: {
      left: "80px",
    },
    [theme.breakpoints.down("sm")]: {
      left: "40px"
    },
    [theme.breakpoints.down("xs")]: {
      left: '50%',
      transform: "translate(-50%, -50%)"
    },
  },
  galleryGridBox: {
    display: "grid",
    gridTemplateColumns: "repeat(4, 1fr)",
    columnGap: "2rem",
    rowGap: "3rem",
    [theme.breakpoints.down('lg')]: {
      gridTemplateColumns: "repeat(3, 1fr)",
    },
    [theme.breakpoints.down('md')]: {
      gridTemplateColumns: "repeat(2, 1fr)"
    },
    [theme.breakpoints.down('sm')]: {
      gridTemplateColumns: "repeat(1, 1fr)"
    },
  },
  galleryImgBox: {
    height: "400px",
  },
  galleryImg: {
    objectFit: "cover",
    height: "100%",
    width: "100%",
  },
  galleryVideosGridBox: {
    display: "grid",
    gridTemplateColumns: "repeat(3, 1fr)",
    rowGap: "3rem",
    columnGap: "2rem",
    [theme.breakpoints.down('md')]: {
      gridTemplateColumns: "repeat(2, 1fr)"
    },
    [theme.breakpoints.down('sm')]: {
      gridTemplateColumns: "repeat(1, 1fr)"
    },
  },
  galleryLayout: {
    padding: '0px 120px',
    [theme.breakpoints.down("md")]: {
      padding: "0px 80px",
    },
    [theme.breakpoints.down("sm")]: {
      padding: "0px 40px",
    },
    [theme.breakpoints.down("xs")]: {
      padding: "0px 24px",
    },
  }
}));

export default useGalleryStyles;
