import React from 'react'
import <PERSON>ousal from '../Carousal'
import { getWebsiteHost } from '@/app/utils/serverOnly/serverUtils'
import { HOME_WIDGET_TYPE } from '@/constants'
import { getHomeComponentsData } from '@/api/harbor.service'

const getBanners = async () => {
    const domainName = getWebsiteHost()
    try {
        const data = await getHomeComponentsData(
            { domainName: domainName },
            HOME_WIDGET_TYPE.BANNER
          );
          return data?.code === 200 ? data.result.banners || [] : [];
    } catch (error) {
        console.log("error fetching banner", error)
        return [];
    }
}

const CarousalWrapper = async ({ isCarouselBanner = 0 }) => {
    const allBanners = await getBanners();

    // Filter banners based on isCarouselBanner flag
    const banners = allBanners.filter(banner =>
        banner.isCarouselBanner === isCarouselBanner
    );

    if(!banners.length) return null;

    return (
        <Carousal banners={banners} isCarouselBanner={isCarouselBanner}/>
    )
}

export default CarousalWrapper