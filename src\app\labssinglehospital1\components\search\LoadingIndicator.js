'use client'
import { Box, Typography, CircularProgress } from '@mui/material';

const LoadingIndicator = ({ message = "Loading..." }) => {
  return (
    <Box sx={{ 
      textAlign: 'center', 
      py: 4, 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      gap: 2 
    }}>
      <CircularProgress size={40} />
      <Typography variant="body1" color="text.secondary">
        {message}
      </Typography>
    </Box>
  );
};

export default LoadingIndicator;
