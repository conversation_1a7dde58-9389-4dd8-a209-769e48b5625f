import React from "react";
import Specialities from "./Specialities";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import { getWebsiteData } from "../layout"; // Only used when not passed a prop

const getSpecialities = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?list=true`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      return data?.result?.specialities || [];
    }
  } catch (error) {
    console.log("Error fetching specialities:", error);
    return [];
  }
};

const SpecialitiesWrapper = async ({ locationEnterpriseCode }) => {
  let enterpriseCode = locationEnterpriseCode;
  let isChainSpeciality = false;
  // Fallback to homepage logic if not provided (i.e., you're on homepage)
  if (!enterpriseCode) {
    isChainSpeciality = true;
    const websiteData = await getWebsiteData();
    enterpriseCode = websiteData?.enterprise_code || null;
  }

  if (!enterpriseCode) return null;

  const specialities = await getSpecialities(enterpriseCode);

  if (specialities.length <= 1) return null;

  return (
    <Specialities
      isChainSpeciality={isChainSpeciality}
      specialities={specialities}
      enterpriseCode={enterpriseCode}
    />
  );
};

export default SpecialitiesWrapper;
