'use client'
import { Box, Typography, useTheme, useMediaQuery } from '@mui/material';
import SearchResultCard from './SearchResultCard';
import LoadingIndicator from './LoadingIndicator';
import NoResultsMessage from './NoResultsMessage';

const SearchResultsList = ({
  searchQuery,
  searchResults,
  isLoading,
  isItemInCart,
  onAddToCart,
  onRemoveFromCart,
  onNavigate
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  return (
    <>
      <Box sx={{
        mb: 3,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: { xs: 1, sm: 0 }
      }}>
        <Typography sx={{ color: 'text.black' }} variant={isMobile ? "subtitle1" : "h6"}>
          Results for '{searchQuery}'
        </Typography>
        <Typography variant="body2" color="text.secondary">
          (Showing {searchResults?.length || 0} results)
        </Typography>
      </Box>
      <Box sx={{
        maxHeight: 'calc(100vh - 250px)', // Max height to enable scrolling when needed
        overflowY: 'auto', // Auto scroll - only shows scrollbar when needed
        pr: { xs: 1, md: 2 },
        '&::-webkit-scrollbar': {
          width: '6px'
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: '#f1f1f1',
          borderRadius: '10px'
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: '#888',
          borderRadius: '10px',
          '&:hover': {
            backgroundColor: '#555'
          }
        }
      }}>
        {isLoading ? (
          <LoadingIndicator message="Searching..." />
        ) : searchResults && searchResults.length > 0 ? (
          /* Limit to 10 results for better performance */
          searchResults.slice(0, 10).map((result) => (
            <SearchResultCard
              key={result.id}
              result={result}
              isInCart={isItemInCart(result.id, result.isPackage ? 'package' : 'test')}
              onAddToCart={onAddToCart}
              onRemoveFromCart={onRemoveFromCart}
              onNavigate={onNavigate}
            />
          ))
        ) : !isLoading && searchQuery.length >= 3 ? (
          <NoResultsMessage />
        ) : null}
      </Box>
    </>
  );
};

export default SearchResultsList;
