"use client";

import { useState, useRef, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
// import PersonIcon from "@mui/icons-material/Person";
import { useTheme } from "@emotion/react";
import Link from "next/link";
// import EditIcon from "@mui/icons-material/Edit"

const menuItems = [
  {
    id: 1,
    name: "Vision Test",
    icon: "/vision.svg",
    url: "https://tally.so/r/npzG6q",
  },
  {
    id: 2,
    name: "LASIK Test",
    icon: "/cataract.svg",
    url: "https://tally.so/r/mJBpad",
  },
];

export default function FloatingMenuButton() {
  const [isOpen, setIsOpen] = useState(false);
  const buttonRef = useRef(null);
  const menuRef = useRef(null);
  const theme = useTheme();

  //   const handleClick = () => {
  //     setIsOpen(!isOpen);
  //   };

  //   const handleItemClick = (url) => {
  //     window.location.href = url;
  //     setIsOpen(false);
  //   };

  // Toggle menu visibility
  const toggleMenu = () => setIsOpen((prev) => !prev);

  // Close menu if click outside
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(e.target) &&
        !buttonRef.current.contains(e.target)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <Box
      sx={{
        position: "fixed",
        bottom: { xs: 70, md: 20 },
        left: { xs: 20, md: 20 },
        zIndex: 1000,
      }}
    >
      <Box
        ref={buttonRef}
        onClick={toggleMenu}
        sx={{
          display: "flex",
          alignItems: "center",
          backgroundColor: theme.palette.primary.main,
          color: "white",
          borderRadius: "30px",
          padding: "10px 16px",
          cursor: "pointer",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          transition: "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out",
          "&:hover": {
            transform: "scale(1.05)",
            boxShadow: "0 6px 12px rgba(0, 0, 0, 0.3)",
          },
        }}
      >
        <AddIcon sx={{ fontSize: 24 }} />
        <Typography sx={{ marginLeft: 1, fontWeight: 500 }}>
          Self Eye Test
        </Typography>
      </Box>

      {isOpen && (
        <Box
          ref={menuRef}
          sx={{
            position: "absolute",
            bottom: "60px",
            left: 0,
            minWidth: "fit-content",
            maxWidth: "250px",
            backgroundColor: "white",
            borderRadius: "8px",
            boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
            overflow: "hidden",
            animation: "fadeIn 0.2s ease-in-out",
            "@keyframes fadeIn": {
              "0%": {
                opacity: 0,
                transform: "translateY(10px) scale(0.95)",
              },
              "100%": {
                opacity: 1,
                transform: "translateY(0) scale(1)",
              },
            },
          }}
        >
          <Box sx={{ maxHeight: "300px", width: "100%", overflowY: "auto" }}>
            {menuItems.map((item) => (
              <Link target="_blank" href={item.url} key={item.id}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    padding: "12px 12px",
                    cursor: "pointer",
                    transition: "background-color 0.2s ease",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.04)",
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: "40px",
                      height: "40px",
                      borderRadius: "50%",
                      backgroundColor: "#f0f0f0",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "#555",
                      marginRight: "12px",
                    }}
                  >
                    {/* {item.icon} */}
                    <img
                      src={item.icon} // Path to your SVG in the public folder
                      alt="icon"
                      style={{
                        width: "24px",
                        height: "24px",
                      }}
                    />
                  </Box>
                  <Typography
                    sx={{
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {item.name}
                  </Typography>
                </Box>
              </Link>
            ))}
          </Box>
          {/* <Box
            onClick={() => handleItemClick("/compose")}
            sx={{
              display: "flex",
              alignItems: "center",
              padding: "12px 16px",
              backgroundColor: "#212121",
              color: "white",
              cursor: "pointer",
              transition: "background-color 0.2s ease",
              "&:hover": {
                backgroundColor: "#333333",
              },
            }}
          >
            <Box
              sx={{
                width: "40px",
                height: "40px",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginRight: "12px",
              }}
            >
              <EditIcon />
            </Box>
            <Typography>Compose new</Typography>
          </Box> */}
        </Box>
      )}
    </Box>
  );
}
