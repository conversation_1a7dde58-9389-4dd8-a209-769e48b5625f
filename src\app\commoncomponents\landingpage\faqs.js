"use client";

import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Typography,
    Container,
    Paper,
    Grid,
    Fade
} from "@mui/material";
import {styled, useTheme} from "@mui/material/styles";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import HelpOutlineRoundedIcon from '@mui/icons-material/HelpOutlineRounded';
import {useState, useEffect} from "react";
import {keyframes} from "@emotion/react";

// Animation keyframes
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// Styled components
const StyledAccordion = styled(Accordion)(({expanded}) => ({
    margin: '8px 0',
    borderRadius: '12px !important',
    overflow: 'hidden',
    boxShadow: expanded
        ? `0 10px 30px rgba(0, 33, 71, 0.15)`
        : `0 6px 15px rgba(0, 33, 71, 0.08)`,
    border: 'none',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:before': {
        display: 'none',
    },
    '&:hover': {
        boxShadow: `0 10px 30px rgba(0, 33, 71, 0.15)`,
        transform: expanded ? 'none' : 'translateY(-4px)',
    },
    '&.Mui-expanded': {
        margin: '8px 0',
        transform: 'none',
    },
    background: expanded
        ? 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)'
        : '#ffffff',
}));

const StyledAccordionSummary = styled(AccordionSummary)(({theme, expanded}) => ({
    padding: '8px 24px',
    '& .MuiAccordionSummary-content': {
        margin: '4px 0',
        alignItems: 'center',
    },
    '& .MuiAccordionSummary-expandIconWrapper': {
        color: expanded ? theme.palette.primary.main : '#002147',
        transform: expanded ? 'rotate(180deg)' : 'rotate(0)',
        transition: 'transform 0.3s, color 0.3s',
    },
    '&:hover .MuiAccordionSummary-expandIconWrapper': {
        color: theme.palette.primary.main,
    },
}));

const QuestionIcon = styled(Box)(() => ({
    width: 32,
    height: 32,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    backgroundColor: 'rgba(78, 205, 196, 0.1)',
    color: '#4ECDC4',
    flexShrink: 0,
}));

const StyledAccordionDetails = styled(AccordionDetails)(() => ({
    padding: '0 24px 24px 72px',
    position: 'relative',
    '&:before': {
        content: '""',
        position: 'absolute',
        left: 40,
        top: -8,
        bottom: 24,
        width: 2,
        backgroundColor: 'rgba(78, 205, 196, 0.2)',
    },
}));

const Faqs = ({value = [], title = ""}) => {
    const theme = useTheme();
    const [expanded, setExpanded] = useState(false);
    const [visibleItems, setVisibleItems] = useState(5);
    const [showAll, setShowAll] = useState(false);
    const [fadeInItems, setFadeInItems] = useState([]);

    // Handle accordion expansion
    const handleChange = (panel) => (_, isExpanded) => {
        setExpanded(isExpanded ? panel : false);
    };

    // Animate items in sequence
    useEffect(() => {
        const timer = setTimeout(() => {
            const newFadeInItems = [];
            const interval = setInterval(() => {
                if (newFadeInItems.length < value.length) {
                    newFadeInItems.push(newFadeInItems.length);
                    setFadeInItems([...newFadeInItems]);
                } else {
                    clearInterval(interval);
                }
            }, 100);

            return () => clearInterval(interval);
        }, 300);

        return () => clearTimeout(timer);
    }, [value.length]);

    // Show more FAQs
    const handleShowMore = () => {
        setVisibleItems(value.length);
        setShowAll(true);
    };

    return (
        <Box
            sx={{
                backgroundColor: theme.palette.primary.main,
                py: {xs: 4, md: 4},
                px: {xs: 2, md: 4},
                overflow: 'hidden',
                position: 'relative',
            }}
        >
            <Box sx={{
                maxWidth: '1400px',
                mx: 'auto',
                px: {xs: 2, md: 4},
                position: 'relative',
                zIndex: 1,
            }}>
                <Grid container spacing={4} alignItems="center">
                    {/* Left side with title */}
                    <Grid item xs={12} md={4}>
                        <Box sx={{
                            position: 'relative',
                            mb: {xs: 4, md: 0},
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center'
                        }}>
                            <Box sx={{
                                textAlign: {xs: 'center', md: 'left'},
                                position: 'relative',
                                zIndex: 1
                            }}>
                                <Typography
                                    variant="h3"
                                    sx={{
                                        // color: '#4ECDC4',
                                        color: "white",
                                        fontSize: {xs: "1.75rem", sm: "2.25rem", md: "2.5rem"},
                                        fontWeight: 'bold',
                                        position: 'relative',
                                        display: 'inline-block',
                                        // mb: 3,
                                        '&:after': {
                                            content: '""',
                                            position: 'absolute',
                                            bottom: '-12px',
                                            left: {xs: '50%', md: 0},
                                            transform: {xs: 'translateX(-50%)', md: 'none'},
                                            width: '60px',
                                            height: '3px',
                                            // background: 'linear-gradient(90deg, #4ECDC4, #2A93D5)',
                                            background: 'white',
                                            borderRadius: '3px',
                                        }
                                    }}
                                >
                                    {title || "Frequently Asked Questions"}
                                </Typography>

                                <Typography
                                    variant="body1"
                                    sx={{
                                        color: 'rgba(255, 255, 255, 0.89)',
                                        mt: 4,
                                        maxWidth: '400px',
                                        mx: {xs: 'auto', md: 0},
                                        lineHeight: 1.8
                                    }}
                                >
                                    We've compiled a list of the most frequently asked questions to help you find the
                                    information you need quickly and easily.
                                </Typography>

                                <Box
                                    sx={{
                                        mt: 2,
                                        p: 2,
                                        borderRadius: '8px',
                                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                                        backdropFilter: 'blur(10px)',
                                        border: '1px solid rgba(255, 255, 255, 0.1)',
                                        display: {xs: 'none', md: 'block'}
                                    }}
                                >
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            color: 'rgba(255, 255, 255, 0.89)',
                                            fontStyle: 'italic',
                                            lineHeight: 1.6
                                        }}
                                    >
                                        "If you can't find the answer to your question here, please don't hesitate to
                                        contact our support team for assistance."
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </Grid>

                    {/* Right side with accordions */}
                    <Grid sx={{marginTop: {xs: -4, md: "auto"}}} item xs={12} md={8}>
                        <Paper
                            elevation={0}
                            sx={{
                                backgroundColor: 'transparent',
                                borderRadius: '16px',
                                overflow: 'hidden',
                            }}
                        >
                            <Box sx={{display: "flex", flexDirection: "column"}}>
                                {value.slice(0, visibleItems).map((item, index) => {
                                    const {question = "", answer = "", position = null} = item || {};
                                    const panelId = `panel-${index}`;
                                    const isExpanded = expanded === panelId;

                                    return (
                                        <Fade
                                            key={index}
                                            in={fadeInItems.includes(index)}
                                            style={{
                                                transitionDelay: `${index * 100}ms`,
                                                animationDelay: `${index * 100}ms`,
                                            }}
                                        >
                                            <Box sx={{
                                                animation: `${fadeIn} 0.5s ease-out forwards`,
                                                opacity: 0,
                                                animationDelay: `${index * 100}ms`,
                                            }}>
                                                <StyledAccordion
                                                    id={`faq${index}Pos${position}`}
                                                    expanded={isExpanded}
                                                    onChange={handleChange(panelId)}
                                                    disableGutters
                                                >
                                                    <StyledAccordionSummary
                                                        expandIcon={<ExpandMoreIcon/>}
                                                        aria-controls={`${panelId}-content`}
                                                        id={`${panelId}-header`}
                                                        expanded={isExpanded}
                                                    >
                                                        <QuestionIcon>
                                                            <HelpOutlineRoundedIcon fontSize="small"/>
                                                        </QuestionIcon>
                                                        <Typography
                                                            variant="subtitle1"
                                                            sx={{
                                                                fontWeight: isExpanded ? 600 : 500,
                                                                color: isExpanded ? theme.palette.primary.main : '#002147',
                                                                transition: 'color 0.3s',
                                                            }}
                                                        >
                                                            {question || ""}
                                                        </Typography>
                                                    </StyledAccordionSummary>
                                                    <StyledAccordionDetails>
                                                        <Typography
                                                            variant="body1"
                                                            sx={{
                                                                color: 'text.secondary',
                                                                lineHeight: 1.7,
                                                            }}
                                                            dangerouslySetInnerHTML={{__html: answer || ""}}
                                                        />
                                                    </StyledAccordionDetails>
                                                </StyledAccordion>
                                            </Box>
                                        </Fade>
                                    );
                                })}
                            </Box>
                            {!showAll && value.length > visibleItems && (
                                <Box sx={{textAlign: 'center', mt: 4}}>
                                    <Box
                                        onClick={handleShowMore}
                                        sx={{
                                            display: 'inline-flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            py: 1.5,
                                            px: 3,
                                            borderRadius: '30px',
                                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                            color: 'white',
                                            fontWeight: 400,
                                            cursor: 'pointer',
                                            transition: 'all 0.3s ease',
                                            '&:hover': {
                                                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                transform: 'translateY(-3px)',
                                            },
                                        }}
                                    >
                                        Show More FAQs
                                    </Box>
                                </Box>
                            )}
                        </Paper>
                    </Grid>
                </Grid>
            </Box>
        </Box>
    );
};

export default Faqs;
