"use client"
import {useRouter} from "next/navigation";
import {useEffect} from "react";

const UrlReplacer = ({ seoSlug, doctorCode }) => {
    const router = useRouter();
    console.log("check here " +seoSlug)
    console.log("check here "+ doctorCode)

    useEffect(() => {
        // If doctorCode doesn't match seoSlug, redirect to the correct slug
        if (doctorCode !== seoSlug) {
            router.replace(`/doctors/${seoSlug}`);
        }
    }, [doctorCode, seoSlug, router]);

    return null
};

export default UrlReplacer;