import PopularCategories from "./PopularCategories";
import { getEnterpriseCode } from "../blogs/location/[locationCode]/[blogCode]/layout";
import { getLabCategories } from "@/api/harbor.service";

export default async function PopularCategoriesWrapper() {
  // Start with loading state
  const loadingComponent = <PopularCategories isLoading={true} categories={[]} />;

  try {
    const enterpriseCode = await getEnterpriseCode();
    const categoriesData = await getLabCategories(enterpriseCode, { perPage: 12 });

    return <PopularCategories categories={categoriesData} isLoading={false} />;
  } catch (error) {
    console.error("Error in PopularCategoriesWrapper:", error);
    return loadingComponent;
  }
}
