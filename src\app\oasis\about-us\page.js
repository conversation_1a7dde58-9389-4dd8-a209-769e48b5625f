"use client";

import { useContext } from "react";
import { AppContext } from "../../AppContextLayout";
import parse from "html-react-parser";
import SectionLayoutOasis from "../styledComponents/SectionLayoutOasis";

export default function AboutUs() {
  const { websiteData } = useContext(AppContext);
  const { about_us: aboutUs = "" } = websiteData || {};
  return <SectionLayoutOasis><div
      className="ck-content"
      dangerouslySetInnerHTML={{__html: aboutUs}} // Render HTML safely
  /></SectionLayoutOasis>;
}
