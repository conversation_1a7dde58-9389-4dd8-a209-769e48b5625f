import { AppRouterCacheProvider } from "@mui/material-nextjs/v14-appRouter";
import CustomThemeProvider from "../../CustomThemeProvider";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PLUGINS,
} from "@/constants";
import Structure from "./components/structure";
import "../globals.css";
// import { Roboto } from "next/font/google";
import { Figtree } from "next/font/google";
import "ckeditor5/ckeditor5.css";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import CustomThemeProviderSingleSpcMultiChain2 from "@/app/singlespecialitymultichain2/CustomThemeProviderSingleSpcMultiChain2";
import { GoogleTagManager, GoogleAnalytics } from "@next/third-parties/google";
import AppContextLayout from "../AppContextLayout";

// const roboto = Roboto({
//   weight: ['300', '400', '500', '700'],
//   subsets: ['latin'],
//   display: 'swap',
//   variable: '--font-roboto',
// });
const figtree = Figtree({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-figtree",
});

export const generateMetadata = async () => {
  // const domainName = headers().get("host");
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    const { seoData = [] } = jsonRes?.result || {};
    const { title = "", meta_description: metaDescription = "" } =
      seoData[0] || {};
    return {
      title: title || "My Hospital",
      description:
        metaDescription ||
        "A trusted platform for personalized patient care, offering tailored solutions and comprehensive services, prioritising comfort and satisfaction.",
    };
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};
export const getWebsiteData = async () => {
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

const getPlugins = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PLUGINS}?details=true`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    return jsonRes?.result;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};
export default async function Serene({ children }) {
  const websiteData = await getWebsiteData();
  const {
    template = {},
    favicon_url: faviconUrl = "",
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};
  const {
    primary_font_family_cdn: fontFamilyCdn = "",
    primary_font_family: fontFamily = "",
  } = template || {};
  const configData = await getPlugins(enterpriseCode);
  const { ga = [], gtm = [] } = configData || {};
  const { configValue: gaConfigValue = null } = ga[0] || {};
  const { configValue: gtmConfigValue = null } = gtm[0] || {};

  return (
    <html lang="en">
      <link rel="icon" href={getThumborUrl(faviconUrl, 32, 32)} sizes="any" />
      <body style={{ height: "100%" }} className={figtree.variable}>
        <AppRouterCacheProvider options={{ key: "css" }}>
          {/* <AppContextLayoutSingleSpecialityMultiChain2
            websiteData={websiteData}
          > */}
          <AppContextLayout websiteData={websiteData}>
            <CustomThemeProviderSingleSpcMultiChain2
              template={template}
              fontFamily={fontFamily}
            >
              <Structure children={children} />
            </CustomThemeProviderSingleSpcMultiChain2>
          </AppContextLayout>
          {/* </AppContextLayoutSingleSpecialityMultiChain2> */}
        </AppRouterCacheProvider>

        {gaConfigValue && <GoogleAnalytics gaId={gaConfigValue} />}
        {gtmConfigValue && <GoogleTagManager gtmId={gtmConfigValue} />}
      </body>
    </html>
  );
}
