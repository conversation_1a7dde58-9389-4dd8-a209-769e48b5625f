"use client";
import { useState, useEffect, useRef } from "react";
import { ArrowRight } from "@mui/icons-material";
import {
  Box,
  Container,
  Typography,
  CardContent,
  CardActions,
  Button,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { Swiper, SwiperSlide } from "swiper/react";
import {Autoplay, Pagination} from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { useRouter } from "next/navigation";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";
import { getThumborUrl } from "@/app/utils/getThumborUrl";

const useObserver = () => {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );

    if (ref.current) observer.observe(ref.current);

    return () => observer.disconnect();
  }, []);

  return [ref, isVisible];
};

const StyledBox = styled(Box)(({ theme }) => ({
  cursor: "pointer",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  transition: "transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out",
  "&:hover": {
    transform: "scale(1.01)",
    boxShadow: theme.shadows[8],
    "& .arrow-icon": {
      transform: "translateX(4px)",
    },
    "& .read-more-text": {
      color: theme.palette.primary.main,
    },
  },
  borderRadius: theme.spacing(2),
  overflow: "hidden",
  backgroundColor: "rgba(205, 205, 205, 0.14)",
}));

const StyledMedia = styled(Box)(({ theme }) => ({
  cursor: "pointer",
  paddingTop: "60%",
  position: "relative",
  backgroundSize: "cover",
  backgroundPosition: "center",
  "&::after": {
    content: '""',
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "30%",
    background:
      "linear-gradient(to top, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%)",
  },
}));

const StyledButton = styled(Typography)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: "8px",
  cursor: "pointer",
  transition: "all 0.3s ease",
  "& .read-more-text": {
    fontSize: "0.875rem",
    color: "#1a1a1a",
    transition: "color 0.3s ease",
  },
  "& .arrow-icon": {
    fontSize: "1.2rem",
    transition: "transform 0.3s ease",
    color: "#1a1a1a",
  },
}));

export default function BlogSection({ blogs = [] }) {

  if (blogs.length === 0 ){
      return (
          <></>
      )
  }


  const [cardsRef, cardsVisible] = useObserver();
  const router = useRouter();

  return (
    <SectionLayoutSingleSpecialitySingleHospital>
      <Box
        component="section"
        sx={{
          position: "relative",
          py: { xs: 4, md: 3 },
          px: { xs: 2, md: 0 },
          maxWidth: "2150px",
          mx: "auto",
          width: "100%",
        }}
      >
        <Box sx={{ mb: 3, textAlign: "center" }}>
          <Typography
            variant="h5"
            sx={{
              color: "primary.main",
              mb: 1,
              display: "block",
            }}
          >
            OUR BLOGS
          </Typography>
          <Typography
            variant="h3"
            component="h2"
            sx={{
              color: "#1a1a1a",
              mb: 2,
            }}
          >
            Latest Articles & Blogs
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "#666",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Stay informed with our latest insights, discoveries, and expert
            advice on eye care and vision health.
          </Typography>
        </Box>

        <Box
          ref={cardsRef}
          sx={{
            opacity: cardsVisible ? 1 : 0,
            transform: cardsVisible ? "translateY(0)" : "translateY(20px)",
            transition: "opacity 0.6s ease-out, transform 0.6s ease-out",
          }}
        >
          <Swiper
           modules={[Pagination, Autoplay]}
            spaceBetween={20}
            slidesPerView={1}
            loop={true}
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 30,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 30,
              },
            }}
            style={{ padding: "10px 0px 50px 0" }}
          >
            
              {blogs.map((blog, index) => {
                  const {
                      code = null,
                      content = "",
                      // blogImage = "/banner.webp",
                      title = "",
                      imageUrl = "",
                      seoSlug,
                  } = blog;
                return (
                  <SwiperSlide>
                  <Box
                    key={index}
                    onClick={() => {
                      if (seoSlug) router.push(`/blogs/${seoSlug}`);
                    }}
                  >
                    <StyledBox>
                      <StyledMedia
                        sx={{
                          backgroundImage: `url(${imageUrl || "/blogs-default.avif"})`,
                        }}
                      />

                      <CardContent sx={{ flexGrow: 1, px: 2 }}>
                        <Typography
                          variant="h6"
                          sx={{
                            color: "#1a1a1a",
                            lineHeight: 1.3,
                          }}
                        >
                          {title}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2, pt: 0 }}>
                        <StyledButton>
                          <span className="read-more-text">
                            Continue Reading
                          </span>
                          <ArrowRight className="arrow-icon" />
                        </StyledButton>
                      </CardActions>
                    </StyledBox>
                  </Box>
            </SwiperSlide>
                );
              })}
          </Swiper>
          <Box sx={{ display: "flex", mt: 3, justifyContent: "center" }}>
            <Button
              onClick={() => router.push("/blogs")}
              color="primary"
              sx={{
                borderRadius: "100px",
                backgroundColor: "primary.main",
                transform: "scale(1)",
                color: "text.primary",
                textTransform: "capitalize",
                fontWeight: "normal",
                fontSize: "14px",
                padding: "10px 20px",
                transition: "transform 0.3s ease",
                "&:hover": {
                  backgroundColor: "primary.main",
                },
              }}
            >
              View All Blogs
            </Button>
          </Box>
        </Box>
      </Box>
    </SectionLayoutSingleSpecialitySingleHospital>
  );
}
