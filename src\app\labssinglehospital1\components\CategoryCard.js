"use client";
import { Box, Paper, Typography, useTheme } from "@mui/material";
import Image from "next/image";
// import MedicationIcon from "@mui/icons-material/Medication";

const CategoryCard = ({ category, onClick }) => {
  const theme = useTheme();
  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        border: "1px solid #e0e0e0",
        borderRadius: "8px",
        display: "flex",
        alignItems: "center",
        gap: 1.5,
        height: "100%",
        minHeight: "72px",
        cursor: "pointer",
        "&:hover": {
          boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
        },
      }}
      onClick={onClick}
    >
      <Box position="relative" sx={{ width: 40, height: 40 }}>
        <Box
          sx={{
            width: 40,
            height: 40,
            backgroundColor: theme.palette.primary.main,
            borderRadius: "50%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: theme.palette.common.white,
            overflow: "hidden",
          }}
        >
          {category.iconUrl ? (            
            <Image
              src={category.iconUrl}
              alt={`${category.name || 'Category'} icon`}
              width={24}
              height={24}
              style={{ objectFit: "contain" }}
            />
          ) : (
            <Image

            src='/labs-category.png'
            alt={`${category.name || 'Category'} icon`}
            width={24}
            height={24}
            style={{ objectFit: "contain", }}
          />
          )}
        </Box>
      </Box>

      <Typography variant="body2" sx={{ fontWeight: 500,color: "text.black" }}>
        {category.name}
      </Typography>
    </Paper>
  );
};

export default CategoryCard;
