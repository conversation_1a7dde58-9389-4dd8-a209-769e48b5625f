/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "ips.docfyn.com",
        port: "",
        pathname: "/unsafe/**",
      },
      {
        protocol: "https",
        hostname: "cdn.docfyn.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "s3.ap-south-1.amazonaws.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "paradise-staging.mydocsite.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  reactStrictMode: false,
  compress: true, // Enable gzip/brotli compression
  swcMinify: true, // Enable SWC-based minification for faster builds
};

export default nextConfig;
