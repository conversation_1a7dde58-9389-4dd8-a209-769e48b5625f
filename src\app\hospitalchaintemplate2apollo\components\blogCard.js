"use client";

import {Box, Typography} from "@mui/material";
import useStyles from "../styles";
import {getThumborUrl} from "../../utils/getThumborUrl";
import {useRouter} from "next/navigation";
import Image from "next/image";

const BlogCard = ({blog = {}, id = null}) => {
    const classes = useStyles();
    const router = useRouter();
    const {
        code = null,
        content = "",
        image_url: blogImage = "",
        title = "",
        imageUrl = "",
        seoSlug = "",
        centerDetails : {
            domain_slug: domainSlug = ""
        } = {}
    } = blog || {};

    const handleBlogRedirection = () => {
        if (seoSlug && domainSlug){
            router.push(`/blogs/location/${domainSlug}/${seoSlug}`);
        }
        else if(seoSlug) router.push(`/blogs/${seoSlug}`);

    };

    return (
        <Box
            id={id}
            sx={{
                borderRadius: "8px",
                boxShadow: "0 1px 10px rgba(0,0,0,.1)",
                cursor: "pointer",
            }}
            onClick={handleBlogRedirection}
        >
            <Box sx={{position: "relative"}}>
                {blogImage || imageUrl ? (
                    <Image
                        alt={title}
                        src={blogImage ? getThumborUrl(blogImage) : getThumborUrl(imageUrl)}
                        height={150}
                        width={195}
                        style={{
                            objectFit: "cover",
                            width: "100%",
                            objectPosition: "center",
                            borderTopLeftRadius: "8px",
                            borderTopRightRadius: "8px",
                        }}
                    />
                ) : (
                    <Box
                        sx={{
                            background: "rgba(0, 0, 0, 0.15)",
                            height: "150px",
                            width: "100%",
                            borderTopLeftRadius: "8px",
                            borderTopRightRadius: "8px",
                        }}
                    ></Box>
                )}
                {/* <Box className={classes.blogsSectionBlogItemType}>
          <span style={{ color: "#fff" }}>Oncology</span>
        </Box> */}
            </Box>
            <Box sx={{padding: "16px"}}>
                <Typography
                    variant="h6"
                    sx={{
                        fontSize: "20px",
                        lineHeight: "1.2",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        display: "-webkit-box",
                        WebkitLineClamp: "1",
                        WebkitBoxOrient: "vertical",
                    }}
                >
                    {title || ""}
                </Typography>
                {/* <Typography
          variant="subtitle1"
          sx={{ fontSize: "14px", color: "#696969", mt: 1 }}
        >
          By Dr Vinod Khanna
        </Typography> */}
            </Box>
        </Box>
    );
};

export default BlogCard;
