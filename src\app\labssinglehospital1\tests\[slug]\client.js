"use client";

import React from "react";
import { Container, Grid } from "@mui/material";
import TestDetailHeader from "../../components/TestDetailHeader";
import TestsIncludedSection from "../../components/TestsIncludedSection";
import TestAboutSection from "../../components/TestAboutSection";
import TestFAQSection from "../../components/TestFAQSection";
import MobileBookButton from "../../components/MobileBookButton";

export default function TestDetailPage({ data }) {
  const { itemData, itemType, faqs = [] } = data || {};

  // Format the data for the components
  const formattedData = {
    id: itemData.code,
    title: itemData.name,
    subtitle: itemData.shortDescription || itemData.alternativeNames,
    description: itemData.description,
    discountedPrice: itemData.discountedPrice,
    originalPrice: itemData.originalPrice,
    discount: `${itemData.discountPercentage}% off`,
    reportHours: itemData.turnaroundTime,
    preparation: itemData.preparationInstructions,
    sampleType: itemData.sampleType,
    applicableGender: itemData.applicableGender,
    targetAgeGroup: itemData.targetAgeGroup,
    reportDeliveryMethod: itemData.reportDeliveryMethod,
    testDuration: itemData.testDuration,
    totalTests: itemData.totalTestsIncluded,
    itemType: itemType,
    testsIncludedText: `${itemData.totalTestsIncluded || 0} Tests included`,
    testsIncluded:
      itemType === "test"
        ? itemData.labTestItems?.map((item) => ({
            name: item.name,
            count: 1,
            description: item.shortDescription || "",
            labTestItems: null, // No nested items for individual tests
          })) || []
        : itemData.labTests?.map((test) => ({
            name: test.name,
            count: test.totalTestsIncluded || 1,
            description: test.shortDescription || test.alternativeNames || "",
            labTestItems: test.labTestItems || [], // Include nested test items for packages
          })) || [],
    faqs: faqs,
  };

  return (
    <>
      <Container
        maxWidth="xl"
        sx={{ py: 4, pb: { xs: 10, sm: 4 }, mt: { md: 2 } }}
      >
        <Grid container spacing={3}>
          {/* Left Column - Smaller on desktop */}
          <Grid item xs={12} md={4}>
            <TestDetailHeader testData={formattedData} />
          </Grid>

          {/* Right Column - Larger on desktop */}
          <Grid item xs={12} md={8}>
            <TestsIncludedSection
              testsIncluded={formattedData.testsIncluded}
              totalTests={formattedData.totalTests}
              itemType={formattedData.itemType}
            />

            <TestAboutSection testData={formattedData} />

            {formattedData.faqs.length > 0 && (
              <TestFAQSection faqs={formattedData.faqs} />
            )}
          </Grid>
        </Grid>
      </Container>

      {/* Mobile sticky book button */}
      <MobileBookButton testData={formattedData} />
    </>
  );
}
