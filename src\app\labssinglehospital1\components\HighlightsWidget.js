'use client';

import React, { useContext } from 'react';
import { Box, Typography, Container, Grid, Paper, useTheme, useMediaQuery } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { AppContext } from '@/app/AppContextLayout';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const HighlightsWidget = () => {
  const { websiteData } = useContext(AppContext);
  const { highlights = [] } = websiteData || {};
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Function to handle redirection based on highlight data
  const handleRedirection = (highlight) => {
    if (!highlight.highlightsRedirection) return;

    const { redirectionUrl } = highlight.highlightsRedirection;

    if (redirectionUrl) {
      router.push(redirectionUrl);
    }
  };

  // Generate background and text colors based on index
  const getColors = (index) => {
    const colorSets = [
      { bg: '#eaf6ef', color: '#1a866b' },
      { bg: '#e6f3fc', color: '#0069b4' },
      { bg: '#fff8e8', color: '#c77700' },
      { bg: '#f9e9f6', color: '#9c27b0' }
    ];

    return colorSets[index % colorSets.length];
  };

  // If no highlights data, don't render the component
  if (!highlights || highlights.length === 0) {
    return null;
  }

  return (
    <Container
      maxWidth={isTablet ? "md" : "xl"}
      sx={{
        my: { xs: 2, sm: 3, md: 4 },
        px: { xs: 2, sm: 3 }
      }}
    >
      <Grid container spacing={isMobile ? 2 : 2.5}>
        {highlights.map((highlight, index) => {
          const { bg, color } = getColors(index);

          return (
            <Grid item xs={12} sm={6} md={4} lg={3} key={highlight.code || index}>
              <Paper
                elevation={0}
                onClick={() => handleRedirection(highlight)}
                sx={{
                  backgroundColor: bg,
                  borderRadius: '16px',
                  p: 1.5,
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 4px 10px rgba(0,0,0,0.08)',
                  },
                }}
              >
                <Box
                  sx={{
                    borderRadius: '50%',
                    width: { xs: 40, sm: 42 },
                    height: { xs: 40, sm: 42 },
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    mr: 2,
                    boxShadow: '0 2px 6px rgba(0,0,0,0.06)',
                    overflow: 'hidden',
                    position: 'relative'
                  }}
                >
                  {highlight.imageUrl ? (
                    <Image
                      src={highlight.imageUrl}
                      alt={highlight.title || 'Highlight'}
                      width={32}
                      height={32}
                      style={{ objectFit: 'contain' }}
                    />
                  ) : (
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        backgroundColor: color,
                        borderRadius: '50%'
                      }}
                    />
                  )}
                </Box>

                <Box sx={{ flexGrow: 1, py: 0.5 }}>
                  <Typography
                    variant="subtitle1"
                    component="h3"
                    fontWeight="600"
                    sx={{
                      color: color,
                      fontSize: { xs: '0.9rem', sm: '0.95rem' },
                      mb: highlight.shortDescription ? 0.25 : 0
                    }}
                  >
                    {highlight.title || 'Highlight'}
                  </Typography>
                  {highlight.shortDescription && (
                    <Typography
                      variant="body2"
                      sx={{
                        color: 'rgba(0,0,0,0.6)',
                        fontSize: { xs: '0.8rem', sm: '0.85rem' },
                      }}
                    >
                      {highlight.shortDescription}
                    </Typography>
                  )}
                </Box>

                <ArrowForwardIcon sx={{
                  color: color,
                  fontSize: { xs: 18, sm: 20 },
                  ml: 1,
                  opacity: 0.9
                }} />
              </Paper>
            </Grid>
          );
        })}
      </Grid>
    </Container>
  );
};

export default HighlightsWidget;
