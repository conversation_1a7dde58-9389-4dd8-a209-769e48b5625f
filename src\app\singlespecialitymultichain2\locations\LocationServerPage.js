import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
} from "@/constants";

import LocationClientPage from "./LocationClientPage";
import SpecialitiesWrapper from "../components/SpecialitiesWrapper";

const getWebsiteData = async (locationCode) => {
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&domainSlug=${locationCode}`;

  try {
    const res = await fetch(url, { cache: "no-store" });
    const json = await res.json();
    return json?.result || {};
  } catch (error) {
    console.log("getWebsiteData Error:", error);
    return {};
  }
};

export default async function LocationServerPage({ params }) {
  const { locationCode } = params || {};
  const websiteData = await getWebsiteData(locationCode);
  const { enterprise_code: locationEnterpriseCode } = websiteData || {};
  const renderedSpecialities = await SpecialitiesWrapper({ locationEnterpriseCode });


  return (
    <>
    
      <LocationClientPage websiteData={websiteData} renderedSpecialities={renderedSpecialities} />

    </>
  );
}
