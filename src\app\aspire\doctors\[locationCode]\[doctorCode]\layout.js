import { getEnterpriseCode } from "@/app/aspire/blogs/location/[locationCode]/[blogCode]/layout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_ENDPOINT_PROFILE,
} from "@/constants";
import axios from "axios";

export const generateMetadata = async ({ params }) => {
  const { doctorCode = "", locationCode = null } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode(locationCode);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_ENDPOINT_PROFILE}${doctorCode}?enterpriseCode=${enterpriseCode}`;
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { seoTitle = "", seoDescription = "" } = result || {};

      return {
        title: seoTitle || "",
        description: seoDescription || "",
      };
    }
  } catch (error) {
    console.log("generateMetadata", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
