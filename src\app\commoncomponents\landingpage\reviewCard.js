"use client";

import { <PERSON>, <PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogTitle, IconButton, Fade } from "@mui/material";
import { keyframes } from '@emotion/react';
import { getInitials } from "@/app/utils/getInitials";
import { useState, useRef, useEffect } from "react";
import CloseIcon from '@mui/icons-material/Close';

// Animation for the card
const fadeIn = keyframes`
  from { opacity: 0; transform: scale(0.98); }
  to { opacity: 1; transform: scale(1); }
`;

const ReviewCard = ({ testimonial = {} }) => {
  const { name = "", rating = null, text = "" } = testimonial || {};
  const [openDialog, setOpenDialog] = useState(false);
  const textRef = useRef(null);

  // Check if text is overflowing to add the has-overflow class
  useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        // Check if the content height is greater than the visible height
        const isOverflowing = textRef.current.scrollHeight > textRef.current.clientHeight;

        if (isOverflowing) {
          textRef.current.classList.add('has-overflow');
        } else {
          textRef.current.classList.remove('has-overflow');
        }
      }
    };

    // Check on initial render
    checkOverflow();

    // Also check on window resize
    window.addEventListener('resize', checkOverflow);

    return () => {
      window.removeEventListener('resize', checkOverflow);
    };
  }, [text]);

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: 'white',
          borderRadius: '10px',
          boxShadow: '0 6px 16px rgba(0, 0, 0, 0.08)',
          p: 2,
          height: '170px', // Fixed height for all cards
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          animation: `${fadeIn} 0.5s ease-out`,
          position: 'relative',
          overflow: 'hidden',
          cursor: 'pointer',
          '&:hover': {
            boxShadow: '0 10px 24px rgba(0, 0, 0, 0.12)',
            transform: 'translateY(-5px)',
          },
        }}
        onClick={handleOpenDialog}
      >
        {/* Patient name with initials and stars - All at the top before divider */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          pb: 1.5,
          borderBottom: '1px solid rgba(0,0,0,0.06)',
        }}>
          {/* Initials on the left */}
          <Box sx={{
            width: 40,
            height: 40,
            borderRadius: '50%',
            backgroundColor: '#002147',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.875rem',
            fontWeight: 'bold',
            mr: 1.5,
            flexShrink: 0
          }}>
            {getInitials(name)}
          </Box>

          {/* Name and stars in a column on the right */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          }}>
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 600,
                color: '#002147',
                mb: 0.5
              }}
            >
              {name || "Patient"}
            </Typography>

            <Rating
              name="read-only"
              value={rating || 5}
              readOnly
              precision={0.5}
              size="small"
              sx={{
                color: '#FFCA28',
                '& .MuiRating-iconFilled': {
                  color: '#FFCA28',
                },
                '& .MuiRating-iconEmpty': {
                  color: 'rgba(255, 202, 40, 0.3)',
                }
              }}
            />
          </Box>
        </Box>

        {/* Review text section */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', mt: 1.5 }}>

          {/* Review text - Now at the bottom */}
          <Box sx={{ position: 'relative', flex: 1 }}>
            <Typography
              ref={textRef}
              variant="body1"
              className="review-text"
              sx={{
                fontSize: '1rem',
                lineHeight: 1.5,
                color: '#333',
                maxHeight: '4.5rem', // Fixed height for 3 lines
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                position: 'relative',
                '&.has-overflow::after': {
                  opacity: 1,
                },
                '&.has-overflow + .read-more': {
                  display: 'block',
                },
                '&:not(.has-overflow) + .read-more': {
                  display: 'none',
                },
              }}
            >
              {text || "This dental clinic provides excellent service with a friendly staff and modern facilities."}
            </Typography>

            <Typography
              className="read-more"
              variant="caption"
              sx={{
                color: 'primary.main',
                fontWeight: 'bold',
                display: 'none', /* Hidden by default, shown via CSS when needed */
                textAlign: 'right',
                // mt: 0.5,
                cursor: 'pointer',
                position: 'absolute',
                bottom: 0,
                right: 0,
              }}
              onClick={(e) => {
                e.stopPropagation();
                setOpenDialog(true);
              }}
            >
              Read More
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Full Review Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        TransitionComponent={Fade}
        TransitionProps={{ timeout: 300 }}
        PaperProps={{
          sx: {
            borderRadius: '10px',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{
          borderBottom: '1px solid rgba(0,0,0,0.08)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          py: 1,
          px: 2,
          // pb: 1.5,
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: '#002147',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1rem',
              fontWeight: 'bold',
              mr: 1
            }}>
              {getInitials(name)}
            </Box>
            <Box sx={{display:"flex", alignItems:"flex-start", flexDirection:"column", gap:0.5}}>
              <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem',color: '#333' }}>
                {name || "Patient"}
              </Typography>
              <Rating
                value={rating || 5}
                readOnly
                precision={0.5}
                size="small"
                sx={{
                  color: '#FFCA28',
                  '& .MuiRating-iconFilled': {
                    color: '#FFCA28',
                  },
                  '& .MuiRating-iconEmpty': {
                    color: 'rgba(255, 202, 40, 0.3)',
                  }
                }}
              />
            </Box>
          </Box>
          <IconButton
            edge="end"
            color="inherit"
            sx={{ color: '#333' }}
            onClick={handleCloseDialog}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
          <DialogContent sx={{mt:2  }}>
          <Typography variant="body1" sx={{ lineHeight: 1.7, color: '#333' }}>
            {text || "This dental clinic provides excellent service with a friendly staff and modern facilities."}
          </Typography>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ReviewCard;
