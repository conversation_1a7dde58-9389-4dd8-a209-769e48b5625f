"use client";

import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Chip from "@mui/material/Chip";
import InputBase from "@mui/material/InputBase";
import Pagination from "@mui/material/Pagination";
import Typography from "@mui/material/Typography";
import SectionLayoutAspire from "../styledComponents/SectionLayoutAspire";
import FaqsSection from "../components/faqsSection";
import BlogCard from "../components/blogCard";
import SearchIcon from "@mui/icons-material/Search";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_BLOGS,
} from "@/constants";
import { useContext, useEffect, useState } from "react";
import { AppContext } from "../../AppContextLayout";
import axios from "axios";
import { Skeleton } from "@mui/material";

export default function Blogs() {
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const [blogs, setBlogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  const getBlogs = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?list=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        setBlogs(result);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getBlogs();
  }, [enterpriseCode]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayoutAspire>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: { xs: "32px", md: "64px" },
          }}
        >
          <Typography variant="h4">Blogs</Typography>
          <Box
            sx={
              {
                // display: "flex",
                // flexDirection: { xs: "column-reverse", md: "row" },
                // gap: "48px",
              }
            }
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: "48px",
                flex: 1,
              }}
            >
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: {
                    xs: "1fr",
                    sm: "1fr 1fr",
                    md: "repeat(3, 1fr)",
                    lg: "repeat(4, 1fr)",
                  },
                  gap: "32px",
                }}
              >
                {isLoading
                  ? [1, 2, 3, 4, 5, 6].map((_, index) => {
                      return (
                        <Skeleton
                          key={index}
                          variant="rounded"
                          animation="wave"
                          height={200}
                        />
                      );
                    })
                  : blogs.map((blog, index) => {
                      const { code = null } = blog || {};
                      return <BlogCard id={`blog${index}`} key={code} blog={blog} />;
                    })}
              </Box>
              {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
                <Pagination count={10} variant="outlined" shape="rounded" />
              </Box> */}
            </Box>
            {/* <Box
              sx={{
                display: { xs: "none", md: "flex" },
                flexDirection: "column",
                gap: "24px",
                position: { xs: "static", md: "sticky" },
                top: { xs: "0", md: "200px" },
                alignSelf: "start",
                width: { xs: "100%", md: "400px" },
              }}
            >
              <div
                style={{
                  backgroundColor: "#fff",
                  border: "1px solid #d3d4db",
                  padding: "8px 12px",
                  borderRadius: "6px",
                  display: "flex",
                  alignItems: "center",
                  gap: "16px",
                }}
              >
                <InputBase placeholder="Search blog" fullWidth />
                <SearchIcon />
              </div>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  gap: "16px",
                }}
              >
                <Typography variant="h5">Category</Typography>
                <Button variant="text" sx={{ textTransform: "none" }}>
                  Clear all
                </Button>
              </Box>
              <Box sx={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
                <Chip label="Cardiac Sciences" onClick={() => {}} />
                <Chip label="Oncology" />
                <Chip label="Urology" />
                <Chip label="Vascular Surgery" />
                <Chip label="ENT" />
              </Box>
            </Box> */}
          </Box>
        </Box>
      </SectionLayoutAspire>
      {/* <FaqsSection /> */}
    </Box>
  );
}
