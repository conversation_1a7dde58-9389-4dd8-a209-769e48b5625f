import { Box, Dialog } from "@mui/material";
import { getThumborUrl } from "../utils/getThumborUrl";
import Image from "next/image";
import { useState } from "react";

const PhotoReview = ({ imageUrl = "", index }) => {
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Box
      sx={{
        position: "relative",
        height: "64px",
        width: "64px",
        overflow: "hidden",
      }}
      index={index}
    >
      <Image
        src={getThumborUrl(imageUrl)}
        fill
        style={{
          borderRadius: "4px",
          objectFit: "cover",
          cursor: "pointer",
        }}
        onClick={handleClickOpen}
      />
      <Dialog
        open={open}
        onClose={handleClose}
        keepMounted
        sx={{
          ".MuiDialog-paper": {
            maxHeight: "calc(100vh - 64px)",
            maxWidth: "calc(100vw - 64px)",
            padding: 0,
            borderRadius: "8px",
            overflow: "hidden",
          },
        }}
      >
        <img
          alt="slider1"
          src={getThumborUrl(imageUrl)}
          style={{
            maxHeight: "calc(100vh - 64px)",
            maxWidth: "calc(100vw - 64px)",
            objectFit: "contain",
            borderRadius: "8px",
            objectPosition: "center",
          }}
        />
      </Dialog>
    </Box>
  );
};

export default PhotoReview;
