import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  items: [],
  totalQuantity: 0,
  totalAmount: 0,
};

export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action) => {
      const newItem = action.payload;
      newItem.quantity = 1; // Always set to 1
      // Make sure the item has a type (test or package)
      if (!newItem.itemType) {
        newItem.itemType = 'test'; // Default to test if not specified
      }

      // Check if the item with the same ID AND type already exists
      const existingItem = state.items.find(item =>
        item.id === newItem.id && item.itemType === newItem.itemType
      );

      if (!existingItem) {
        // Only add the item if it doesn't already exist
        state.items.push({ ...newItem });

        // Update total quantity and amount
        state.totalQuantity = state.items.length;
        state.totalAmount = state.items.reduce((total, item) => {
          // Use originalPrice if discountPercentage is null or 0, otherwise use discountedPrice
          const price = (!item.discountPercentage || item.discountPercentage <= 0)
            ? item.originalPrice
            : item.discountedPrice;
          return total + (price * item.quantity);
        }, 0);
      }
      // If item already exists, do nothing
    },

    removeFromCart: (state, action) => {
      // We can receive either an ID or an object with id and itemType
      let id, itemType;

      if (typeof action.payload === 'object') {
        // If we received an object with id and itemType
        id = action.payload.id;
        itemType = action.payload.itemType;
        // Remove the specific item with matching id AND itemType
        state.items = state.items.filter(item => !(item.id === id && item.itemType === itemType));
      } else {
        // Backward compatibility: if we just received an ID
        id = action.payload;
        // Remove all items with this ID (regardless of type)
        state.items = state.items.filter(item => item.id !== id);
      }

      // Update total quantity and amount
      state.totalQuantity = state.items.length;
      state.totalAmount = state.items.reduce((total, item) => {
        // Use originalPrice if discountPercentage is null or 0, otherwise use discountedPrice
        const price = (!item.discountPercentage || item.discountPercentage <= 0)
          ? item.originalPrice
          : item.discountedPrice;
        return total + (price * item.quantity);
      }, 0);
    },

    clearCart: (state) => {
      state.items = [];
      state.totalQuantity = 0;
      state.totalAmount = 0;
    },
  },
});

// Export actions
export const { addToCart, removeFromCart, clearCart } = cartSlice.actions;

// Selectors
export const selectCartItems = (state) => state.cart.items;
export const selectCartTotalQuantity = (state) => state.cart.totalQuantity;
export const selectCartTotalAmount = (state) => state.cart.totalAmount;
export const selectIsItemInCart = (id, itemType = 'test') => (state) =>
  state.cart.items.some(item => item.id === id && item.itemType === itemType);

export default cartSlice.reducer;
