import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import VideosClient from "./VideosClient";
import {
  getHomeComponentsData,
  getHomeSectionHeadings,
} from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";

const getMultimediaData = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData(
      { domainName: domainName },
      HOME_WIDGET_TYPE.MULTIMEDIA
    );

    if (data.code === 200) {
      const multimedia = data?.result?.multiMedia || [];
      const aggregatedTags = data?.result?.aggregatedTags || [];
      const videos = multimedia?.filter((item) => item.video_url);

      return {
        videos,
        aggregatedTags,
      };
    }
    return [];
  } catch (error) {
    console.error("Error fetching multimedia data:", error);
    return { videos: [], aggregatedTags: [] };
  }
};

const getVideosHeadings = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeSectionHeadings(
      { domainName: domainName },
      HOME_SECTION_HEADING_TYPE.VIDEOS
    );
    if (data.code === 200) {
      return data?.result || [];
    } else return [];
  } catch (error) {
    console.error("Error fetching banner data:", error);
    return [];
  }
};
export default async function VideosSection() {
  const { videos } = await getMultimediaData();
  const headings = await getVideosHeadings();
  const allTags = videos.reduce((tags, video) => {
    video?.tags?.forEach((tag) => {
      if (!tags.includes(tag.name)) {
        tags.push(tag.name);
      }
    });
    return tags || [];
  }, []);

  if (videos.length === 0) return <></>;

  return (
    <VideosClient
      // aggregatedTags={aggregatedTags}
      tags={allTags}
      videos={videos}
      headings={headings}
    />
  );
}
