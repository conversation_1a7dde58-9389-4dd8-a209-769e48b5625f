"use client";

import { Box, Typography } from "@mui/material";
import { useRouter } from "next/navigation";

export default function ThankyouPage() {
  const router = useRouter();
  return (
    <Box
      sx={{
        height: "100vh",
        padding: { xs: "64px 16px", sm: "64px", md: "64px 160px" },
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
        }}
      >
        <Box
          sx={{
            height: "calc(100vh - 128px)",
            display: "grid",
            gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
            alignItems: "center",
            justifyContent: "center",
            gap: "48px",
          }}
        >
          <img
            alt="thankyou"
            src="/thankyou.svg"
            style={{ height: "100%", width: "100%", objectFit: "contain" }}
          />
          <Box>
            <Typography
              variant="h4"
              sx={{
                color: "#00BFA6",
                fontSize: { xs: "2.5rem", sm: "4rem" },
                textAlign: { xs: "center", md: "left" },
              }}
            >
              Thank You !
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                color: "#00BFA6",
                fontSize: { xs: "1rem", sm: "1.2rem" },
                textAlign: { xs: "center", md: "left" },
              }}
            >
              We will get in touch with you shortly !
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
