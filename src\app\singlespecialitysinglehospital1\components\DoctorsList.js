import { Box, Grid } from "@mui/material";
import DoctorC<PERSON> from "./DoctorCard";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

const DoctorsList = ({ doctors = [] }) => {
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.down("sm"));
  const isSm = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Box sx={{display: "flex", justifyContent: "center" , width: "100%"}}>
      <Grid container spacing={isXs ? 2 : 3}>
        {doctors.length > 0 ? (
          doctors.map((doctor, index) => (
            <Grid sx={{display: "flex", justifyContent: "center" , width: "100%"}} item xs={12} sm={isSm ? 6 : 4} md={4} lg={3} key={index}>
              <DoctorCard
                name={doctor?.doctorDetails?.name}
                specialization={
                  doctor?.doctorDetails?.medicalSpecialities
                    ?.map((speciality) => speciality.name)
                    .join(", ") || doctor?.doctorDetails?.description
                }
                imageUrl={doctor?.doctorDetails?.profilePicture || ""}
                cardsVisible={true}
                index={index}
                experience={doctor?.doctorDetails?.additionalDetails?.workExperience}
                consultationFee={doctor?.doctorDetails?.additionalDetails?.consultationFee}
                doctorCode={doctor.doctorDetails.code}
                seoSlug={doctor.doctorDetails.seoSlug}
              />
            </Grid>
          ))
        ) : (
          <p>No doctors found.</p>
        )}
      </Grid>
    </Box>
  );
};

export default DoctorsList;
