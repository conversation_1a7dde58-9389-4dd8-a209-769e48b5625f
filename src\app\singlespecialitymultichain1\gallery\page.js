"use client";

import { Box, Typography } from "@mui/material";
import React, { useContext, useEffect, useState } from "react";
import useStyles from "../styles";
import homepageStyles from "../components/Homepage/styles";
import useGalleryStyles from "./styles";
import Dialog from "@mui/material/Dialog";
import {AppContext} from "@/app/AppContextLayout";
import {getThumborUrl} from "@/app/utils/getThumborUrl";

const Gallery = () => {
  const commonClasses = useStyles();
  const classes = useGalleryStyles();
  const homepageClasses = homepageStyles();
  const { websiteData } = useContext(AppContext);
  const { multiMedia = [] } = websiteData || {};
  const [photos, setPhotos] = useState([]);
  const [videos, setVideos] = useState([]);
  const [open, setOpen] = React.useState(false);
  const [imgUrl, setImgUrl] = React.useState(false);

  const handleClickOpen = (imageUrl) => {
    setImgUrl(imageUrl)
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (multiMedia.length > 0) {
      const photosArr = multiMedia.filter((media) => Boolean(media.image_url));
      const videosArr = multiMedia.filter((media) => Boolean(media.video_url));
      setPhotos(photosArr);
      setVideos(videosArr);
    }
  }, [multiMedia]);

  return (
    <div>
      {photos.length > 0 && (
        <Box>
          <Box style={{ position: "relative" }}>
            <Box className={classes.galleryBackgroundHeading}>Photos</Box>
            <Typography
              className={`${homepageClasses.sectionHeading} ${classes.galleryHeading}`}
              sx={{fontSize: "3rem"}}
            >
              Gallery
            </Typography>
          </Box>
          <Box className={`${classes.galleryLayout} ${classes.galleryGridBox}`}>
            {photos.map((photo, index) => {
              const { image_url: imageUrl = "" } = photo || {};
              return (
                  <Box
                      key={index}
                      onClick={() => handleClickOpen(imageUrl)} // Pass imageUrl as argument
                      style={{ cursor: "pointer" }}
                      className={classes.galleryImgBox}
                  >
                    <img
                        alt="default-img"
                        src={getThumborUrl(imageUrl, 0, 0)}
                        className={classes.galleryImg}
                    />
                  </Box>
              );
            })}
          </Box>
          <Dialog
              open={open}
              onClose={handleClose}
              aria-labelledby="alert-dialog-title"
              aria-describedby="alert-dialog-description"
              sx={{
                ".MuiDialog-paper": {
                  maxHeight: "calc(100vh - 64px)",
                  maxWidth: "calc(100vw - 64px)",
                  padding: 0,
                  borderRadius: "8px",
                  overflow: "hidden",
                  display: "flex", // Center content in the dialog
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "rgba(0, 0, 0, 0)", // Optional for better contrast
                },
              }}
          >
            <Box
                sx={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  overflow: "auto", // Allow scrolling if the image is still too large
                }}
            >
              <img
                  alt="slider1"
                  src={imgUrl}
                  style={{
                    maxHeight: "calc(100vh - 128px)", // Ensure it doesn't exceed dialog height
                    maxWidth: "calc(100vw - 128px)", // Ensure it doesn't exceed dialog width
                    width: "auto",
                    height: "auto",
                    objectFit: "contain",
                    objectPosition: "center",
                  }}
              />
            </Box>
          </Dialog>
        </Box>
      )}
      {videos.length > 0 && (
        <Box>
          <Box style={{ position: "relative" }}>
            <Box className={classes.galleryBackgroundHeading}>Videos</Box>
            <Typography
              className={`${classes.galleryHeading} ${homepageClasses.sectionHeading}`}
              sx={{fontSize: "3rem"}}
            >
              Videos
            </Typography>
          </Box>
          <Box
            className={`${classes.galleryLayout} ${classes.galleryVideosGridBox}`}
          >
            {videos.map((video, index) => {
              const { video_url: videoUrl = "" } = video || {};
              return (
                <Box key={index}>
                  <iframe
                    width="100%"
                    height="400"
                    src={`https://www.youtube.com/embed/${videoUrl}`}
                    title="YouTube video player"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  ></iframe>
                </Box>
              );
            })}
          </Box>
        </Box>
      )}
    </div>
  );
};

export default Gallery;
