'use client'
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import ReviewCard from "./ReviewCard";


const ReviewsSlider = ({testimonials}) => {
  return (
   <>
    <Swiper
      modules={[Pagination, Autoplay]}
      spaceBetween={20}
      slidesPerView={1}
      loop={true}
      pagination={{ dynamicBullets: true, clickable: true }}
      autoplay={{ delay: 3000, disableOnInteraction: false }}
      breakpoints={{
        320: {
          slidesPerView: 1,
          spaceBetween: 16,
        },
        600: {
          slidesPerView: 2,
          spaceBetween: 16,
        },
        960: {
          slidesPerView: 3,
          spaceBetween: 20,
        },
        1280: {
          slidesPerView: 4,
          spaceBetween: 20,
        },
      }}
      style={{
        paddingBottom: "40px",
      }}
    >
      {testimonials?.map((testimonial) => (
        <SwiperSlide key={testimonial.id}>
          <ReviewCard testimonial={testimonial} disableNavigation={false} />
        </SwiperSlide>
      ))}
    </Swiper>
      {/* <Box sx={{ display: "flex", mt: 1, justifyContent: "center" }}>
      <Button
      onClick={() => router.push("/reviews")}
        sx={{
          borderRadius: "100px",
          backgroundColor: "primary.main",
          transform: "scale(1)",
          color: "text.primary",
          textTransform: "capitalize",
          fontWeight: "normal",
          fontSize: "14px",
          padding: "10px 20px",
          transition: "transform 0.3s ease",
          "&:hover": {
            backgroundColor: "primary.main",
          },
        }}
      >
        View Testimonials
      </Button>
    </Box> */}
   </>
  );
};

export default ReviewsSlider;
