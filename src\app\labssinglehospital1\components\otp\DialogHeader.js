'use client';

import React from 'react';
import { Box, Typography, IconButton, DialogTitle } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const DialogHeader = ({ step, setStep, onClose }) => {
  const getTitle = () => {
    switch (step) {
      case 1:
        return 'Verify Your Phone Number';
      case 2:
        return 'Enter Verification Code';
      case 3:
        return 'Your Contact Details';
      case 4:
        return 'Booking Successful';
      default:
        return 'Verification';
    }
  };

  return (
    <DialogTitle
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: { xs: 2, sm: 3 },
        pb: { xs: 1, sm: 2 },
        color: 'text.black',
        borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {(step === 2 || step === 3) && (
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => setStep(step === 2 ? 1 : 2)}
            aria-label="back"
            sx={{ mr: 1, color: 'text.secondary' }}
          >
            <ArrowBackIcon />
          </IconButton>
        )}
        <Typography variant="h6" component="div" sx={{ fontWeight: 600, color: 'text.black' }}>
          {getTitle()}
        </Typography>
      </Box>
      <IconButton
        edge="end"
        color="inherit"
        onClick={onClose}
        aria-label="close"
        sx={{ color: 'text.secondary' }}
      >
        <CloseIcon />
      </IconButton>
    </DialogTitle>
  );
};

export default DialogHeader;
