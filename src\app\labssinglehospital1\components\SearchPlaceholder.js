'use client'
import { useState, useEffect } from 'react';
import { Typography } from '@mui/material';

const phrases = [
  'lab tests',
  'health packages',
  'diagnostics'
];

const SearchPlaceholder = () => {
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    let timeout;

    const animateText = () => {
      const currentPhrase = phrases[currentPhraseIndex];

      if (!isDeleting) {
        if (displayText.length < currentPhrase.length) {
          setDisplayText(currentPhrase.slice(0, displayText.length + 1));
          timeout = setTimeout(animateText, 100);
        } else {
          timeout = setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        if (displayText.length > 0) {
          setDisplayText(displayText.slice(0, -1));
          timeout = setTimeout(animateText, 50);
        } else {
          setIsDeleting(false);
          setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
        }
      }
    };

    timeout = setTimeout(animateText, 100);

    return () => clearTimeout(timeout);
  }, [displayText, currentPhraseIndex, isDeleting]);

  return (
    <Typography
      color="text.black"
      sx={{
        width: 'auto',
        minWidth: { xs: '140px', sm: '180px', md: '220px', lg: '250px' },
        maxWidth: { xs: '180px', sm: '250px', md: '300px', lg: '350px' },
        fontSize: { xs: '0.8rem', sm: '0.875rem', md: '1rem' },
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        '&::after': {
          content: '"|"',
          animation: 'blink 1s step-end infinite'
        },
        '@keyframes blink': {
          'from, to': { opacity: 1 },
          '50%': { opacity: 0 }
        }
      }}
    >
     Search for {displayText}
    </Typography>
  );
};

export default SearchPlaceholder;
