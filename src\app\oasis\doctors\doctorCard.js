"use client";

import { useTheme } from "@emotion/react";
import { Box, Typography } from "@mui/material";
import { alpha } from "@mui/material/styles";
import Image from "next/image";
import WorkHistoryOutlinedIcon from "@mui/icons-material/WorkHistoryOutlined";
import CurrencyRupeeIcon from "@mui/icons-material/CurrencyRupee";
import OutlinedButton from "../styledComponents/OutlinedButton";
import PrimaryButton from "../styledComponents/PrimaryButton";
import { useRouter } from "next/navigation";
import { getThumborUrl } from "../../utils/getThumborUrl";

const DoctorCard = ({ doctorDetails = {}, id = null }) => {
  const theme = useTheme();
  const router = useRouter();

  const {
    code = null,
    name = "",
    profilePicture = "",
    medicalSpecialities = [],
    additionalDetails = null,
    seoSlug = ""
  } = doctorDetails || {};

  const { workExperience = "", consultationFee = "" } = additionalDetails || {};

  const handleDoctorCardClick = () => {
    router.push(`/doctors/${seoSlug}`);
  };

  return (
    <Box
      id={id}
      sx={{
        boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
        borderRadius: "8px",
        cursor: "pointer",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        minHeight: "200px",
      }}
      onClick={handleDoctorCardClick}
    >
      <Box sx={{ padding: "20px 16px", display: "flex", gap: "16px" }}>
        <Box
          sx={{
            height: "64px",
            width: "64px",
            borderRadius: "50px",
            overflow: "hidden",
          }}
        >
          <Image
            alt="profilePic"
            src={
              profilePicture
                ? getThumborUrl(profilePicture, 64, 64)
                : "/doctor-profile-icon.png"
            }
            height={64}
            width={64}
            style={{
              objectFit: "cover",
              objectPosition: "center",
              border: "1px solid #dfe2f2",
              borderRadius: "100%",
            }}
          />
        </Box>
        <Box sx={{ flex: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            {name || ""}
          </Typography>
          <Typography
            variant="subtitle2"
            sx={{
              color: "#7c7c7c",
              overflow: "hidden",
              textOverflow: "ellipsis",
              display: "-webkit-box",
              WebkitLineClamp: "2",
              WebkitBoxOrient: "vertical",
            }}
          >
            {medicalSpecialities?.length > 0 &&
              `Specialised in ${medicalSpecialities.map((speciality, index) => {
                const { name = "" } = speciality || {};
                return `${name} ${
                  index < medicalSpecialities.length - 1 ? "|" : ""
                }`;
              })}`}
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              gap: "16px",
              mt: 2,
            }}
          >
            {Boolean(workExperience) && (
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <WorkHistoryOutlinedIcon />
                <Typography variant="subtitle2">
                  {workExperience} years
                </Typography>
              </Box>
            )}
            {Boolean(consultationFee) && (
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <CurrencyRupeeIcon />
                <Typography variant="subtitle2">{consultationFee}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      <Box sx={{ display: "flex" }}>
        <OutlinedButton
          id="doctorViewProfile"
          sx={{ width: "50%", borderRadius: "0", textTransform: "none" }}
        >
          View full profile
        </OutlinedButton>
        <PrimaryButton
          id="doctorBookAppointment"
          sx={{ width: "50%", borderRadius: "0", textTransform: "none" }}
        >
          Book appointment
        </PrimaryButton>
      </Box>
    </Box>
  );
};

export default DoctorCard;
