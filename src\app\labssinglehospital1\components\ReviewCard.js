"use client";
import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ing,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
} from "@mui/material";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import CloseIcon from "@mui/icons-material/Close";
import { useRouter } from "next/navigation";

const ReviewCard = ({ testimonial, disableNavigation = false }) => {
  const router = useRouter();
  const [expanded, setExpanded] = useState(false);
  const [isTextTruncated, setIsTextTruncated] = useState(false);
  const textRef = useRef(null);

  // Check if text is truncated and needs a "Read More" button
  useEffect(() => {
    if (textRef.current) {
      const element = textRef.current;
      // Only consider it truncated if it's significantly taller than visible area
      // This helps avoid showing Read More for just 1-2 lines
      const isTruncated = element.scrollHeight > (element.clientHeight + 10); // Adding 10px buffer
      setIsTextTruncated(isTruncated);
    }
  }, [testimonial.text]);

  // Add escape key listener to close dialog
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && expanded) {
        setExpanded(false);
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [expanded]);

  const handleClick = (e) => {
    // If the click was on the "Read More" button, don't navigate
    if (e.target.closest(".read-more-button")) {
      e.stopPropagation();
      return;
    }

    // If text is truncated, show the dialog instead of navigating
    if (isTextTruncated) {
      e.stopPropagation();
      setExpanded(true);
      return;
    }

    // Only navigate if not disabled and not showing dialog
    if (!disableNavigation) {
      router.push("/reviews");
    }
  };

  const handleReadMoreClick = (e) => {
    e.stopPropagation();
    setExpanded(true);
  };

  const handleClose = (e) => {
    if (e) e.stopPropagation(); // Stop event propagation
    setExpanded(false);
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        p: { xs: 2, md: 3 },
        height: "100%",
        minHeight: { xs: "auto", md: "220px" },
        maxHeight: { xs: "none", md: "280px" },
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        backgroundColor: "white",
        borderRadius: "12px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
        position: "relative",
        overflow: "hidden",
        transition: "transform 0.2s ease, box-shadow 0.2s ease",
        cursor: "pointer",
        "&:hover": {
          transform: "translateY(-2px)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        },
      }}
    >
      <Rating value={testimonial.rating} readOnly size="small" sx={{ mb: 2 }} />
      <FormatQuoteIcon
        sx={{
          fontSize: { xs: "2.5rem", md: "3rem" },
          color: "#e0e0e0",
          position: "absolute",
          top: { xs: "1rem", md: "1.5rem" },
          right: { xs: "0.5rem", md: "1rem" },
          zIndex: 0,
          opacity: 0.7,
        }}
      />
      <FormatQuoteIcon
        sx={{
          fontSize: { xs: "2.5rem", md: "3rem" },
          color: "#e0e0e0",
          position: "absolute",
          top: { xs: "1rem", md: "1.5rem" },
          left: { xs: "0.5rem", md: "1rem" },
          rotate: "180deg",
          zIndex: 0,
          opacity: 0.7,
        }}
      />
      <Box sx={{ position: "relative", width: "100%", mb: 3 }}>
        <Typography
          ref={textRef}
          variant="body2"
          sx={{
            lineHeight: 1.6,
            color: "text.black",
            position: "relative",
            zIndex: 1,
            textAlign: "center",
            fontSize: { xs: "0.875rem", md: "0.9rem" },
            overflow: "hidden",
            display: "-webkit-box",
            WebkitLineClamp: 4,
            WebkitBoxOrient: "vertical",
          }}
        >
          {testimonial.text}
        </Typography>

        {isTextTruncated && (
          <Button
            className="read-more-button"
            onClick={handleReadMoreClick}
            sx={{
              mt: 1,
              fontSize: "1rem",
              textTransform: "none",
              color: "primary.main",
              width: "100%",
              p: 0,
              fontWeight: "medium",
              "&:hover": {
                backgroundColor: "transparent",
                textDecoration: "underline",
              },
            }}
          >
            <Typography
              sx={{
                fontSize: "1rem",
                fontWeight: "medium",
                textAlign: "center",
              }}
            >
              Read More
            </Typography>
          </Button>
        )}
      </Box>

      {/* Full review dialog */}
      <Dialog
        open={expanded}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        onClick={(e) => e.stopPropagation()} // Prevent click from bubbling up
        // Use onClose instead of BackdropProps for better compatibility
        disableEscapeKeyDown={false}
        disableRestoreFocus={false}
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: "12px",
            p: { xs: 1, sm: 2 },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            pb: 1,
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: "bold", color: "text.black" }}>
            Review by {testimonial.name}
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleClose}
            aria-label="close"
            size="small"
            sx={{
              bgcolor: 'rgba(0, 0, 0, 0.05)',
              color: 'text.black',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.1)',
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <Rating value={testimonial.rating} readOnly size="small" />
            <Typography variant="body2" sx={{ ml: 1, color: "text.secondary" }}>
              {testimonial.rating}/5
            </Typography>
          </Box>
          <Typography variant="body1" sx={{ lineHeight: 1.8,color: "text.black" }}>
            {testimonial.text}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleClose}
            sx={{
              textTransform: "none",
              fontWeight: "medium",
              borderRadius: "8px",
              px: 3
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <Box
        sx={{
          mt: "auto",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Typography
          variant="subtitle2"
          sx={{
            fontWeight: 600,
            color: "text.black",
            fontSize: { xs: "0.8rem", md: "0.875rem" },
          }}
        >
          {testimonial.name}
        </Typography>
      </Box>
    </Box>
  );
};

export default ReviewCard;
