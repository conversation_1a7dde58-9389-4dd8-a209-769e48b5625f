import { Box, Container, Typography } from "@mui/material";
import Link from "next/link";
import ReviewsSlider from "../../ReviewsSlider";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import {
  getHomeComponentsData,
  getHomeSectionHeadings,
} from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";
const getReviewsHeadings = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeSectionHeadings(
      { domainName: domainName },
      HOME_SECTION_HEADING_TYPE.TESTIMONIAL
    );
    if (data.code === 200) {
      return data?.result || [];
    } else return [];
  } catch (error) {
    console.error("Error fetching reviews data:", error);
    return [];
  }
};

export default async function TestimonialSlider() {
  const reviews = await getReviewsHeadings();

  const heading = reviews[0]?.heading;
  let testimonials = [];

  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData(
      { domainName },
      HOME_WIDGET_TYPE.TESTIMONIAL
    );
    if (data.code === 200) {
      testimonials = data?.result?.testimonials || [];
    }
  } catch (error) {
    console.error("Error fetching testimonials:", error);
  }
  if (testimonials.length === 0) {
    return null;
  }
  return (
    <>
      <Box
        sx={{
          width: "100%",
          backgroundColor: "#f8f8f8",
          py: "12px",
          position: "relative",
        }}
      >
        <Container maxWidth="xl" sx={{ p: 3 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
              position: "relative",
            }}
          >
            <Typography variant="h2" component="h2" fontWeight="bold" color="#333333" sx={{ fontSize: { xs: '1.5rem', md: '1.8rem' } }}>
              {heading || "Our Testimonials"}
            </Typography>
            <Link href="/reviews" passHref aria-label="View all reviews">
              <Typography
                component="span"
                sx={{
                  textWrap: "nowrap",
                  color:'primary.main',
                  cursor: "pointer",
                  fontWeight: 500,
                  fontSize: "0.9rem",
                }}
              >
                View All
              </Typography>
            </Link>
          </Box>
          <ReviewsSlider testimonials={testimonials} />
        </Container>
      </Box>
    </>
  );
}
