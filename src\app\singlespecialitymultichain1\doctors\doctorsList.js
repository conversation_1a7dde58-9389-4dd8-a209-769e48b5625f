import { Box } from "@mui/material";
import Doctor<PERSON><PERSON> from "./doctorCard";

const DoctorsList = ({ doctors = [], selectedLocation = null }) => {
  return (
    <Box
      sx={{
        display: "grid",
        gridTemplateColumns: { xs: "1fr", md: "1fr 1fr", lg: "repeat(3, 1fr)" },
        columnGap: "32px",
        rowGap: "48px",
      }}
    >
      {doctors.map((doctor, index) => {
        const { doctorDetails = {} } = doctor || {};
        const { code = null } = doctorDetails || {};
        return <DoctorCard id={`doctor${index}`} key={code} doctorDetails={doctorDetails} selectedLocation={selectedLocation} />;
      })}
    </Box>
  );
};

export default DoctorsList;
