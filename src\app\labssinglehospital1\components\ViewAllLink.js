'use client';

import { Typography, useTheme } from "@mui/material";
import Link from "next/link";

const ViewAllLink = ({ href, ariaLabel = 'View all items' }) => {
  const theme = useTheme();

  return (
    <Link href={href} passHref aria-label={ariaLabel}>
      <Typography
        component="span"
        sx={{
          textWrap: "nowrap",
          color: theme.palette.primary.main,
          cursor: "pointer",
          fontWeight: 500,
          fontSize: "0.9rem",
        }}
      >
        View All
      </Typography>
    </Link>
  );
};

export default ViewAllLink;
