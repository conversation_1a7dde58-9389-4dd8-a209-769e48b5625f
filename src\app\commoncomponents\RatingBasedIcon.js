import ThumbUpOffAltIcon from "@mui/icons-material/ThumbUpOffAlt";
import ThumbDownOffAltIcon from "@mui/icons-material/ThumbDownOffAlt";
import { Rating, Typography } from "@mui/material";

const RatingBasedIcon = ({ ratingType = 2, rating }) => {
  switch (ratingType) {
    case 1:
      return <Typography>%</Typography>;
    case 2:
      return (
        <Rating name="simple-controlled" value={rating} precision={0.5} readOnly size="small" />
      );
    case 3:
      return (
        <Typography
          variant="subtitle2"
          sx={{
            fontSize: "12px",
            display: "flex",
            alignItems: "center",
            gap: "4px",
          }}
        >
          <ThumbUpOffAltIcon fontSize="small" />I recommend the doctor
        </Typography>
      );
    case 4:
      return (
        <Typography
          variant="subtitle2"
          sx={{
            fontSize: "12px",
            display: "flex",
            alignItems: "start",
            gap: "8px",
          }}
        >
          <ThumbDownOffAltIcon fontSize="small" />I do not recommend the doctor
        </Typography>
      );
    default:
      return <></>;
  }
};

export default RatingBasedIcon;
