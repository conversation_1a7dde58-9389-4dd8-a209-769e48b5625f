"use client";

import { DAYS_OF_WEEK, convertTo12HourFormat } from "@/constants";
import { Box, Typography } from "@mui/material";
import { useState } from "react";

const OPDTimings = ({ practiceTimings = {} }) => {
  const [viewAllTimings, setViewAllTimings] = useState(false);

  return (
    <Box sx={{ display: { xs: "block", md: "none" } }}>
      <Typography variant="subtitle1" sx={{ fontSize: "16px", mb: 1, mt: 1 }}>
        OPD Timings
      </Typography>
      <Box>
        {practiceTimings.availability
          .slice(0, viewAllTimings ? practiceTimings.availability.length : 2)
          .map((item) => {
            const {
              slotStartTime = "",
              slotEndTime = "",
              entries = [],
            } = item || {};
            const daysArr = entries.map((entry) => {
              const { day = null } = entry || {};
              return DAYS_OF_WEEK[day];
            });
            const str = daysArr.join(", ");
            return (
              <Typography variant="subtitle1" sx={{ fontSize: "14px" }}>
                {`${str} : ${convertTo12HourFormat(
                  slotStartTime
                )} - ${convertTo12HourFormat(slotEndTime)}`}
              </Typography>
            );
          })}
        {practiceTimings.availability.length > 2 &&
          (!viewAllTimings ? (
            <Typography
              variant="subtitle2"
              sx={{
                fontSize: "14px",
                cursor: "pointer",
                textDecoration: "underline",
              }}
              onClick={() => setViewAllTimings(true)}
            >
              View All Timings
            </Typography>
          ) : (
            <Typography
              variant="subtitle2"
              sx={{
                fontSize: "14px",
                cursor: "pointer",
                textDecoration: "underline",
              }}
              onClick={() => setViewAllTimings(false)}
            >
              View Less
            </Typography>
          ))}
      </Box>
    </Box>
  );
};

export default OPDTimings;
