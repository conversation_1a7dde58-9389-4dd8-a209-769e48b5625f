"use client";

import {Box} from "@mui/material";
import {useContext,} from "react";
import {AppContext} from "@/app/AppContextLayout";
import WebHighlightsWidgetChainTemp2
    from "@/app/hospitalchaintemplate2apollo/components/highlights/webHighlightsWidgetChainTemp2";
import MWebHighlightsWidgetChainTemp2
    from "@/app/hospitalchaintemplate2apollo/components/highlights/MWebHighlightsWidgetChainTemp2";
import SectionLayoutChainTemp2 from "@/app/hospitalchaintemplate2apollo/styledComponents/SectionLayoutChainTemp2";

const HighlightsWidgetChainTemp2 = () => {
    const {websiteData = {},} = useContext(AppContext);
    const {highlights = []} = websiteData || {};

    return (
        <>
            {highlights.length > 0 && (
                <Box
                    sx={{
                        position: "absolute",
                        bottom: "-40px",
                        left: "50%",
                        transform: "translateX(-50%)",
                        width: "max-content",
                        zIndex: 1,
                        display: {xs: "none", md: "block"},
                    }}
                >
                    <WebHighlightsWidgetChainTemp2 highlights={highlights}/>
                </Box>
            )}
            <Box
                sx={{
                    position: "relative",
                    display: {xs: "block", md: "none"},
                    bgcolor: "#f6f6f6",
                }}
            >
                {highlights.length > 0 && (
                    <SectionLayoutChainTemp2>
                        <MWebHighlightsWidgetChainTemp2 highlights={highlights}/>
                    </SectionLayoutChainTemp2>
                )}
            </Box>
        </>
    );
};

export default HighlightsWidgetChainTemp2;
