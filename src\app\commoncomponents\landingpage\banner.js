"use client";

import { getThumborUrl } from "@/app/utils/getThumborUrl";
import {
  Box,
  Button,
  CircularProgress,
  // Container,
  Divider,
  FormControl,
  FormHelperText,
  InputBase,
  MenuItem,
  Rating,
  Select,
  Typography,
  useTheme,
} from "@mui/material";
import { alpha } from "@mui/material/styles";
import React, { useState, useContext } from "react";
import Image from "next/image";
import { COUNTRIES, countryToFlag } from "@/constants";
import { isEmptyObject } from "@/app/utils/isEmptyObject";
import { useRouter } from "next/navigation";
import { AppContext } from "@/app/AppContextLayout";
import axios from "axios";
import {
  API_ENDPOINT_GENERATE,
  API_SECTION_API,
  API_SECTION_ENTERPRISE,
  API_SECTION_LEADS,
  API_SECTION_PUBLIC,
  API_VERSION,
  HARBOR_API_DOCFYN_DOMAIN,
} from "@/constants";
// import ModelForm from "./dialogForm";

const Banner = ({
  value = [],
  enterpriseCode = null,
  campaignName = null,
  productCode = null,
  requestMappings = [],
}) => {
  const {
    title = "",
    subTitle = "",
    googleRating = "",
    heroImageUrl = "",
  } = value[0] || {};

  const theme = useTheme();
  const router = useRouter();
  const { mobileView } = useContext(AppContext);

  // Form state
  const initialInput = { dialCode: "+91" };
  const [input, setInput] = useState(initialInput);
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e) => {
    if (errors[e.target.name]) {
      const copyOfErrors = { ...errors };
      delete copyOfErrors[e.target.name];
      setErrors(copyOfErrors);
    }
    setInput((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleValidation = () => {
    const error = {};
    if (!input["firstName"] || input["firstName"].length === 0)
      error["firstName"] = "Cannot be empty";
    if (!input["phone"] || input["phone"].length === 0)
      error["phone"] = "Cannot be empty";
    if (input["phone"]?.length !== 10)
      error["phone"] = "Enter a 10 digit phone number";
    if (!isEmptyObject(error)) {
      setErrors(error);
      return false;
    }
    return true;
  };

  const extractCodeFromSlug = (slug) => {
    const segments = slug.split("-");
    return segments[segments.length - 1];
  };

  const handleLeadGeneration = async () => {
    if (!enterpriseCode || isLoading) return;
    if (!handleValidation()) return;
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}${productCode ? `?productCode=${extractCodeFromSlug(productCode)}` : ""}`;
    const reqBody = { ...input, leadSource: 6, lastName: "", type: 2, requestMappings };
    if (campaignName) reqBody["campaignName"] = campaignName;
    try {
      const response = await axios.post(url, reqBody, {
        headers: {
          "Content-Type": "application/json",
          source: mobileView ? "mweb" : "website",
        },
      });
      const { status = null } = response || {};
      if (status >= 200 && status < 300) {
        // Success, redirect to thank you page
        router.push("/thank-you");
        setInput({ ...initialInput });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: (theme) => `
            radial-gradient(circle at 20% 30%, ${alpha(theme.palette.primary.main, 0.08)} 0%, transparent 35%),
            radial-gradient(circle at 80% 20%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 40%),
            radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.08)} 0%, transparent 35%),
            radial-gradient(circle at 70% 60%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 40%),
            linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.03)} 0%, transparent 100%)
          `,
          zIndex: 0,
        },
      }}
    >
      <Box
        sx={{
          maxWidth: "1400px",
          mx: "auto",
          // py: { xs: 0, md: 0 },
          overflow: "hidden",
          position: "relative",
          zIndex: 1,
          // backgroundColor: "rgba(255, 255, 255, 0.85)",
          backdropFilter: "blur(8px)",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            alignItems: "center",
            justifyContent: "space-between",
            gap: { xs: 1, md: 0 }, // Reduced gap on mobile
            minHeight: { xs: "auto", md: "calc(100vh - 200px)" },
            position: "relative",
          }}
        >
          {/* Content Section */}
          <Box
            sx={{
              padding: { xs: "8px 16px 0 16px", md: "18px 32px 0 0px" },
              width: { xs: "100%", md: "60%" },
              order: { xs: 2, md: 1 },
              display: "flex",
              flexDirection: "column",
              gap: 1.5, // Reduced gap between elements
              alignItems: "center",
              zIndex: 2,
            }}
          >
            {/* Title */}
            <Typography
              variant="h1"
              sx={{
                color: "primary.main",
                fontSize: { xs: "2.5rem", sm: "3.2rem", md: "3.45rem" },
                fontWeight: "500",
                textAlign: "center",
                lineHeight: 1,
                maxWidth: "600px",
                mb: 0.5,
              }}
            >
              {title || ""}
            </Typography>

            {/* Subtitle */}
            <Typography
              variant="h2"
              sx={{
                color: "#444",
                fontSize: { xs: "1rem", md: "1.2rem" },
                fontWeight: "400",
                textAlign: "center",
                maxWidth: "550px",
                mb: 0.5,
                opacity: 0.9,
              }}
            >
              {subTitle || ""}
            </Typography>

            {/* Embedded Form */}
            <Box
              sx={{
                width: "100%",
                maxWidth: "350px",
                // height: "300px",
                mt: 1,
                // mb: 1.5,
                pb: 2,
                borderRadius: "12px",
                // backgroundColor: "rgb(228, 228, 228)",
                backgroundColor: "white",
                boxShadow: "0 4px 16px rgba(0, 0, 0, 0.27)",
                border: "1px solid rgba(0,0,0,0.05)",
                overflow: "hidden",
                alignSelf: "center",
              }}
            >
              <Box
                sx={{
                  // mb: 3,
                  width: "100%",
                  backgroundColor: "rgba(239, 239, 239, 0.53)",

                  // background: (theme) => `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                  // background: (theme) => theme.palette.primary.main,
                  // boxShadow: (theme) =>
                  //   `0 4px 10px ${alpha(theme.palette.primary.main, 0.3)}`,
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    fontSize: "1.1rem",
                    color: "primary.main",
                    textAlign: "center",
                    py: 1,
                  }}
                >
                  Book an Appointment
                </Typography>
              </Box>
              <Divider />

              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                  px: 2,
                  pt: 2,
                  // pb: 1,
                  // backgroundColor: "rgba(0,0,0,0.03)",
                  borderRadius: "0 0 12px 12px",
                }}
              >
                {/* Name Field */}
                <FormControl fullWidth>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 0.5,
                      fontWeight: 500,
                      color: "#333",
                      fontSize: "0.875rem",
                    }}
                  >
                    Your Name
                  </Typography>
                  <InputBase
                    name="firstName"
                    id="leadName"
                    sx={{
                      borderRadius: "8px",
                      height: { xs: "40px", sm: "42px" },
                      fontSize: { xs: "0.9rem", sm: "0.95rem" },
                      width: "100%",
                      color: "#333",
                      padding: "8px 12px",
                      transition: "all 0.2s ease",
                      backgroundColor: "rgb(255, 255, 255)",
                      border: errors["firstName"]
                        ? "1px solid #d32f2f"
                        : "1px solid rgba(0, 0, 0, 0.38)",
                      "& .MuiInputBase-input": {
                        height: "100%",
                        padding: "0 14px",
                        display: "flex",
                        alignItems: "center",
                      },
                      "&:hover": {
                        backgroundColor: "rgb(255, 255, 255)",
                        border: errors["firstName"]
                          ? "1px solid #d32f2f"
                          : "1px solid rgba(0,0,0,0.12)",
                      },
                      "&:focus-within": {
                        backgroundColor: "#fff",
                        border: errors["firstName"]
                          ? "1px solid #d32f2f"
                          : `1px solid ${theme.palette.primary.main}`,
                        boxShadow: errors["firstName"]
                          ? "none"
                          : `0 0 0 2px ${alpha(theme.palette.primary.main, 0.1)}`,
                      },
                    }}
                    value={input["firstName"] || ""}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    inputProps={{
                      "aria-label": "enter your name",
                      style: { height: "100%", padding: "0" },
                    }}
                  />
                  {Boolean(errors["firstName"]) && (
                    <FormHelperText
                      error={true}
                      sx={{
                        ml: 0.5,
                        mt: 0.5,
                        fontSize: "0.7rem",
                      }}
                    >
                      {errors["firstName"]}
                    </FormHelperText>
                  )}
                </FormControl>

                {/* Phone Field */}
                <FormControl fullWidth>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 0.5,
                      fontWeight: 500,
                      color: "#333",
                      fontSize: "0.875rem",
                    }}
                  >
                    Phone Number
                  </Typography>
                  <Box
                    sx={{ display: "flex", gap: "8px", alignItems: "center" }}
                  >
                    <Select
                      id="quickEnquiryCountryCode"
                      value={input["dialCode"]}
                      onChange={(e) =>
                        setInput((prev) => ({
                          ...prev,
                          dialCode: e.target.value,
                        }))
                      }
                      sx={{
                        height: { xs: "40px", sm: "42px" },
                        minWidth: "80px",
                        color: "#333",
                        backgroundColor: "rgb(255, 255, 255)",
                        borderRadius: "8px",
                        border: "1px solid rgba(0,0,0,0.08)",
                        "& .MuiSelect-select": {
                          paddingY: "10px",
                          paddingX: "10px",
                          fontSize: { xs: "0.9rem", sm: "0.95rem" },
                          display: "flex",
                          alignItems: "center",
                        },
                        "&:hover": {
                          backgroundColor: "rgb(255, 255, 255)",
                          border: "1px solid rgba(0,0,0,0.12)",
                        },
                        "&.Mui-focused": {
                          backgroundColor: "#fff",
                          border: `1px solid ${theme.palette.primary.main}`,
                          boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.1)}`,
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            maxHeight: "300px",
                            borderRadius: "8px",
                            mt: 0.5,
                            boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
                          },
                        },
                      }}
                    >
                      {COUNTRIES.map((country, index) => {
                        const {
                          dial_code: dialCode = "",
                          code = "IN",
                          value = "",
                        } = country || {};
                        return (
                          <MenuItem
                            key={index}
                            value={dialCode}
                            sx={{
                              color: "#333",
                              py: 0.75,
                              fontSize: "0.9rem",
                              "&:hover": {
                                backgroundColor: "rgba(0,0,0,0.04)",
                              },
                            }}
                          >
                            {`${countryToFlag(code)} ${value}`}
                          </MenuItem>
                        );
                      })}
                    </Select>
                    <InputBase
                      name="phone"
                      id="leadPhone"
                      sx={{
                        borderRadius: "8px",
                        height: { xs: "40px", sm: "42px" },
                        fontSize: { xs: "0.9rem", sm: "0.95rem" },
                        width: "100%",
                        color: "#333",
                        padding: "8px 12px",
                        transition: "all 0.2s ease",
                        backgroundColor: "rgb(255, 255, 255)",
                        border: errors["phone"]
                          ? "1px solid #d32f2f"
                          : "1px solid rgba(0,0,0,0.38)",
                        "& .MuiInputBase-input": {
                          height: "100%",
                          padding: "0 14px",
                          display: "flex",
                          alignItems: "center",
                        },
                        "&:hover": {
                          backgroundColor: "rgb(255, 255, 255)",
                          border: errors["phone"]
                            ? "1px solid #d32f2f"
                            : "1px solid rgba(0,0,0,0.12)",
                        },
                        "&:focus-within": {
                          backgroundColor: "#fff",
                          border: errors["phone"]
                            ? "1px solid #d32f2f"
                            : `1px solid ${theme.palette.primary.main}`,
                          boxShadow: errors["phone"]
                            ? "none"
                            : `0 0 0 2px ${alpha(theme.palette.primary.main, 0.1)}`,
                        },
                      }}
                      type="tel"
                      value={input["phone"] || ""}
                      onChange={handleInputChange}
                      placeholder="Enter your phone number"
                      inputProps={{
                        "aria-label": "enter your phone",
                        style: { height: "100%", padding: "0" },
                      }}
                    />
                  </Box>
                  {Boolean(errors["phone"]) && (
                    <FormHelperText
                      error={true}
                      sx={{
                        ml: 0.5,
                        mt: 0.5,
                        fontSize: "0.7rem",
                      }}
                    >
                      {errors["phone"]}
                    </FormHelperText>
                  )}
                </FormControl>

                {/* Submit Button */}
                <Button
                  id="leadGenSubmit"
                  variant="contained"
                  color="primary"
                  sx={{
                    textTransform: "none",
                    padding: "8px 0",
                    fontSize: { xs: "0.9rem", sm: "0.95rem", md: "1rem" },
                    fontWeight: 500,
                    width: "100%",
                    borderRadius: "8px",
                    // background: (theme) => `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    background: (theme) => theme.palette.primary.main,
                    boxShadow: (theme) =>
                      `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                    mt: 1,
                    transition: "all 0.3s ease",
                    "&:hover": {
                      boxShadow: (theme) =>
                        `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
                      transform: "translateY(-2px)",
                      // background: (theme) => `linear-gradient(135deg, ${theme.palette.primary.main} 10%, ${theme.palette.primary.dark} 90%)`,
                    },
                  }}
                  onClick={handleLeadGeneration}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <CircularProgress size={20} sx={{ color: "white" }} />
                  ) : (
                    "Submit"
                  )}
                </Button>
              </Box>
            </Box>
            {/* Google Rating */}
            {googleRating && (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  py: 1,
                  px: 1.5,
                  borderRadius: "50px",
                  background: (theme) =>
                    `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                  alignSelf: "center",
                  boxShadow: (theme) =>
                    `0 8px 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                  transition: "all 0.3s ease",
                  position: "relative",
                  marginY: 2,
                  "&:hover": {
                    transform: "translateY(-2px)",
                    boxShadow: (theme) =>
                      `0 10px 25px ${alpha(theme.palette.primary.main, 0.4)}`,
                  },
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "rgb(255, 255, 255)",
                    borderRadius: "50%",
                    p: 0.8,
                    mr: 1.5,
                  }}
                >
                  <Image
                    alt="Google"
                    src="/google.png"
                    height={20}
                    width={20}
                  />
                </Box>

                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography
                    sx={{
                      fontWeight: "700",
                      fontSize: "1.3rem",
                      color: "white",
                      mr: 1,
                      lineHeight: 1,
                      textShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    }}
                  >
                    {googleRating}
                  </Typography>

                  <Rating
                    name="google-rating"
                    value={parseFloat(googleRating) || 0}
                    precision={0.5}
                    readOnly
                    size="medium"
                    sx={{
                      color: "#FFCA28",
                      "& .MuiRating-iconFilled": {
                        color: "#FFCA28",
                        filter: "drop-shadow(0 2px 3px rgba(0,0,0,0.2))",
                      },
                      "& .MuiRating-iconEmpty": {
                        color: "rgba(255, 255, 255, 0.3)",
                      },
                    }}
                  />
                </Box>
              </Box>
            )}
          </Box>

          {/* Image Section with Square Aspect Ratio */}
          <Box
            sx={{
              width: { xs: "100%", md: "56%" },
              order: { xs: 1, md: 2 },
              position: "relative",
              overflow: "hidden", // Changed to hidden to prevent overflow issues
              display: { xs: "block", md: "block" },
              aspectRatio: "1/1", // Simple square aspect ratio
              height: "auto",
            }}
          >
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  padding: { xs: 2, md: 0 }, // Add padding on mobile
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image
                  alt="Banner"
                  src={
                    heroImageUrl
                      ? getThumborUrl(heroImageUrl)
                      : "/placeholder-image.jpg"
                  }
                  fill
                  priority
                  sizes="(max-width: 768px) 100vw, 50vw"
                  style={{
                    objectFit: "contain", // Changed from cover to contain
                    objectPosition: "center",
                  }}
                />
              </Box>
          </Box>
        </Box>
        {/* <ModelForm enterpriseCode={enterpriseCode} campaignName={campaignName} productCode={productCode}/> */}
      </Box>
    </Box>
  );
};

export default Banner;
