"use client";

import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { useContext, useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { NextArrow, PrevArrow } from "@/app/aspire/components/photosSection";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { YouTubeEmbed } from '@next/third-parties/google'
import {AppContext} from "@/app/AppContextLayout";

const SliderInfo = ({doctorMediaLinks = []}) => {
  const router = useRouter();
  const [videoItems, setVideoItems] = useState([]);
  const [articles, setArticles] = useState([]);
  const [blogs, setBlogs] = useState([]);
  const { desktopView, websiteData } = useContext(AppContext);
  const { logo_url: logoUrl = "" } = websiteData || {};

  const settings = {
    dots: true,
    infinite: true,
    adaptiveHeight: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  useEffect(() => {
    if (doctorMediaLinks?.length > 0) {
      const videos = doctorMediaLinks.filter((media) => media.type === 5);
      const articles = doctorMediaLinks.filter((media) => media.type === 6);
      const blogs = doctorMediaLinks.filter((media) => media.type === 7);
      setVideoItems(videos);
      setArticles(articles);
      setBlogs(blogs);
    }
  }, [doctorMediaLinks]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: "64px",
        marginTop: "64px",
      }}
    >
      {articles.length > 0 && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "24px",
            fontFamily: "inherit",
            color: "inherit",
          }}
        >
          <Typography variant="h5">
            <Box
              sx={{
                display: "inline-block",
                "&::after": {
                  content: "''",
                  width: "50%",
                  borderBottom: `3px solid`,
                  borderColor: "primary.main",
                  display: "block",
                  marginBottom: "-1px",
                  left: "50%",
                  right: "50%",
                },
              }}
            >
              Articles
            </Box>
          </Typography>
          {desktopView && articles.length < 4 ? (
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  lg: "repeat(3, 1fr)",
                },
                gap: "32px",
              }}
            >
              {articles.map((item) => {
                const { mediaTitle = "", mediaLink = "" } = item || {};
                return (
                  <Box
                    sx={{
                      borderRadius: "16px",
                      border: "2px solid #f0f0f5",
                      overflow: "hidden",
                      cursor: "pointer",
                    }}
                    onClick={() => router.push(mediaLink)}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        height: "250px",
                        alignItems: "center",
                        justifyContent: "center",
                        // backgroundImage: "url(/pattern-bg.png)",
                        backgroundRepeat: "repeat",
                        backgroundPosition: "top left",
                        backgroundColor: "#f6f6f6",
                      }}
                    >
                      <img
                        alt="article"
                        src={getThumborUrl(logoUrl)}
                        style={{
                          height: "120px",
                          width: "120px",
                          objectFit: "contain",
                        }}
                      />
                    </Box>
                    <Box sx={{ padding: "16px" }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "-webkit-box",
                          WebkitLineClamp: "2",
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {mediaTitle}
                      </Typography>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          ) : (
            <Slider
              {...settings}
              infinite={articles.length > 3}
              arrows={articles.length > 3}
            >
              {articles.map((item) => {
                const { mediaTitle = "", mediaLink = "" } = item || {};
                return (
                  <Box sx={{ padding: { xs: "16px 8px", md: "16px" } }}>
                    <Box
                      sx={{
                        borderRadius: "16px",
                        border: "2px solid #f0f0f5",
                        overflow: "hidden",
                        cursor: "pointer",
                      }}
                      onClick={() => router.push(mediaLink)}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          height: "250px",
                          alignItems: "center",
                          justifyContent: "center",
                          // backgroundImage: "url(/pattern-bg.png)",
                          backgroundRepeat: "repeat",
                          backgroundPosition: "top left",
                          backgroundColor: "#f6f6f6",
                        }}
                      >
                        <img
                          alt="article"
                          src={getThumborUrl(logoUrl)}
                          style={{
                            height: "120px",
                            width: "120px",
                            objectFit: "contain",
                          }}
                        />
                      </Box>
                      <Box sx={{ padding: "16px" }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: "2",
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          {mediaTitle}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Slider>
          )}
        </Box>
      )}
      {blogs.length > 0 && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "24px",
            fontFamily: "inherit",
            color: "inherit",
          }}
        >
          <Typography variant="h5">
            <Box
              sx={{
                display: "inline-block",
                "&::after": {
                  content: "''",
                  width: "50%",
                  borderBottom: `3px solid`,
                  borderColor: "primary.main",
                  display: "block",
                  marginBottom: "-1px",
                  left: "50%",
                  right: "50%",
                },
              }}
            >
              Blogs
            </Box>
          </Typography>
          {desktopView && blogs.length < 4 ? (
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  lg: "repeat(3, 1fr)",
                },
                gap: "32px",
              }}
            >
              {blogs.map((item) => {
                const { mediaTitle = "", mediaLink = "" } = item || {};
                return (
                  <Box
                    sx={{
                      borderRadius: "16px",
                      border: "2px solid #f0f0f5",
                      overflow: "hidden",
                      cursor: "pointer",
                    }}
                    onClick={() => router.push(mediaLink)}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        height: "250px",
                        alignItems: "center",
                        justifyContent: "center",
                        // backgroundImage: "url(/pattern-bg.png)",
                        backgroundRepeat: "repeat",
                        backgroundPosition: "top left",
                        backgroundColor: "#f6f6f6",
                      }}
                    >
                      <img
                        alt="article"
                        src={getThumborUrl(logoUrl)}
                        style={{
                          height: "120px",
                          width: "120px",
                          objectFit: "contain",
                        }}
                      />
                    </Box>
                    <Box sx={{ padding: "16px" }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "-webkit-box",
                          WebkitLineClamp: "2",
                          WebkitBoxOrient: "vertical",
                        }}
                      >
                        {mediaTitle}
                      </Typography>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          ) : (
            <Slider
              {...settings}
              infinite={blogs.length > 3}
              arrows={blogs.length > 3}
            >
              {blogs.map((item) => {
                const { mediaTitle = "", mediaLink = "" } = item || {};
                return (
                  <Box sx={{ padding: { xs: "16px 8px", md: "16px" } }}>
                    <Box
                      sx={{
                        borderRadius: "16px",
                        border: "2px solid #f0f0f5",
                        overflow: "hidden",
                        cursor: "pointer",
                      }}
                      onClick={() => router.push(mediaLink)}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          height: "250px",
                          alignItems: "center",
                          justifyContent: "center",
                          // backgroundImage: "url(/pattern-bg.png)",
                          backgroundRepeat: "repeat",
                          backgroundPosition: "top left",
                          backgroundColor: "#f6f6f6",
                        }}
                      >
                        <img
                          alt="article"
                          src={getThumborUrl(logoUrl)}
                          style={{
                            height: "120px",
                            width: "120px",
                            objectFit: "contain",
                          }}
                        />
                      </Box>
                      <Box sx={{ padding: "16px" }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: "2",
                            WebkitBoxOrient: "vertical",
                          }}
                        >
                          {mediaTitle}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Slider>
          )}
        </Box>
      )}
      {videoItems.length > 0 && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "24px",
            fontFamily: "inherit",
            color: "inherit",
          }}
        >
          <Typography variant="h5">
            <Box
              sx={{
                display: "inline-block",
                "&::after": {
                  content: "''",
                  width: "50%",
                  borderBottom: `3px solid`,
                  borderColor: "primary.main",
                  display: "block",
                  marginBottom: "-1px",
                  left: "50%",
                  right: "50%",
                },
              }}
            >
              Media
            </Box>
          </Typography>
          {desktopView && videoItems.length < 4 ? (
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  lg: "repeat(3, 1fr)",
                },
                gap: "32px",
              }}
            >
              {videoItems.map((item) => {
                const { mediaTitle = "", mediaLink = "" } = item || {};
                return (
                  <Box sx={{ borderRadius: "16px", height: "240px" }}>
                    {/*<iframe*/}
                    {/*  height="100%"*/}
                    {/*  width="100%"*/}
                    {/*  src={`https://www.youtube.com/embed/${mediaLink}`}*/}
                    {/*  title="YouTube video player"*/}
                    {/*  frameBorder="0"*/}
                    {/*  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"*/}
                    {/*  allowFullScreen*/}
                    {/*  style={{*/}
                    {/*    backgroundSize: "contain",*/}
                    {/*    backgroundRepeat: "no-repeat",*/}
                    {/*    backgroundPosition: "center",*/}
                    {/*    // pointerEvents: "none",*/}
                    {/*    borderRadius: "16px",*/}
                    {/*  }}*/}
                    {/*></iframe>*/}

                    <YouTubeEmbed
                        videoid={mediaLink}
                        params="controls=0"
                        style={{
                          backgroundSize: "contain",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center",
                        }}
                    />

                  </Box>
                );
              })}
            </Box>
          ) : (
            <Slider
              {...settings}
              infinite={videoItems.length > 3}
              arrows={videoItems.length > 3}
            >
              {videoItems.map((item) => {
                const { mediaTitle = "", mediaLink = "" } = item || {};
                return (
                  <Box sx={{ padding: { xs: "16px 8px", md: "16px" } }}>
                    <Box sx={{ borderRadius: "16px", height: "240px" }}>
                      {/*<iframe*/}
                      {/*  height="100%"*/}
                      {/*  width="100%"*/}
                      {/*  src={`https://www.youtube.com/embed/${mediaLink}`}*/}
                      {/*  title="YouTube video player"*/}
                      {/*  frameBorder="0"*/}
                      {/*  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"*/}
                      {/*  allowFullScreen*/}
                      {/*  style={{*/}
                      {/*    backgroundSize: "contain",*/}
                      {/*    backgroundRepeat: "no-repeat",*/}
                      {/*    backgroundPosition: "center",*/}
                      {/*    // pointerEvents: "none",*/}
                      {/*    borderRadius: "16px",*/}
                      {/*  }}*/}
                      {/*></iframe>*/}
                      <YouTubeEmbed
                          videoid={mediaLink}
                          params="controls=0"
                          style={{
                            backgroundSize: "contain",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: "center",
                          }}
                      />
                    </Box>
                  </Box>
                );
              })}
            </Slider>
          )}
        </Box>
      )}
    </Box>
  );
};

export default SliderInfo;
