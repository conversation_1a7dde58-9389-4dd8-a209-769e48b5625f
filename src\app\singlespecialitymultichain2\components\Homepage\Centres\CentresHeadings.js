'use client'
import { Box, Typography, useTheme } from '@mui/material'
import React from 'react'

const CentresHeadings = ({heading="", subHeading=""}) => {
    const theme = useTheme()
  return (
    <Box
    sx={{
      position: "absolute",
      left: 0,
      top: 0,
      bottom: 0,
      backgroundColor: theme.palette.primary.main,
      borderRadius: "10px",
      width: { xs: "100%", md: "40%" },
      p: 4,
      color: "text.primary",
      zIndex: 1,
    }}
  >
    <Typography variant="h4" component="h2" sx={{ fontWeight: 700, mb: 2, fontSize: "24px" }}>
     {heading || "Our Hospital Network"}
    </Typography>
    <Typography sx={{ mb: 5, lineHeight: 1.2, width:"80%", fontSize: "20px" }}>
     {subHeading || `We are one of India's leading providers of world-class healthcare services, with 5000+ doctors and a vast network of hospitals across the country.`}
    </Typography>
  </Box>
  )
}

export default CentresHeadings