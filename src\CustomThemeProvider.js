"use client";

import { ThemeProvider } from "@emotion/react";
import { createTheme, CssBaseline } from "@mui/material";

const CustomThemeProvider = ({ children, template, fontFamily }) => {
  const fontFamilyToUse = fontFamily || '"Roboto", "Arial", sans-serif';

  const theme = createTheme({
    palette: {
      primary: {
        main: template.primary_color || template.primaryColor || "#EE4266",
      },
      secondary: {
        main: template.secondary_color || template.secondaryColor || "#E78895",
      },
      text: {
        primary: template.primary_text_color || template.primaryTextColor || "#000000"
      }
    },
    typography: {
      fontFamily: fontFamilyToUse,
    },
    breakpoints: {
      values: {
        xs: 0,
        sm: 600,
        md: 900,
        lg: 1200,
        xl: 1536,
      },
    },
  });

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

export default CustomThemeProvider;
