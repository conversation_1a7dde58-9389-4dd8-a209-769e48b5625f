"use client";

import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import {
  COUNTRIES,
  countryToFlag,
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_OTP,
  API_ENDPOINT_REQUEST,
} from "@/constants";
import { useTheme } from "@emotion/react";
import {
  CircularProgress,
  FormControl,
  FormHelperText,
  InputBase,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import Box from "@mui/material/Box";
import { useContext, useState } from "react";
import AppointmentChange from "./appointmentChange";
import AppointmentSectionLayout from "./appointmentSectionLayout";
import { AppointmentSchedulerContext } from "./appointmentScheduler";
import axios from "axios";
import VerifyOtp from "./verifyOtp";
import { AppContext } from "@/app/AppContextLayout";

const RequestOtp = ({enterpriseCode = null}) => {
  const theme = useTheme();
  const { setViewSnackbarMain } = useContext(AppContext);
  const { dialCode, setDialCode, phone, setPhone, handleComponentDisplay } =
    useContext(AppointmentSchedulerContext);
  const [isLoading, setIsLoading] = useState(false);
  const [verifyOtp, setVerifyOtp] = useState(false);
  const [error, setError] = useState(null);

  const handleRequestOtp = async () => {
    if (phone.length !== 10) {
      setError("Enter a 10 digit valid number");
      return;
    }
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_OTP}${API_ENDPOINT_REQUEST}`;
    const reqBody = { phone: `${dialCode}${phone}` };
    try {
      const res = await axios.post(url, reqBody,  {
        params: {
          enterpriseCode: enterpriseCode
        },
        headers: {
          "Content-Type": "application/json",
          source: "website",
        },
      });
      const { data = {}, status = null } = res || {};
      if (status >= 200 && status < 300) {
        setVerifyOtp(true);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleMobileInput = (e) => {
    const value = e.target.value;
    if (value) setError(null);
    setPhone(e.target.value);
  }

  if (verifyOtp)
    return (
      <VerifyOtp
        handleRequestOtp={handleRequestOtp}
        isReqOtpLoading={isLoading}
      />
    );
  return (
    <AppointmentSectionLayout>
      <AppointmentChange />
      <Box>
        <Box>
          <Typography
            variant="subtitle2"
            sx={{ fontSize: "14px", mb: 1, fontWeight: "400" }}
          >
            Phone
          </Typography>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: ".3fr 1fr",
              gap: "16px",
            }}
          >
            <Select
              labelId="demo-customized-select-label"
              id="demo-customized-select"
              //   classes={{ select: classes.selectFocused }}
              value={dialCode}
              onChange={(e) => setDialCode(e.target.value)}
              // sx={{ alignSelf: "start" }}
              input={
                <InputBase
                  sx={{
                    border: `1px solid #BDBDBD`,
                    borderRadius: "10px",
                    padding: "8px 12px",
                    fontSize: "14px",
                    width: "100%",
                    alignSelf: "start",
                    transition: "all .3s",
                    "&.Mui-focused": {
                      borderColor: "primary.main",
                      boxShadow: `0 0 4px ${theme.palette.primary.main}`,
                    },
                  }}
                  placeholder="Enter Mobile Number"
                  inputProps={{ "aria-label": "Enter Mobile Number" }}
                />
              }
            >
              {COUNTRIES.map((country) => {
                const {
                  label = "",
                  dial_code: dialCode = "",
                  code = "IN",
                  value = "",
                } = country || {};
                return (
                  <MenuItem value={dialCode}>{`${countryToFlag(
                    code
                  )} ${value}`}</MenuItem>
                );
              })}
            </Select>
            <FormControl>
              <InputBase
                sx={{
                  border: `1px solid #BDBDBD`,
                  borderRadius: "10px",
                  padding: "8px 12px",
                  fontSize: "14px",
                  width: "100%",
                  transition: "all .3s",
                  "&.Mui-focused": {
                    borderColor: "primary.main",
                    boxShadow: `0 0 4px ${theme.palette.primary.main}`,
                  },
                }}
                placeholder="Enter Mobile Number"
                inputProps={{ "aria-label": "Enter Mobile Number" }}
                autoFocus
                value={phone || ""}
                onChange={handleMobileInput}
              />
              <FormHelperText error={error}>{error || ""}</FormHelperText>
            </FormControl>
          </Box>
        </Box>
        <Box sx={{ mt: 4 }}>
          <Typography
            variant="subtitle2"
            sx={{
              color: theme.palette.text.secondary,
              fontSize: "10px",
              fontWeight: "400",
            }}
            align="center"
          >
            You will receive an SMS with a verification code on this number
          </Typography>
          <PrimaryButton
            sx={{ borderRadius: "24px", width: "100%" }}
            onClick={handleRequestOtp}
            disabled={!Boolean(phone)}
          >
            {isLoading ? (
              <CircularProgress size={24} style={{ color: "#fff" }} />
            ) : (
              "Request Otp"
            )}
          </PrimaryButton>
        </Box>
      </Box>
    </AppointmentSectionLayout>
  );
};

export default RequestOtp;
