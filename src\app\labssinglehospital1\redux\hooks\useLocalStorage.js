'use client';

import { useEffect } from 'react';
import { useSelector } from 'react-redux';

export const useLocalStorage = (key, selector) => {
  const state = useSelector(selector);

  useEffect(() => {
    // Only run on client-side
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, JSON.stringify(state));
    }
  }, [key, state]);

  return null;
};

export const loadState = (key, initialState = {}) => {
  if (typeof window === 'undefined') {
    return initialState;
  }
  
  try {
    const serializedState = localStorage.getItem(key);
    if (!serializedState) {
      return initialState;
    }
    return JSON.parse(serializedState);
  } catch (err) {
    console.error('Error loading state from localStorage:', err);
    return initialState;
  }
};
