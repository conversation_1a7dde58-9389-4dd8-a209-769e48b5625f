"use client";

import {
  <PERSON>,
  Button,
  CircularProgress,
  FormControl,
  FormHelperText,
  InputBase,
  TextField,
  Typography,
} from "@mui/material";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_SECTION_LEADS,
  API_ENDPOINT_GENERATE,
} from "@/constants";
import { useContext, useState } from "react";
import axios from "axios";
import { AppContext } from "@/app/AppContextLayout";
import { useRouter } from "next/navigation";
import { isEmptyObject } from "@/app/utils/isEmptyObject";

const CoverWidgetForm = ({ enterpriseCode = null, campaignName = null,  productCode= null, requestMappings = [] }) => {
  const router = useRouter();
  const { setViewSnackbarMain, mobileView } = useContext(AppContext);
  const [input, setInput] = useState({ dialCode: "+91" });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleInputChange = (e) => {
    if (errors[e.target.name]) {
      const copyOfErrors = { ...errors };
      delete copyOfErrors[e.target.name];
      setErrors(copyOfErrors);
    }
    setInput((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleValidation = () => {
    const error = {};
    if (!input["firstName"] || input["firstName"].length === 0)
      error["firstName"] = "Cannot be empty";
    if (!input["phone"] || input["phone"].length === 0)
      error["phone"] = "Cannot be empty";
    if (input["phone"]?.length !== 10)
      error["phone"] = "Enter a 10 digit phone number";
    if (!isEmptyObject(error)) {
      setErrors(error);
      return false;
    }
    return true;
  };

  const extractCodeFromSlug = (slug) => {
    const segments = slug.split('-');
    return segments[segments.length - 1];
  }
  const handleLeadGeneration = async () => {
    if (!enterpriseCode || isLoading) return;
    if (!handleValidation()) return;
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}${productCode ? `?productCode=${extractCodeFromSlug(productCode)}` : ''}`;
    const reqBody = { ...input, leadSource: 6, lastName: "", type: 2, requestMappings };
    if (campaignName) reqBody["campaignName"] = campaignName;
    try {
      const response = await axios.post(url, reqBody, {
        headers: {
          "Content-Type": "application/json",
          source: mobileView ? "mweb" : "website",
        },
      });
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { message = "" } = result || {};
        router.push("/thank-you");
        setInput({ ...initialInput });
      }
    } catch (error) {
      //   setViewSnackbarMain({
      //     message: "Something went wrong. Please try again later!",
      //     type: "error",
      //   });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{ bgcolor: "primary.main", padding: "16px", borderRadius: "12px" }}
    >
      <Typography variant="h5" sx={{ color: "#fff", mb: 2 }} align="center">
        Book Appointment Now
      </Typography>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          // maxWidth: "400px",
        }}
      >
        {/* <TextField
          id="leadPhone"
          name="phone"
          label="Enter your Phone"
          placeholder="Enter Your Phone"
          value={input["phone"] || ""}
          onChange={handleInputChange}
          error={errors["phone"]}
          helperText={errors["phone"]}
          type="tel"
          // sx={{background: "#fff"}}
          InputProps={{ sx: { borderRadius: "10px" } }}
        /> */}
        <FormControl>
          <InputBase
            name="phone"
            id="leadPhone"
            sx={{
              borderRadius: "10px",
              padding: "8px 12px",
              fontSize: "14px",
              width: "100%",
              transition: "all .3s",
              background: "#fff",
              color: "#000",
            }}
            type="tel"
            value={input["phone"] || ""}
            onChange={handleInputChange}
            placeholder="Enter Your Phone"
            inputProps={{ "aria-label": "enter your name" }}
          />
          {Boolean(errors["phone"]) && (
            <FormHelperText error={errors["phone"]}>
              {errors["phone"]}
            </FormHelperText>
          )}
        </FormControl>
        <FormControl>
          <InputBase
            name="firstName"
            id="leadName"
            sx={{
              borderRadius: "10px",
              padding: "8px 12px",
              fontSize: "14px",
              width: "100%",
              transition: "all .3s",
              background: "#fff",
              color: "#000",
            }}
            value={input["firstName"] || ""}
            onChange={handleInputChange}
            placeholder="Enter Your Name"
            inputProps={{ "aria-label": "enter your name" }}
          />
          {Boolean(errors["firstName"]) && (
            <FormHelperText error={errors["firstName"]}>
              {errors["firstName"]}
            </FormHelperText>
          )}
        </FormControl>
        {/* <TextField
          id="leadName"
          name="firstName"
          label="Enter Your Name"
          placeholder="Enter Your Name"
          value={input["firstName"] || ""}
          onChange={handleInputChange}
          error={errors["firstName"]}
          helperText={errors["firstName"]}
        /> */}
        <Button
          id="leadGenSubmit"
          variant="contained"
          color="secondary"
          sx={{
            textTransform: "none",
            padding: "8px 16px",
            fontSize: "16px",
            width: { xs: "100%", md: "auto", mt: 1 },
            background: "secondary.main",
            boxShadow: "none",
            borderRadius: "10px",
            color: "text.secondary",
          }}
          onClick={handleLeadGeneration}
        >
          {isLoading ? (
            <CircularProgress size={24} sx={{ color: "white" }} />
          ) : (
            "Submit"
          )}
        </Button>
      </Box>
    </Box>
  );
};

export default CoverWidgetForm;
