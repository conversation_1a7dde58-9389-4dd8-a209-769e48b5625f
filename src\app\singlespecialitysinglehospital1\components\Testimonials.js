"use client";
import { <PERSON>, <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import "swiper/css";
import "swiper/css/pagination";
import { useRouter } from "next/navigation";
import { useContext } from "react";
import { AppContext } from "@/app/AppContextLayout";
import { useTheme } from "@emotion/react";


export default function TestimonialSlider() {
  const { websiteData } = useContext(AppContext);
    const { testimonials = [] } = websiteData || {};
  const router = useRouter()
  const theme = useTheme();
  return (
    <>
    {testimonials && testimonials.length > 0 && <Box
      sx={{
        width: "100%",
        // background:
        //   "linear-gradient(180deg, rgba(255,236,230,1) 0%, rg<PERSON>(230,255,248,1) 100%)",
      //   background: `
      //   linear-gradient(
      //     180deg,
      //   ${theme.palette.primary.main}20 0%,
      //     rgba(255, 255, 255, 1) 50%,
      //     ${theme.palette.secondary.main}20 100%
      //   )
      // `,

        backgroundColor:"#f8f8f8",
        py: { xs: 4, md: 6 },
        px: { xs: 2, md: 0 },
        position: "relative",
      }}
    >
      <Box
        sx={{
          maxWidth: "1200px",
          margin: "0 auto",
          textAlign: "center",
          padding:"0px 15px"
        }}
      >
        <Box 
            sx={{ 
              textAlign: "center", 
              mb: { xs: 4, sm: 5, md: 3 },
              maxWidth: "800px",
              mx: "auto"
            }}
          >
            <Typography
              variant="h5"
              sx={{
                color: "primary.main",
                mb: 1,
                display: "block",
                fontSize: { xs: "1.125rem", md: "1.25rem" },
              }}
            >
              OUR TESTIMONIALS
            </Typography>
            <Typography
              variant="h3"
              sx={{
                color: "#1a1a1a",
                mb: 2,
                fontSize: { xs: "1.875rem", sm: "2.25rem", md: "2.5rem" },
                lineHeight: 1.2,
              }}
            >
              What our patients have to say about Us
            </Typography>
          </Box>
        <Swiper
          modules={[Pagination, Autoplay]}
          spaceBetween={20}
          slidesPerView={1}
          loop={true}
          pagination={{ dynamicBullets: true, clickable: true }}
          autoplay={{ delay: 3000, disableOnInteraction: false }}
          breakpoints={{
            640: {
              slidesPerView: 1,
            },
            768: {
              slidesPerView: 2,
            },
          }}
          style={{
            paddingBottom: "50px",
          }}
        >
          {testimonials.map((testimonial) => (
            <SwiperSlide key={testimonial.id}>
              <Box
                sx={{
                  p: 4,
                  minHeight: {xs:"400px",md:"300px"},
                  display: "flex",
                  flexDirection: "column",
                  alignItems:"center",
                  border:"0.1px solid rgba(26, 26, 26, 0.12)",
                  backgroundColor:"#fff",
                  borderRadius: "16px",
                  boxShadow: "5px 4px 20px rgba(0,0,0,0.05)",
                  position: "relative",
                  overflow: "visible",
                }}
              >
                <Rating value={testimonial.rating} readOnly sx={{ mb: 2 }} />
                <FormatQuoteIcon
                  sx={{
                    fontSize: "4rem",
                    color: "#f0f0f0",
                    position: "absolute",
                    top: "2rem",
                    right: "2rem",
                    zIndex: 0,
                    opacity: 0.5,
                  }}
                />
                <FormatQuoteIcon
                  sx={{
                    fontSize: "4rem",
                    color: "#f0f0f0",
                    position: "absolute",
                    top: "2rem",
                    left: "2rem",
                    rotate:"180deg",
                    zIndex: 0,
                    opacity: 0.5,
                  }}
                />
                <Typography
                  sx={{
                    mb: 3,
                    lineHeight: 1.8,
                    color: "#666",
                    position: "relative",
                    zIndex: 1,
                  }}
                >
                  {testimonial.text}
                </Typography>
                <Box sx={{ mt: "auto" }}>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      color: "#333",
                    }}
                  >
                    {testimonial.name}
                  </Typography>
                </Box>
              </Box>
            </SwiperSlide>
          ))}
        </Swiper>
        <Box sx={{ display: "flex", mt: 3, justifyContent: "center" }}>
            <Button
            onClick={() => router.push("/reviews")}
              color="primary"
              sx={{
                borderRadius: "100px",
                backgroundColor: "primary.main",
                transform: "scale(1)",
                color: "text.primary",
                textTransform: "capitalize",
                fontWeight: "normal",
                fontSize: "14px",
                padding: "10px 20px",
                transition: "transform 0.3s ease",
                "&:hover": {
                  backgroundColor: "primary.main",
                },
              }}
            >
              View Testimonials
            </Button>
          </Box>
      </Box>
    </Box>}
    </>
    
  );
}
