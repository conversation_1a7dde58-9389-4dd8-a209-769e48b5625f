"use client";

import { Box, Typography } from "@mui/material";
import SectionLayout from "../styledComponents/SectionLayout";
import { useTheme } from "@emotion/react";
import useStyles from "../styles";
import OutlinedButton from "../styledComponents/OutlinedButton";
import { NextArrow, PrevArrow } from "./photosSection";
import Slider from "react-slick";
import BlogCard from "./blogCard";
import SectionHeading from "./sectionHeading";
import { useRouter } from "next/navigation";

const BlogsSection = ({ blogs = [] }) => {
  const classes = useStyles();
  const theme = useTheme();
  const router = useRouter();

  const settings = {
    dots: true,
    infinite: false,
    lazyLoad: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    adaptiveHeight: true,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const handleViewAllBlogs = () => {
    router.push("/blogs");
  };

  return (
    <SectionLayout>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "48px",
        }}
      >
        <SectionHeading align="right">Explore Our Blogs</SectionHeading>
        {blogs.length > 3 ? (
          <Box>
            <Slider {...settings} style={{ height: "auto" }}>
              {blogs.map((blog) => {
                const { code = null } = blog || {};
                return (
                  <Box key={code} sx={{ padding: "16px 24px" }}>
                    <BlogCard blog={blog} />
                  </Box>
                );
              })}
            </Slider>
          </Box>
        ) : (
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {
                xs: "1fr",
                sm: "repeat(3, 1fr)",
                md: "repeat(4, 1fr)",
              },
            }}
          >
            {blogs.slice(0, 6).map((blog, index) => {
              const { code = null } = blog || {};
              return (
                <Box key={code} id={`blog${index}`} sx={{ padding: "16px 24px" }}>
                  <BlogCard blog={blog} />
                </Box>
              );
            })}
          </Box>
        )}
        <Box sx={{ textAlign: "center" }}>
          <OutlinedButton
            id="viewAllBlogs"
            style={{ width: "fit-content" }}
            onClick={handleViewAllBlogs}
          >
            View all
          </OutlinedButton>
        </Box>
      </Box>
    </SectionLayout>
  );
};

export default BlogsSection;
