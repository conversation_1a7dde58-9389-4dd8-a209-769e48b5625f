import {NextResponse} from "next/server";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_WEBSITE
} from "@/constants";
import {getHostForServerComponent, getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";

const PUBLIC_FILE = /\.(.*)$/;
export async function middleware(request) {
    const {pathname} = request.nextUrl;
    const url = request.nextUrl.clone();

    // Exclude Next.js internals, API routes, static files, and public folder files
    if (
        pathname.startsWith("/_next") ||
        pathname.startsWith("/api") ||
        pathname.startsWith("/static") ||
        PUBLIC_FILE.test(pathname)
    ) {
        return NextResponse.next();
    }

    const domainName = getHostForServerComponent();

    try {
        const response = await fetch(
            `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true`, {
                cache: 'no-store' // Prevents caching
            }
        );

        const data = await response.json();
        const {result = {}, code} = data || {};
        if (code >= 200 && code < 300) {
            const {template = {}} = result || {};
            const {name: templateName = ""} = template || {};
            const websiteTemplateName = templateName?.toLowerCase().replace(/\s/g, "");

            if (websiteTemplateName) {
                url.pathname = `/${websiteTemplateName}${pathname}`;
                return NextResponse.rewrite(url);
            }

            return NextResponse.next();
        }
    } catch (error) {
        console.error("Error fetching template name:", error);
    }

    return NextResponse.next();
}

export const config = {
    matcher: ["/:path*"],
};
