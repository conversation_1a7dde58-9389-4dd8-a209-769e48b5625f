"use client";

import { Box, Typography } from "@mui/material";
import SectionLayoutAspire from "../styledComponents/SectionLayoutAspire";
import { styled } from "@mui/styles";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import PrimaryButton from "../styledComponents/PrimaryButton";
import Link from "next/link";
import { useRouter } from "next/navigation";

const WidgetLayout = ({ children, ...props }) => {
  return (
    <Box
      sx={{
        padding: "12px 16px",
        display: "flex",
        alignItems: "center",
        gap: "16px",
        justifyContent: "space-between",
        border: `2px solid ${props.bordercolor}`,
        background: props.background,
        borderRadius: "8px",
        cursor: "pointer",
        height: {xs: "100%", sm: "135px"},
      }}
    >
      {children}
    </Box>
  );
};

const WidgetsSection = () => {
  const router = useRouter();

  return (
    <SectionLayoutAspire>
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
          gap: "64px",
          marginTop: {xs: "12px"},
          alignItems: "center",
        }}
      >
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
            gap: "16px",
          }}
        >
          <Link id="appointmentsWidget" href="/doctors">
            <WidgetLayout bordercolor="#f1f5de" background="#fdfff4">
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 500 }}>
                  Book an Appointment
                </Typography>
                <Typography
                  variant="subtitle1"
                  sx={{
                    lineHeight: "1.2",
                    mt: 1,
                    fontWeight: 300,
                    fontSize: "14px",
                  }}
                >
                  With country's leading experts
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <CalendarMonthOutlinedIcon
                  fontSize="large"
                  sx={{ color: "#E5E483" }}
                />
              </Box>
            </WidgetLayout>
          </Link>
          <Link id="specialitiesWidget" href="/specialities">
            <WidgetLayout bordercolor="#d1f1ff" background="#edfaff">
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 500 }}>
                  Specialities
                </Typography>
                <Typography
                  variant="subtitle1"
                  sx={{
                    lineHeight: "1.2",
                    mt: 1,
                    fontWeight: 300,
                    fontSize: "14px",
                  }}
                >
                  Our expertise in healthcare
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Box
                  sx={{
                    height: "35px",
                    width: "35px",
                    backgroundColor: "#008DDA",
                    mask: "url(/speciality-icon.svg) no-repeat center / contain",
                  }}
                ></Box>
              </Box>
            </WidgetLayout>
          </Link>
          <Link id="blogsWidget" href="/doctors">
              <WidgetLayout bordercolor="#e9e6ff" background="#f8f7ff">
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 500 }}>
                  Doctors
                </Typography>
                <Typography
                  variant="subtitle1"
                  sx={{
                    lineHeight: "1.2",
                    mt: 1,
                    fontWeight: 300,
                    fontSize: "14px",
                  }}
                >
                  Top experts for your health
                </Typography>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Box
                  sx={{
                    height: "35px",
                    width: "35px",
                    backgroundColor: "#9195F6",
                    mask: "url(/doctor-male.svg) no-repeat center / contain",
                  }}
                ></Box>
              </Box>
            </WidgetLayout>
          </Link>

            <Link id="blogsWidget" href="/blogs">
                <WidgetLayout bordercolor="#f7e9e5" background="#fff6f4">
                    <Box>
                        <Typography variant="h5" sx={{ fontWeight: 500 }}>
                            Blogs
                        </Typography>
                        <Typography
                            variant="subtitle1"
                            sx={{
                                lineHeight: "1.2",
                                mt: 1,
                                fontWeight: 300,
                                fontSize: "14px",
                            }}
                        >
                            Empowering you with expert healthcare knowledge
                        </Typography>
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <Box
                            sx={{
                                height: "35px",
                                width: "35px",
                                backgroundColor: "#ffb8a2",
                                mask: "url(/blogwidget.svg) no-repeat center / contain",
                            }}
                        ></Box>
                    </Box>
                </WidgetLayout>
            </Link>
        </Box>
        <Box>
          <Typography variant="h4">
            For Healthcare Assistance
          </Typography>
          <PrimaryButton
            id="contactUsWidget"
            style={{
                marginTop: "24px",
                fontSize: 14,
                padding: '8px 32px', }}
            disableElevation
            onClick={() => router.push("/contact-us")}
          >
            Contact Us
          </PrimaryButton>
        </Box>
      </Box>
    </SectionLayoutAspire>
  );
};

export default WidgetsSection;
