"use client";

import { Box } from "@mui/material";
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';

export const CustomPrevArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <Box
      className={className}
      sx={{
        ...style,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "40px",
        height: "40px",
        borderRadius: "50%",
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
        zIndex: 1,
        left: { xs: "-5px", md: "-20px" },
        transition: "all 0.3s ease",
        "&:hover": {
          backgroundColor: "white",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.2)",
        },
        "&:before": {
          display: "none",
        }
      }}
      onClick={onClick}
    >
      <ArrowBackIosNewIcon sx={{ fontSize: "16px", color: "#002147" }} />
    </Box>
  );
};

export const CustomNextArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <Box
      className={className}
      sx={{
        ...style,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "40px",
        height: "40px",
        borderRadius: "50%",
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
        zIndex: 1,
        right: { xs: "-5px", md: "-20px" },
        transition: "all 0.3s ease",
        "&:hover": {
          backgroundColor: "white",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.2)",
        },
        "&:before": {
          display: "none",
        }
      }}
      onClick={onClick}
    >
      <ArrowForwardIosIcon sx={{ fontSize: "16px", color: "#002147" }} />
    </Box>
  );
};
