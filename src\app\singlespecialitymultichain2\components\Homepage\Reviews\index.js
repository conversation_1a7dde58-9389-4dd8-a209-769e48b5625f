import { Box, Container, Typography } from "@mui/material";
import ReviewsSlider from "../../ReviewsSlider";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import {
  getHomeComponentsData,
  getHomeSectionHeadings,
} from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";
const getReviewsHeadings = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeSectionHeadings(
      { domainName: domainName },
      HOME_SECTION_HEADING_TYPE.TESTIMONIAL
    );
    if (data.code === 200) {
      return data?.result || [];
    } else return [];
  } catch (error) {
    console.error("Error fetching reviews data:", error);
    return [];
  }
};

export default async function TestimonialSlider() {
  const reviews = await getReviewsHeadings();

  const heading = reviews[0]?.heading;
  let testimonials = [];

  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData(
      { domainName },
      HOME_WIDGET_TYPE.TESTIMONIAL
    );
    if (data.code === 200) {
      testimonials = data?.result?.testimonials || [];
    }
  } catch (error) {
    console.error("Error fetching testimonials:", error);
  }
  if (testimonials.length === 0) {
    return null;
  }
  return (
    <>
      <Box
        sx={{
          width: "100%",
          backgroundColor: "#f8f8f8",
          py: "24px",
          // px: { xs: 2, md: 0 },
          position: "relative",
        }}
      >
        {/* <StarPattern/> */}
        <Container maxWidth="lg">
          <Box
            sx={{
              textAlign: "center",
              mb: { xs: 4, sm: 5, md: 3 },
              maxWidth: "800px",
              mx: "auto",
            }}
          >
            <Typography
              variant="h4"
              align="center"
              fontWeight={400}
              sx={{
                mb: 2,
                fontSize: {
                  xs: "24px",
                  md: "36px",
                },
                color:"text.black"
              }}
            >
              {heading || "What our patients have to say about Us"}
            </Typography>
          </Box>
          <ReviewsSlider testimonials={testimonials} />
        </Container>
      </Box>
    </>
  );
}
