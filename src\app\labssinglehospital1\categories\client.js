"use client";
import React from "react";
import { Box, Container, Typography, Grid, Pagination, useMediaQuery, useTheme } from "@mui/material";
import CategoryCard from "../components/CategoryCard";
import { useRouter } from "next/navigation";

export default function CategoriesClient({ categories, currentPage, itemsPerPage }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const categoriesData = categories?.data || [];
  const totalCount = categories?.totalCount || categories?.availableCount || categoriesData.length;
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const router = useRouter();

  const handleCategoryClick = (category) => {
    const categoryParam = category.seoSlug;
    router.push(`/tests?category=${categoryParam}`);
  };

  const handlePageChange = (event, newPage) => {
    // Navigate to the new page
    if (newPage === 1) {
      router.push('/categories');
    } else {
      router.push(`/categories?page=${newPage}`);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 4, md: 6 } }}>
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: "bold",
            mb: 1,
            fontSize: { xs: "1.5rem", md: "2rem" },
            color: "text.black",
          }}
        >
          All Health Check Categories {categories?.totalCount ? `(${categories.totalCount})` : categories?.availableCount ? `(${categories.availableCount})` : categoriesData.length > 0 ? `(${categoriesData.length})` : ''}
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{ mb: 3, maxWidth: "800px" }}
        >
          Browse all our health check categories and find the right tests for your needs.
        </Typography>
      </Box>

      {categoriesData.length > 0 ? (
        <>
          <Grid container spacing={2}>
            {categoriesData.map((category) => (
              <Grid item xs={6} sm={4} md={3} key={category.code || category.id}>
                <CategoryCard
                  category={category}
                  onClick={() => handleCategoryClick(category)}
                />
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{
              mt: 4,
              mb: 2,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              borderTop: '1px solid #eaeaea',
              pt: 3
            }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={handlePageChange}
                size={isMobile ? "medium" : "large"}
                // showFirstButton
                // showLastButton
                sx={{
                  '& .MuiPaginationItem-root': {
                    color: 'text.black',
                  },
                  '& .MuiPaginationItem-page.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: 'white',
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    }
                  }
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Showing {currentPage} of {totalPages} pages
              </Typography>
            </Box>
          )}
        </>
      ) : (
        <Typography variant="body1" sx={{ py: 4, textAlign: 'center' }}>
          No categories available at the moment.
        </Typography>
      )}
    </Container>
  );
}
