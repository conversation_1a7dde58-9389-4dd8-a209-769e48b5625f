import { useState } from "react";
import Box from "@mui/material/Box";
import HealthAndSafetyOutlinedIcon from "@mui/icons-material/HealthAndSafetyOutlined";
import KeyboardArrowDownOutlinedIcon from "@mui/icons-material/KeyboardArrowDownOutlined";
import { useRouter } from "next/navigation";
import {Typography} from "@mui/material";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";

const NavbarDropdownItem = ({ section = {}, setAnchorEl, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const {
    displayName = "",
    sections = [],
    redirection = {},
    iconUrl = "",
    type = 1,
  } = section || {};

  const handleClick = () => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setIsOpen((prev) => !prev);
  };

  return (
    <Box sx={{ padding: "8px 16px"}}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
          justifyContent: "space-between",
          fontSize: "14px",
          cursor: "pointer",
          color:"#fff",
          transition: "all .3s",
          "&:hover": { color: "primary.main" }
        }}
        onClick={handleClick}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
          {
            iconUrl ?
            <Image
                alt="logo"
                src={getThumborUrl(iconUrl)}
                width={20}
                height={20}
            /> 
            : <Image src={"/right.png"} height={20} width={20}/>
          }
          
            <Typography
                fontSize="14px"
            >
                {displayName || ""}
            </Typography>
        </Box>
        {sections !== null && <KeyboardArrowDownOutlinedIcon />}
      </Box>
      {sections !== null &&
        isOpen &&
        sections.map((section, index) => {
          const { displayName = "" } = section || {};
          return (
            <NavbarDropdownItem
              key={`${displayName}${index}`}
              section={section}
              setAnchorEl={setAnchorEl}
            />
          );
        })}
    </Box>
  );
};

export default NavbarDropdownItem;
