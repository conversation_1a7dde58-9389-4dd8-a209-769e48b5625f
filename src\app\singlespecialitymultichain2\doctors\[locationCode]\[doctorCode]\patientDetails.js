"use client";

import { useContext, useState } from "react";
import { Box, CircularProgress, InputBase, Typography } from "@mui/material";
import AppointmentSectionLayout from "./appointmentSectionLayout";
import AppointmentChange from "./appointmentChange";
import { useTheme } from "@emotion/react";
import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_SECTION_APPOINTMENT,
  API_ENDPOINT_BOOK,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";
import { AppointmentSchedulerContext } from "./appointmentScheduler";
import axios from "axios";

const PatientDetails = ({ enterpriseCode = null }) => {
  const theme = useTheme();
  const { websiteData = {}, setViewSnackbarMain } = useContext(AppContext);
  const {
    dialCode,
    phone,
    handleComponentDisplay,
    selectedAppointment = {},
    doctorCode,
    setBookingInfo,
    patientDetails,
    setPatientDetails,
  } = useContext(AppointmentSchedulerContext);
  const [isLoading, setIsLoading] = useState(false);
  const { st: slotTime = "", ts: scheduleDate = "" } =
    selectedAppointment || {};
  const slotDate = scheduleDate.split(" ")[0] || "";

  const handleBookAppointment = async () => {
    if (!enterpriseCode) return;
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_SECTION_APPOINTMENT}${API_ENDPOINT_BOOK}?code=${doctorCode}`;
    const reqBody = {
      phone: `${dialCode}${phone}`,
      name: patientDetails["name"],
      slotTime,
      slotDate,
      email: patientDetails["email"],
    };
    try {
      const response = await axios.post(url, reqBody, {
        headers: {
          "Content-Type": "application/json",
          source: "website",
        },
      });
      const { data = {}, status = null } = response || {};
      const { result = {} } = data || {};
      if (status >= 200 && status < 300) {
        const { bookingCode = null, status = null } = result || {};
        setBookingInfo({ code: bookingCode, status });
        handleComponentDisplay(3);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setPatientDetails((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  return (
    <AppointmentSectionLayout>
      <AppointmentChange />
      <Box sx={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Box>
          <Typography
            variant="subtitle2"
            sx={{ fontSize: "14px", mb: 1, fontWeight: "400" }}
          >
            Patient Name
          </Typography>
          <InputBase
            name="name"
            sx={{
              border: `1px solid #BDBDBD`,
              borderRadius: "10px",
              padding: "8px 12px",
              fontSize: "14px",
              width: "100%",
              transition: "all .3s",
              "&.Mui-focused": {
                borderColor: "primary.main",
                boxShadow: `0 0 4px ${theme.palette.primary.main}`,
              },
            }}
            placeholder="Enter Patient Name"
            inputProps={{ "aria-label": "Enter Mobile Number" }}
            autoFocus
            value={patientDetails["name"] || ""}
            onChange={handleInputChange}
          />
        </Box>
        <Box>
          <Typography
            variant="subtitle2"
            sx={{ fontSize: "14px", mb: 1, fontWeight: "400" }}
          >
            Email
          </Typography>
          <InputBase
            name="email"
            sx={{
              border: `1px solid #BDBDBD`,
              borderRadius: "10px",
              padding: "8px 12px",
              fontSize: "14px",
              width: "100%",
              transition: "all .3s",
              "&.Mui-focused": {
                borderColor: "primary.main",
                boxShadow: `0 0 4px ${theme.palette.primary.main}`,
              },
            }}
            placeholder="Enter Email ID"
            inputProps={{ "aria-label": "Enter Mobile Number" }}
            value={patientDetails["email"] || ""}
            onChange={handleInputChange}
          />
        </Box>
      </Box>
      <Box sx={{ mt: 2 }}>
        <Typography
          variant="subtitle2"
          sx={{
            color: theme.palette.text.secondary,
            fontSize: "10px",
            fontWeight: "400",
          }}
          align="center"
        >
          You will receive an SMS with a verification code on this number
        </Typography>
        <PrimaryButton
          sx={{
            width: "100%",
            borderRadius: "24px",
            textTransform: "none",
            boxShadow: "none",
          }}
          onClick={handleBookAppointment}
          disabled={!Boolean(patientDetails["name"] && patientDetails["email"])}
        >
          {isLoading ? (
            <CircularProgress size={24} style={{ color: "#fff" }} />
          ) : (
            "Book Appointment"
          )}
        </PrimaryButton>
      </Box>
    </AppointmentSectionLayout>
  );
};

export default PatientDetails;
