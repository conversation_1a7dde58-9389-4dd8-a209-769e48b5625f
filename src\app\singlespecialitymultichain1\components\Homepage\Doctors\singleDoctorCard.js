import {getThumborUrl} from "@/utils/getThumborUrl";
import {useTheme} from "@emotion/react";
import {alpha, Box, Button, Typography} from "@mui/material";
import Image from "next/image";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import {useRouter} from "next/navigation";
import React from "react";

const SingleDoctorCard = ({doctorDetails = {}}) => {
    const theme = useTheme();
    const router = useRouter();

    const {
        code = null,
        name = "",
        profilePicture = "",
        medicalSpecialities = [],
        additionalDetails = null,
        seoSlug = "",
    } = doctorDetails || {};

    const {workExperience = "", consultationFee = ""} = additionalDetails || {};

    const handleDoctorCardClick = () => {
        router.push(`/doctors/${seoSlug}`);
    };

    return (
        <Box
            key={code}
            sx={{
                display: "flex",
                backgroundColor: "#fff",
                flexDirection: "column",
                boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.3)}`,
                borderRadius: "8px",
                cursor: "pointer",
                width: { xs: "100%", lg: "332px" },
                alignItems: "center", // Ensures the content inside is centered
            }}
            onClick={handleDoctorCardClick}
        >
            <Box
                sx={{

                    padding: "16px",
                    width: "100%", // Ensure it takes full width of the container
                }}
            >
                <Image
                    alt="Doctor profile picture"
                    src={
                        profilePicture
                            ? getThumborUrl(profilePicture, 0, 0)
                            : "/placeholder.webp"
                    }
                    height={300}
                    width={300}
                    style={{
                        display: "block",
                        margin: "0 auto",
                        borderRadius: "8px", // Ensures the image is rounded on all corners
                        objectFit: "cover",  // Center crops the image
                        height: "300px",     // Adjust the height if necessary
                        width: "300px",      // Adjust the width if necessary
                    }}

                />
            </Box>

            <Box
                style={{
                    textAlign: "center",
                    paddingBottom: "16px",
                    marginTop: "16px",
                }}
            >
                <Typography
                    variant="subtitle1"
                    style={{ fontWeight: "500", fontSize: "1.2rem" }}
                >
                    {name || ""}
                </Typography>
                <Typography variant="subtitle2" style={{ fontWeight: "300" }}>
                    {medicalSpecialities?.length > 0 && (
                        <>
                            Specialised in{" "}
                            {medicalSpecialities.map((speciality, index) => {
                                const { name: specialityName = "" } = speciality || {};
                                if (index === medicalSpecialities.length - 1)
                                    return <>{specialityName}</>;
                                else return <>{`${specialityName},`}</>;
                            })}
                        </>
                    )}
                </Typography>
                <Button
                    color="primary"
                    style={{ fontWeight: "500", textTransform: "none" }}
                    onClick={handleDoctorCardClick}
                >
                    Make an appointment <ArrowRightAltIcon />
                </Button>
            </Box>
        </Box>

    );
};

export default SingleDoctorCard;
