"use client";

import { Box, Typography } from "@mui/material";
import Rating from "@mui/material/Rating";
import { useTheme } from "@emotion/react";
import useStyles from "../styles";
import OutlinedButton from "../styledComponents/OutlinedButton";
import { getInitials } from "../../utils/getInitials";
import SectionHeading from "./sectionHeading";
import { useRouter } from "next/navigation";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

const ReviewsSection = ({ testimonials = [] }) => {
  const classes = useStyles();
  const theme = useTheme();
  const router = useRouter();

  const handleViewAllReview = () => {
    router.push("/reviews");
  }

  return (
    <SectionLayoutSingleSpecialitySingleHospital background={"#fcf9f9"}>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "48px",
        }}
      >
        <SectionHeading>Our Patient Stories</SectionHeading>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: {
              xs: "1fr",
              sm: "1fr 1fr",
              md: "repeat(3, 1fr)",
            },
            gap: "32px",
          }}
        >
          {testimonials.slice(0, 3).map((testimonial, index) => {
            const { name = "", rating = null, text = "" } = testimonial || {};
            return (
              <Box key={index}>
                <Box className={classes.reviewsSectionCarousalBoxItem}>
                  <Box className={classes.reviewsSectionContentBox}>
                    <Box className={classes.reviewsSectionReviewBox}>
                      <Rating
                        name="simple-controlled"
                        value={rating}
                        readOnly
                      />
                      <Typography
                        variant="body1"
                        className={classes.reviewsSectionReviewBoxText}
                      >
                        {text || ""}
                      </Typography>
                    </Box>
                  </Box>
                  <Box className={classes.reviewsSectionProfileBox}>
                    <Box className={classes.reviewsSectionProfileImg}>
                      {getInitials(name) || ""}
                    </Box>
                    <Box className={classes.reviewsSectionProfileDetailsBox}>
                      <Typography className={classes.reviewsSectionProfileName}>
                        {name || ""}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Box>
        <Box sx={{ textAlign: "center" }}>
          <OutlinedButton id="viewAllReviews" style={{ width: "fit-content" }} onClick={handleViewAllReview} >
            View all
          </OutlinedButton>
        </Box>
      </Box>
    </SectionLayoutSingleSpecialitySingleHospital>
  );
};

export default ReviewsSection;
