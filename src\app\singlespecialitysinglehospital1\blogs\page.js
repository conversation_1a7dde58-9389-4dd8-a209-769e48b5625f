"use client";
import { useContext, useEffect, useState } from "react";
import {
  Box,
  Container,
  Typography,
  Grid,
  CardContent,
  CardActions,
  Pagination,
  Skeleton,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { ArrowRight } from "@mui/icons-material";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";
import { AppContext } from "@/app/AppContextLayout";
import { API_ENDPOINT_BLOGS, API_SECTION_API, API_SECTION_PUBLIC, API_SECTION_WEBSITE, API_VERSION, HARBOR_API_DOCFYN_DOMAIN } from "@/constants";
import axios from "axios";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { useRouter } from "next/navigation";

const StyledBox = styled(Box)(({ theme }) => ({
  cursor: "pointer",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  transition: "transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out",
  "&:hover": {
    transform: "scale(1.01)",
    boxShadow: theme.shadows[8],
    "& .arrow-icon": {
      transform: "translateX(4px)",
    },
    "& .read-more-text": {
      color: theme.palette.primary.main,
    },
  },
  borderRadius: theme.spacing(2),
  overflow: "hidden",
  backgroundColor: "rgba(205, 205, 205, 0.14)",
}));

const StyledMedia = styled(Box)(({ theme }) => ({
  cursor: "pointer",
  paddingTop: "60%",
  position: "relative",
  backgroundSize: "cover",
  backgroundPosition: "center",
  "&::after": {
    content: '""',
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "30%",
    background:
      "linear-gradient(to top, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%)",
  },
}));

const StyledButton = styled(Typography)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: "8px",
  cursor: "pointer",
  transition: "all 0.3s ease",
  "& .read-more-text": {
    fontSize: "0.875rem",
    color: "#1a1a1a",
    transition: "color 0.3s ease",
  },
  "& .arrow-icon": {
    fontSize: "1.2rem",
    transition: "transform 0.3s ease",
    color: "#1a1a1a",
  },
}));


export default function AllBlogsPage() {
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const [blogs, setBlogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  const router = useRouter()

  const getBlogs = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?list=true`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        setBlogs(result);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    if (enterpriseCode) getBlogs();
  }, [enterpriseCode]);

  return (
    <Box sx={{ backgroundColor: "#f8f8f8", paddingTop: "120px" }}>
      <SectionLayoutSingleSpecialitySingleHospital>
        <Box sx={{ mb: 3, textAlign: "center" }}>
          <Typography
            variant="h5"
            sx={{
              color: "primary.main",
              mb: 1,
              display: "block",
            }}
          >
            OUR BLOGS
          </Typography>
          <Typography
            variant="h3"
            component="h2"
            sx={{
              color: "#1a1a1a",
              mb: 2,
            }}
          >
            Latest Articles & Blogs
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "#666",
              maxWidth: "600px",
              mx: "auto",
            }}
          >
            Stay informed with our latest insights, discoveries, and expert
            advice on eye care and vision health.
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {isLoading
            ? [1, 2, 3, 4, 5, 6].map((_, index) => {
                return (
                  <Skeleton
                    key={index}
                    variant="rounded"
                    animation="wave"
                    height={200}
                  />
                );
              })
            : blogs && blogs.length > 0 &&  blogs.map((blog, index) => {
            const {
              code = null,
              content = "",
              // image_url: blogImage = "",
              title = "",
              imageUrl = "",
              seoSlug,
            } = blog;
                return (
                  <Grid item xs={12} sm={6} md={4} key={code}>
                    <StyledBox key={index}
                    onClick={() => {
                      if (blog.seoSlug) router.push(`/blogs/${seoSlug}`);
                    }}>
                      <StyledMedia
                        sx={{
                          backgroundImage: `url(${imageUrl || "/blogs-default.avif"})`,
                        }}
                      />
                      <CardContent sx={{ flexGrow: 1, px: 2 }}>
                        <Typography
                          variant="h5"
                          component="h3"
                          sx={{
                            color: "#1a1a1a",
                            lineHeight: 1.3,
                          }}
                        >
                          {title}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2, pt: 0 }}>
                        <StyledButton>
                          <span className="read-more-text">
                            Continue Reading
                          </span>
                          <ArrowRight className="arrow-icon" />
                        </StyledButton>
                      </CardActions>
                    </StyledBox>
                  </Grid>
                );
              })}
          {/* {allBlogPosts.map((post, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <StyledBox>
                <StyledMedia
                  sx={{
                    backgroundImage: `url(${post.image})`,
                  }}
                />
                <CardContent sx={{ flexGrow: 1, px: 2 }}>
                  <Typography
                    variant="h5"
                    component="h3"
                    sx={{
                      color: "#1a1a1a",
                      lineHeight: 1.3,
                    }}
                  >
                    {post.title}
                  </Typography>
                </CardContent>
                <CardActions sx={{ p: 2, pt: 0 }}>
                  <StyledButton>
                    <span className="read-more-text">Continue Reading</span>
                    <ArrowRight className="arrow-icon" />
                  </StyledButton>
                </CardActions>
              </StyledBox>
            </Grid>
          ))} */}
        </Grid>
      </SectionLayoutSingleSpecialitySingleHospital>
    </Box>
  );
}

// "use client";

// import Box from "@mui/material/Box";
// import Button from "@mui/material/Button";
// import Chip from "@mui/material/Chip";
// import InputBase from "@mui/material/InputBase";
// import Pagination from "@mui/material/Pagination";
// import Typography from "@mui/material/Typography";
// import SectionLayout from "../styledComponents/SectionLayout";
// import FaqsSection from "../components/faqsSection";
// import BlogCard from "../components/blogCard";
// import SearchIcon from "@mui/icons-material/Search";
// import {
//   HARBOR_API_DOCFYN_DOMAIN,
//   API_SECTION_API,
//   API_VERSION,
//   API_SECTION_PUBLIC,
//   API_SECTION_WEBSITE,
//   API_ENDPOINT_BLOGS,
// } from "@/constants";
// import { useContext, useEffect, useState } from "react";
// import { AppContext } from "../../AppContextLayout";
// import axios from "axios";
// import { Skeleton } from "@mui/material";
// import SectionLayoutOasis from "../styledComponents/SectionLayoutOasis";

// export default function Blogs() {
//   const { websiteData, setViewSnackbarMain } = useContext(AppContext);
//   const [blogs, setBlogs] = useState([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const { enterprise_code: enterpriseCode = null } = websiteData || {};

//   const getBlogs = async () => {
//     setIsLoading(true);
//     const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?list=true`;
//     try {
//       const response = await axios.get(url);
//       const { status = null, data = {} } = response || {};
//       if (status >= 200 && status < 300) {
//         const { result = [] } = data || {};
//         setBlogs(result);
//       }
//     } catch (error) {
//       setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   useEffect(() => {
//     if (enterpriseCode) getBlogs();
//   }, [enterpriseCode]);

//   return (
//     <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
//       <SectionLayoutOasis>
//         <Box
//           sx={{
//             display: "flex",
//             flexDirection: "column",
//             gap: { xs: "32px", md: "64px" },
//           }}
//         >
//           <Typography variant="h4">Blogs</Typography>
//           <Box
//             sx={
//               {
//                 // display: "flex",
//                 // flexDirection: { xs: "column-reverse", md: "row" },
//                 // gap: "48px",
//               }
//             }
//           >
//             <Box
//               sx={{
//                 display: "flex",
//                 flexDirection: "column",
//                 gap: "48px",
//                 flex: 1,
//               }}
//             >
//               <Box
//                 sx={{
//                   display: "grid",
//                   gridTemplateColumns: {
//                     xs: "1fr",
//                     sm: "1fr 1fr",
//                     md: "repeat(3, 1fr)",
//                     lg: "repeat(4, 1fr)",
//                   },
//                   gap: "32px",
//                 }}
//               >
// {isLoading
//   ? [1, 2, 3, 4, 5, 6].map((_, index) => {
//       return (
//         <Skeleton
//           key={index}
//           variant="rounded"
//           animation="wave"
//           height={200}
//         />
//       );
//     })
//   : blogs.map((blog, index) => {
//       const { code = null } = blog || {};
//       return <BlogCard id={`blog${index}`} key={code} blog={blog} />;
//     })}
//               </Box>
//               {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
//                 <Pagination count={10} variant="outlined" shape="rounded" />
//               </Box> */}
//             </Box>
//             {/* <Box
//               sx={{
//                 display: { xs: "none", md: "flex" },
//                 flexDirection: "column",
//                 gap: "24px",
//                 position: { xs: "static", md: "sticky" },
//                 top: { xs: "0", md: "200px" },
//                 alignSelf: "start",
//                 width: { xs: "100%", md: "400px" },
//               }}
//             >
//               <div
//                 style={{
//                   backgroundColor: "#fff",
//                   border: "1px solid #d3d4db",
//                   padding: "8px 12px",
//                   borderRadius: "6px",
//                   display: "flex",
//                   alignItems: "center",
//                   gap: "16px",
//                 }}
//               >
//                 <InputBase placeholder="Search blog" fullWidth />
//                 <SearchIcon />
//               </div>
//               <Box
//                 sx={{
//                   display: "flex",
//                   justifyContent: "space-between",
//                   gap: "16px",
//                 }}
//               >
//                 <Typography variant="h5">Category</Typography>
//                 <Button variant="text" sx={{ textTransform: "none" }}>
//                   Clear all
//                 </Button>
//               </Box>
//               <Box sx={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//                 <Chip label="Cardiac Sciences" onClick={() => {}} />
//                 <Chip label="Oncology" />
//                 <Chip label="Urology" />
//                 <Chip label="Vascular Surgery" />
//                 <Chip label="ENT" />
//               </Box>
//             </Box> */}
//           </Box>
//         </Box>
//       </SectionLayoutOasis>
//       {/* <FaqsSection /> */}
//     </Box>
//   );
// }
