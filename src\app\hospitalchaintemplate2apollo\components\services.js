"use client"
import {alpha, Box, Typography} from "@mui/material";
import React, {useContext} from "react";
import SectionLayoutChainTemp2 from "@/app/hospitalchaintemplate2apollo/styledComponents/SectionLayoutChainTemp2";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import SectionHeading from "@/app/hospitalchaintemplate2apollo/components/sectionHeading";
import Image from "next/image";
import {AppContext} from "@/app/AppContextLayout";

const Services = () => {
    const {websiteData} = useContext(AppContext);
    const {
        banners = [],
        multiMedia = [],
        testimonials = [],
        blogs = [],
        faqs = [],
        websiteServices: services = []
    } = websiteData || {};

    if (services.length <= 0){
        return (
            <></>
        )
    }

    return (
        <SectionLayoutChainTemp2
            id="services">
            <Box
                sx={{display: "flex", flexDirection: "column", gap: "48px",}}>
                <SectionHeading align="right">Uncover Our Diverse Services</SectionHeading>
                <Box sx={{
                    display: "grid",
                    gridTemplateColumns: {xs: "1fr", md: "1fr 1fr", lg: "repeat(3, 1fr)"},
                    columnGap: "24px",
                    rowGap: "24px"
                }}>
                    {services.map((item, index) => {
                        const {service = {}} = item || {};
                        const {
                            name: serviceName = null,
                            short_description: serviceDescription = null,
                            image_url: serviceImg = "",
                        } = service || {};
                        return (
                            <Box key={index} style={{
                                borderRadius: "8px",
                                backgroundColor: '#fff',
                                padding: "16px",
                                display: "flex",
                                boxShadow: `0 4px 20px ${alpha("#333", 0.1)}`,
                                gap: "16px"
                            }}>
                                {Boolean(serviceImg) && (
                                    <Image
                                        alt="service-image"
                                        src={getThumborUrl(serviceImg, 0, 0)}
                                        height={64}
                                        width={64}
                                        style={{
                                            objectFit: "cover",
                                            marginTop: "6px",
                                            objectPosition: "center",
                                        }}
                                    />
                                )}
                                <div style={{ marginLeft: !serviceImg && "8px"}}>
                                    <Typography variant="h6"
                                                sx={{fontSize: "18px"}}>{serviceName || ""}</Typography>
                                    <Typography
                                        variant="body1"
                                        sx={{
                                            fontWeight: "300",
                                            color: "#666666",
                                            display: "-webkit-box",
                                            boxOrient: "vertical",
                                            wordBreak: "break-word",
                                            overflow: "hidden",
                                            WebkitBoxOrient: "vertical",
                                            lineHeight: "1.5",
                                            textOverflow: "ellipsis",
                                            WebkitLineClamp: "4",
                                        }}
                                    >
                                        {serviceDescription || ""}
                                    </Typography>
                                </div>
                            </Box>
                        );
                    })}
                </Box>
            </Box>

        </SectionLayoutChainTemp2>
    );
};

export default Services;
