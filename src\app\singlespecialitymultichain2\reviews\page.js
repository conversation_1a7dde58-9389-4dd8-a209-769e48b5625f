// "use client";
import Box from "@mui/material/Box";
import {
  Grid,
  Rating,
  Typography,
} from "@mui/material";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_REVIEWS,
} from "@/constants";
import SectionLayout from "@/app/singlespecialitymultichain2/components/SectionLayout";
import FormatQuoteIcon from "@mui/icons-material/FormatQuote";
import {getEnterpriseCode} from "@/app/oasis/blogs/[blogCode]/layout";
import { getWebsiteData } from "../layout";
import Footer from "../Footer";
// import ReviewCard from "@/app/aspire/components/reviewCard";

const getReviews = async (enterpriseCode) => {
  if (!enterpriseCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}?list=true`;
  try {
    const response = await fetch(url, {
      cache: "no-store"
    });
    const jsonResult = await response.json();
    const { result: reviews = {} } = jsonResult || {};
    return reviews;
  } catch (error) {
    console.log("getReviewsError", error);
  }
};

export default async function Reviews() {
  const websiteData = await getWebsiteData();
  const enterpriseCode = await getEnterpriseCode();
  const testimonials = await getReviews(enterpriseCode) || [];

  return (
    <>
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayout>
        <Typography sx={{py:"16px"}} variant="h4">Patient Reviews</Typography>
        {/* <Box
          sx={{ display: "flex", flexDirection: "column", gap: "48px", mt: 2 }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {
                  xs: "1fr",
                  sm: "1fr 1fr",
                  lg: "repeat(3, 1fr)",
                },
                columnGap: "32px",
                rowGap: "64px",
              }}
            >
              {testimonials.map((testimonial, index) => {
                return <ReviewCard key={index} testimonial={testimonial} />;
              })}
            </Box>
          </Box>
        </Box> */}
         <Grid container spacing={3}>
          {testimonials.map((testimonial, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Box
                sx={{
                  p: 4,
                  minHeight: {xs:"400px",md:"300px"},
                  display: "flex",
                  flexDirection: "column",
                  alignItems:"center",
                  backgroundColor:"#fff",
                  borderRadius: "16px",
                  border:"0.1px solid rgba(26, 26, 26, 0.12)",
                  boxShadow: "0 4px 20px rgba(0,0,0,0.05)",
                  position: "relative",
                  overflow: "visible",
                }}
              >
                <Rating value={testimonial.rating} readOnly sx={{ mb: 2 }} />
                <FormatQuoteIcon
                  sx={{
                    fontSize: "4rem",
                    color: "#f0f0f0",
                    position: "absolute",
                    top: "2rem",
                    right: "2rem",
                    zIndex: 0,
                    opacity: 0.5,
                  }}
                />
                <FormatQuoteIcon
                  sx={{
                    fontSize: "4rem",
                    color: "#f0f0f0",
                    position: "absolute",
                    top: "2rem",
                    left: "2rem",
                    rotate:"180deg",
                    zIndex: 0,
                    opacity: 0.5,
                  }}
                />
                <Typography
                  sx={{
                    mb: 3,
                    lineHeight: 1.8,
                    color: "#666",
                    position: "relative",
                    zIndex: 1,
                  }}
                >
                  {testimonial.text}
                </Typography>
                <Box sx={{ mt: "auto" }}>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      color: "#333",
                    }}
                  >
                    {testimonial.name}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </SectionLayout>
      {/* <FaqsSection /> */}
    </Box>
    <Footer websiteData={websiteData}/>
    </>
  );
}
