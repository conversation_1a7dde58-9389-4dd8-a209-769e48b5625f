"use client";

import QuickEnquiry from "@/app/commoncomponents/quickEnquiry";
import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_ENTERPRISE,
    API_ENDPOINT_PROCEDURE, LEAD_SOURCES,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";
import parse from "html-react-parser";
import FaqsSection from "@/app/oasis/components/faqsSection";
import { getParsedHTML } from "@/app/utils/getParsedHTML";
import { useRouter } from "next/navigation";
import {useTheme} from "@emotion/react";
import {alpha} from "@mui/material";

export default function ProcedureDetail({ params }) {
  const router = useRouter();
  const { procedureCode = null, specialityCode = null } = params || {};
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const theme = useTheme();
  const [procedureDetails, setProcedureDetails] = useState({});
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const {
    displayName: procedureName = "",
    shortDescription = "",
    description = "",
    bannerUrl = "",
    otherProcedures = [],
    faqs = [],
  } = procedureDetails || {};

  const getProcedureDetails = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PROCEDURE}?code=${procedureCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { procedures = [] } = result || {};
        const { speciality = {}, seoSlug: procedureSeoSlug = "" } = procedures[0];
        const { seoSlug: specialitySeoSlug = "" } = speciality || {};
        if (specialityCode !== specialitySeoSlug || procedureCode !== procedureSeoSlug) {
          router.push(`/specialities/${specialitySeoSlug}/procedures/${procedureSeoSlug}`);
        }
        setProcedureDetails(procedures[0] || {});
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    if (procedureCode) getProcedureDetails();
  }, [procedureCode]);

  return (
    <Box>
      <Box
        sx={{
          position: "relative",
          minHeight: "280px",
          bgcolor: alpha(theme.palette.primary.main, 0.7),
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <SectionLayout>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              alignItems: "center",
            }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{
                color: "#fff",
                fontSize: { xs: "2rem", sm: "2.5rem", md: "40px" },
              }}
            >
              {procedureName || ""}
            </Typography>
            {/* <PrimaryButton style={{ textTransform: "none" }}>
              Find a doctor
            </PrimaryButton> */}
            <Typography
              variant="subtitle1"
              align="center"
              sx={{ color: "#fff", fontSize: "14px" }}
            >
              {shortDescription || ""}
            </Typography>
          </Box>
        </SectionLayout>
      </Box>
      <SectionLayout>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", md: "1fr 400px" },
            gap: "48px",
          }}
        >
          <Box>
            <Typography variant="h4">Overview</Typography>
            <Typography variant="subtitle1" sx={{ mt: 2 }}>
                <div
                    className="ck-content"
                    dangerouslySetInnerHTML={{__html: description}} // Render HTML safely
                />
            </Typography>
          </Box>
          <Box>
            <QuickEnquiry leadSource={LEAD_SOURCES.PROCEDURE_PAGE} productCode={procedureCode}/>
          </Box>
        </Box>
      </SectionLayout>
      {faqs.length > 0 && <FaqsSection faqs={faqs} />}
    </Box>
  );
}
