
import { getHealthTips } from "@/api/healthtip.service";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE
} from "@/constants";

export const getEnterpriseCode = async () => {
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true`;
  try {
  const res = await fetch(url, {
    cache: "no-store"
  });
  const jsonRes = await res.json();
  const { result = {} } = jsonRes || {};
  const { enterprise_code: enterpriseCode = null } = result || {};
  return enterpriseCode;
} catch (error) {
  console.log("getWebsiteData", error);
}
}

const fetchHealthTipDetail = async (enterpriseCode, healthTipCode) => {
  try {
    const data = await getHealthTips(enterpriseCode, { code: healthTipCode });
    if (data.code === 200) {
      const { result = [] } = data || {};
      const { seoTitle = "", seoDescription = "" } = result[0] || {};
      return {
        title: seoTitle || "Health Tips",
        description: seoDescription || "Simple health tips to keep you fit and healthy every day",
      };
    }
  } catch (error) {
    console.log(error);
  }
};

export const generateMetadata = async ({ params }) => {
  const { healthTipCode = null } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode();
    const metaData = await fetchHealthTipDetail(enterpriseCode, healthTipCode);
    return metaData;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
