"use client"
import React, { useEffect, useState } from "react";

import { Box, Collapse, Divider, Typography } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import useStyles from "../../../styles";
import homepageStyles from "../styles";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
const FaqSection = ({faqs = []}) => {
  const commonClasses = useStyles();
  const classes = homepageStyles();
  const [faqsOrder, setFaqsOrder] = useState({});
  // const [isFaqOpen, setIsFaqOpen] = useState(false);

  const handleExpandFaq = (index) => {
    setFaqsOrder((prev) => ({...prev, [index]: !faqsOrder[index]}))
    // setIsFaqOpen((prev) => !prev);
  };

  useEffect(() => {
    faqs.forEach((faq, index) => {
      // const {  }
      setFaqsOrder((prev) => ({...prev, [index] : false}));
    })
  }, [faqs])

  return (
      <SectionLayout
          style={{ }}>
        <Box
            sx={{
              gap: {xs: "32px", md: "48px"}
            }}
        >
          <Box
              className={classes.faqsSecionFlexBox}
          >
            <Box
                className={classes.faqSectionFaq}
            >
              {faqs.map((faq, index) => {
                const { question = '', answer = '' } = faq || {};
                return  <Box
                    key={index}
                    className={classes.faqSectionGridItem}
                >
                  <Box
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        cursor: "pointer",
                      }}
                      onClick={() => handleExpandFaq(index)}
                  >
                    <Typography style={{ fontWeight: "500" }}>
                      {question || ''}
                    </Typography>
                    <ExpandMoreIcon
                        className={`${classes.faqSectionExpandIcon} ${
                            faqsOrder[index] ? classes.faqSectionExpandIconActive : ""
                        }`}
                    />
                  </Box>
                  {faqsOrder[index] && (
                      <Divider
                          style={{ marginTop: "8px", backgroundColor: "#E8E8E8" }}
                      />
                  )}
                  <Collapse in={faqsOrder[index]}>
                    <Typography style={{ marginTop: "1rem", fontSize: "14px" }}>
                      {answer || ''}
                    </Typography>
                  </Collapse>
                </Box>;
              })}
            </Box>
            <Box
                className={classes.faqsSectionHeadingBox}
            >
              <Typography variant="h5"
                          sx={{
                            textTransform: "uppercase",
                            color: "primary.main",
                          }}>
                FAQ'S
              </Typography>
              <Typography
                  variant="h3"
                  sx={{color: "#333333"}}
                  className={classes.homepageSectionSubHeading}
              >
                Get Informed: Health{" "}
                <span className={classes.sectionHeading}>FAQs</span>
              </Typography>
            </Box>
          </Box>
        </Box>
      </SectionLayout>
  );
};

export default FaqSection;
