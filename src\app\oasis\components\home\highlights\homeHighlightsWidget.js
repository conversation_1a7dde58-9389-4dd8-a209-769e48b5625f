"use client";

import {Box} from "@mui/material";
import {useContext, } from "react";
import {useRouter} from "next/navigation";
import {AppContext} from "@/app/AppContextLayout";
import WebHighlightsWidget from "@/app/oasis/components/home/<USER>/webHighlightsWidget";
import MWebHighlightsWidget from "@/app/oasis/components/home/<USER>/mwebHighlightsWidget";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";

const HomeHighlightsWidget = () => {
    const router = useRouter();
    // const [specialities, setSpecialities] = useState([]);
    // const [isLoading, setIsLoading] = useState(true);
    const {websiteData = {},} = useContext(AppContext);
    const {enterprise_code: enterpriseCode = null, highlights = []} = websiteData || {};

    // const getFeaturedSpecialities = async () => {
    //     setIsLoading(true);
    //     const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?isFeatured=true`;
    //     try {
    //         const response = await axios.get(url);
    //         const {status = null, data = {}} = response || {};
    //         if (status >= 200 && status < 300) {
    //             const {result = {}} = data || {};
    //             const {specialities = []} = result || {};
    //             setSpecialities(specialities);
    //         }
    //     } catch (error) {
    //         //   setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    //     } finally {
    //         setIsLoading(false);
    //     }
    // };

    // useEffect(() => {
    //     if (enterpriseCode) getFeaturedSpecialities();
    // }, [enterpriseCode]);

    return (
      <>
        {highlights.length > 0 && (
          <Box
            sx={{
              position: "absolute",
              bottom: "-40px",
              left: "50%",
              transform: "translateX(-50%)",
              width: "max-content",
              zIndex: 1,
              display: { xs: "none", md: "block" },
            }}
          >
            <WebHighlightsWidget highlights={highlights} />
          </Box>
        )}
        <Box
          sx={{
            position: "relative",
            display: { xs: "block", md: "none" },
            bgcolor: "#f6f6f6",
          }}
        >
          {highlights.length > 0 && (
            <SectionLayout>
              <MWebHighlightsWidget highlights={highlights} />
            </SectionLayout>
          )}
        </Box>
      </>
    );
};

export default HomeHighlightsWidget;
