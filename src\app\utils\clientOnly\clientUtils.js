import "client-only"
export const getWebsiteHost = () => {
    if (window.location.hostname === "localhost") {
        let host;
        if (process.env.WEBSITE_HOST === "healthcare-staging" ){
            host = "healthcare-staging.mydocsite.com" //oasis
        }else if(process.env.WEBSITE_HOST === "labs-staging"){
            host = "labs-staging.mydocsite.com" //Aspire
        } else {
            host = "hospital-chain-staging.mydocsite.com" //Aspire
        }
        return host
    } else {
        return  window.location.hostname
    }
}

export const getHostForClientComponent = () => {
    if (window.location.hostname === "localhost") {
        let host;
        if (process.env.WEBSITE_HOST === "healthcare-staging"){
            host = "healthcare-staging.mydocsite.com" //oasis
        }else if(process.env.WEBSITE_HOST === "labs-staging"){
            host = "labs-staging.mydocsite.com" //Aspire
        }else{
            host = "hospital-chain-staging.mydocsite.com" //Aspire
        }
        return host
    } else {
        return  window.location.hostname
    }
}