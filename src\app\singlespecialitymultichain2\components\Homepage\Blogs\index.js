import { Box, Container, Typography } from "@mui/material";
import BlogsClient from "../../BlogsClient";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import {
  getHomeComponentsData,
  getHomeSectionHeadings,
} from "@/api/harbor.service";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";

const fetchBlogs = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData(
      { domainName },
      HOME_WIDGET_TYPE.BLOGS
    );
    return data?.code === 200 ? data?.result?.blogs : [];
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return [];
  }
};

const getBlogsHeadings = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeSectionHeadings(
      { domainName: domainName },
      HOME_SECTION_HEADING_TYPE.BLOGS
    );
    if (data.code === 200) {
      return data?.result || [];
    } else return [];
  } catch (error) {
    console.error("Error fetching blogs data:", error);
    return [];
  }
};
export default async function BlogSection() {
  const blogs = await fetchBlogs();

  const headings = await getBlogsHeadings();

  const heading = headings[0]?.heading;
  //  const subHeading = headings[0]?.subHeading

  if (blogs.length === 0) {
    return <></>;
  }

  return (
    <Container maxWidth="lg" sx={{ py: "32px" }}>
      <Box sx={{ mb: 0, textAlign: "center" }}>
        <Typography
          variant="h4"
          align="center"
          fontWeight={400}
          sx={{
            mb: 2,
            fontSize: {
              xs: "24px",
              md: "36px",
            },
            color: "text.black",
          }}
        >
          {heading || "Latest Articles & Blogs"}
        </Typography>
      </Box>
      <BlogsClient blogs={blogs} />
    </Container>
  );
}
