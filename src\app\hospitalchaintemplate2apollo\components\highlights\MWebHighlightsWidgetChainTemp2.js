"use client";

import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import MedicationIcon from "@mui/icons-material/Medication";
import { getThumborUrl } from "@/app/utils/getThumborUrl";

const MWebHighlightsWidgetChainTemp2 = ({ highlights }) => {
  const router = useRouter();
  const slicedHighlights = highlights.slice(0, 8);
  const onhighlightItemClick = (type, url) => {
    if (type === 0) {
      // Internal redirect
      router.push(url);
    } else if (type === 1) {
      // External redirect, open in a new tab
      window.open(url, "_blank", "noopener,noreferrer");
    }
  };

  return (
    <Box
      sx={{
        display: "grid",
        boxShadow: "0 2px 20px rgb(0 0 0 / 5%)",
        padding: "8px 16px",
        borderRadius: "16px",
        zIndex: 1,
        background: "#fff",
        justifyContent: "row",
        gap: "16px",
        gridTemplateColumns: "repeat(3, 1fr)",
      }}
    >
      {slicedHighlights.map((highlightItem) => {
        const {
          code = null,
          title: displayName = "",
          imageUrl: iconUrl = "",
          highlightsRedirection = {},
        } = highlightItem || {};
        const { redirectionUrl: url, redirectionType: type } =
          highlightsRedirection;
        return (
          <Box
            id={`highlightsCard-${code}`}
            key={code}
            sx={{
              background: "#fff",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              marginTop: "8px",
              cursor: "pointer",
            }}
            onClick={() => onhighlightItemClick(type, url)}
          >
            {iconUrl ? (
              <Image
                alt="highlights"
                src={getThumborUrl(iconUrl, 32, 32)}
                height={32}
                width={32}
              />
            ) : (
              <MedicationIcon
                sx={{ fontSize: "48px", color: "primary.main" }}
              />
            )}
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                overflow: "hidden",
                maxWidth: "100px",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                color: "#333333",
                WebkitLineClamp: 2, // Limit to two lines
                WebkitBoxOrient: "vertical",
                marginTop: "8px",
                fontSize: "14px",
                textAlign: "center",
                whiteSpace: "normal", // Allow normal word wrapping
              }}
            >
              {displayName || ""}
            </Typography>
          </Box>
        );
      })}
    </Box>
  );
};

export default MWebHighlightsWidgetChainTemp2;
