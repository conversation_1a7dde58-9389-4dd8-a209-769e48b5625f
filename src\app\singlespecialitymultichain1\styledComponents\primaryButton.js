import Button from "@mui/material/Button";

const PrimaryButton = ({ children, ...props }) => {
  return (
    <Button
      color="primary"
      variant="contained"
      sx={{
        fontSize: "14px",
        fontWeight: 500,
        boxShadow: "none",
        textTransform: "none",
        color: "#fff"
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

export default PrimaryButton;
