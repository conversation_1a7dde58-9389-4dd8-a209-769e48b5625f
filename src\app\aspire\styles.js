import { makeStyles } from "@mui/styles";
import { alpha } from "@mui/material/styles"

const useStyles = makeStyles((theme) => ({
  sectionLayout: {
    padding: "32px 64px",
  },
  reviewsSectionContentBox: {
    padding: "8px",
    paddingBottom: "24px",
    border: `1px solid #ededed`,
    // boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
    backgroundColor: "#fff",
    borderRadius: "8px",
    position: "relative",
    "&:before": {
      content: "''",
      position: "absolute",
      height: "30px",
      width: "30px",
      border: `2px solid #ededed`,
      borderLeftColor: "transparent",
      borderTopColor: "transparent",
      boxSizing: "border-box",
      bottom: "-15px",
      left: "40px",
      transform: "rotate(45deg)",
      zIndex: 0,
      backgroundColor: "#fff",
    },
  },
  reviewsSectionReviewBox: {
    padding: "8px",
    minHeight: "10rem",
    maxHeight: "10rem",
    overflowY: "auto",
    // backgroundColor: THEME_WEBSITE_TERNARY_BG_COLOR,
  },
  reviewsSectionReviewBoxText: {
    // color: theme.palette.text.secondary,
    fontSize: "14px",
    marginTop: "8px",
  },
  reviewsSectionReviewTime: {
    // color: theme.palette.text.secondary,
    fontSize: "12px",
  },
  reviewsSectionCarousalBoxItem: {
    display: "flex",
    flexDirection: "column",
    gap: "2rem",
    transition: "all .3s",
    // "&:hover": {
    //   transform: "translate(10px, -10px)",
    // },
  },
  reviewsSectionContentBoxComma: {
    // content: "\201C",
    // fontSize: '4rem',
    alignSelf: "start",
  },
  reviewsSectionProfileBox: {
    display: "flex",
    gap: "16px",
    padding: "0 8px",
    alignItems: "center",
  },
  reviewsSectionProfileImg: {
    height: "48px",
    width: "48px",
    borderRadius: "100px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    fontSize: "1.5rem",
    backgroundColor: "#1097A7",
    color: "#ffffff",
    // [theme.breakpoints.down("sm")]: {
    //   height: "32px",
    //   width: "32px",
    //   fontSize: "1rem",
    // },
  },
  reviewsSectionProfileDetailsBox: {
    display: "flex",
    flexDirection: "column",
    gap: "4px",
  },
  reviewsSectionProfileName: {
    fontSize: "1.2rem",
    // [theme.breakpoints.down("sm")]: {
    //   fontSize: "1rem",
    // },
  },
  blogsSectionBlogItemType: {
    background: "#8f74a2",
    padding: "6px 10px",
    position: "absolute",
    bottom: "-15px",
    left: "-7px",
    minWidth: "130px",
    maxWidth: "90%",
    textAlign: "center",
    height: "31px",
    "&:before": {
      content: "''",
      position: "absolute",
      bottom: "-10px",
      width: 0,
      height: 0,
      left: 0,
      borderTop: "10px solid #1e1e36",
      borderLeft: "8px solid transparent",
    },
    "&:after": {
      content: "''",
      position: "absolute",
      right: "-10px",
      bottom: 0,
      width: 0,
      height: 0,
      borderLeft: "10px solid #8f74a2",
      borderTop: "16px solid transparent",
      borderBottom: "15px solid transparent",
    },
  },
}));

export default useStyles;
