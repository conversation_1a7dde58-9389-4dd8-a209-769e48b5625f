"use client";

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from "@mui/material";
import { alpha } from "@mui/material/styles";
import SectionLayoutAspire from "../styledComponents/SectionLayoutAspire";
import { useTheme } from "@emotion/react";
import Image from "next/image";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import SectionHeading from "./sectionHeading";

const FaqsSection = ({ faqs = [] }) => {
  const theme = useTheme();

  return (
    <SectionLayoutAspire>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
        <SectionHeading>Frequently Asked Questions</SectionHeading>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
            gap: "32px",
            alignItems: "center",
          }}
        >
          <Box
            sx={{
              height: "400px",
              width: "400px",
              display: { xs: "none", md: "flex" },
            }}
          >
            <Image alt="faqs" src="/faqs.svg" height={400} width={400} />
          </Box>
          <Box sx={{ display: "flex", flexDirection: "column", gap: "24px" }}>
            {faqs.map((faq, index) => {
              const { code = null, question = "", answer = "" } = faq || {};
              return (
                <Accordion
                  id={`faq${index}`}
                  key={code}
                  sx={{
                    boxShadow: `0 2px 20px ${alpha(
                      theme.palette.primary.main,
                      0.1
                    )}`,
                    "&:before": {
                      backgroundColor: "white",
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel2-content"
                    id="panel2-header"
                  >
                    <Typography>{question || ""}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography>{answer || ""}</Typography>
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </Box>
        </Box>
      </Box>
    </SectionLayoutAspire>
  );
};

export default FaqsSection;
