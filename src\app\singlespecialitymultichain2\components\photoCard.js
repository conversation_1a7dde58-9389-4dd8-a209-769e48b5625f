import { getThumborUrl } from "@/utils/getThumborUrl";
import { Box, Dialog } from "@mui/material";
import { useState } from "react";

const PhotoCard = ({ imageUrl = "", title = "", index }) => {
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Box
        key={index}
        sx={{
          borderRadius: "8px",
          overflow: "hidden",
          height: "400px",
          objectFit: "cover",
          objectPosition: "center",
          cursor: "pointer"
        }}
        onClick={handleClickOpen}
      >
        <img
          alt={title}
          src={getThumborUrl(imageUrl, 0, 0)}
          style={{ height: "100%", width: "100%", objectFit: "cover" }}
        />
      </Box>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{
          ".MuiDialog-paper": {
            maxHeight: "calc(100vh - 64px)",
            maxWidth: "calc(100vw - 64px)",
            padding: 0,
            borderRadius: "8px",
            overflow: "hidden",
          },
        }}
      >
        <img
          alt="slider1"
          src={getThumborUrl(imageUrl)}
          style={{
            maxHeight: "calc(100vh - 64px)",
            maxWidth: "calc(100vw - 64px)",
            objectFit: "contain",
            borderRadius: "8px",
            objectPosition: "center",
          }}
        />
      </Dialog>
    </>
  );
};

export default PhotoCard;
