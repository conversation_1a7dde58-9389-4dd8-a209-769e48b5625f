export const mapTestDataForCard = (test) => {
  if (!test) return null;

  const testsCount = test.totalTestsIncluded || test.labTestItems?.length || 1;

  return {
    id: test.code,
    title: test.name,
    description: test.shortDescription || test.alternativeNames,
    testsIncludedText: `${testsCount} ${testsCount === 1 ? 'Test' : 'Tests'} included`,
    originalPrice: test.originalPrice,
    discountedPrice: test.discountedPrice,
    discount: `${test.discountPercentage}% off`,
    iconUrl: test.iconUrl,
    seoSlug: test.seoSlug,
    totalTestsIncluded: testsCount,
    labTestItems: test.labTestItems,
    turnaroundTime: test.turnaroundTime,
    sampleType: test.sampleType,
    currency: test.currency
  };
};

export const mapPackageDataForCard = (packageData) => {
  if (!packageData) return null;

  const testsCount = packageData.totalTestsIncluded || packageData.labTests?.length || 1;

  return {
    id: packageData.code,
    title: packageData.name,
    description: packageData.shortDescription || packageData.alternativeNames,
    testsIncludedText: `${testsCount} ${testsCount === 1 ? 'Test' : 'Tests'} included`,
    originalPrice: packageData.originalPrice,
    discountedPrice: packageData.discountedPrice,
    discount: `${packageData.discountPercentage}% off`,
    iconUrl: packageData.iconUrl,
    seoSlug: packageData.seoSlug,
    totalTestsIncluded: testsCount,
    labTests: packageData.labTests,
    turnaroundTime: packageData.turnaroundTime,
    sampleType: packageData.sampleType,
    currency: packageData.currency
  };
};
