"use client";

import { useContext, useState } from "react";
import axios from "axios";
import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import { Box, CircularProgress, Typography } from "@mui/material";
import { MuiOtpInput } from "mui-one-time-password-input";
import AppointmentChange from "./appointmentChange";
import AppointmentSectionLayout from "./appointmentSectionLayout";
import { AppointmentSchedulerContext } from "./appointmentScheduler";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_OTP,
  API_ENDPOINT_VERIFY,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";

const VerifyOtp = ({ handleRequestOtp, isReqOtpLoading = false }) => {
  const { setViewSnackbarMain } = useContext(AppContext);
  const { phone, dialCode, handleComponentDisplay } = useContext(
    AppointmentSchedulerContext
  );
  const [otp, setOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleOtpChange = (newValue) => {
    setOtp(newValue);
  };

  const handleVerifyOtp = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_OTP}${API_ENDPOINT_VERIFY}`;
    const reqBody = { phone: `${dialCode}${phone}`, otp };
    try {
      const response = await axios.post(url, reqBody, {
        headers: {
          "Content-Type": "application/json",
          source: "website",
        },
      });
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        handleComponentDisplay(2);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppointmentSectionLayout>
      <AppointmentChange />
      <Box
        sx={{
          padding: "1rem",
          border: "1px solid #F5F5F5",
          borderRadius: "10px",
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          alignItems: "center",
          textAlign: "center",
        }}
      >
        <Box>
          <Typography align="center" variant="body1" sx={{ lineHeight: "1.5" }}>
            An OTP has been sent to this number
          </Typography>
          <Typography
            variant="subtitle1"
            align="center"
            sx={{ lineHeight: "1.5", fontSize: "20px", fontWeight: "500" }}
          >
            {phone || ""}
          </Typography>
        </Box>
        <MuiOtpInput
          length={6}
          value={otp}
          autoFocus
          onChange={handleOtpChange}
          // onComplete={handleVerifyOtp}
          gap={2}
          TextFieldsProps={{
            type: "tel",
          }}
        />
        <Typography
          variant="subtitle1"
          align="center"
          sx={{ marginTop: "16px", fontSize: "14px" }}
        >
          Didn’t receive the code?{" "}
          <Typography
            sx={{ color: "primary.main", cursor: "pointer" }}
            onClick={handleRequestOtp}
          >
            {isReqOtpLoading ? (
              <CircularProgress color="primary" />
            ) : (
              "Resend OTP"
            )}
          </Typography>
        </Typography>
      </Box>
      <PrimaryButton
        sx={{ borderRadius: "24px", width: "100%" }}
        onClick={handleVerifyOtp}
      >
        {isLoading ? (
          <CircularProgress size={24} style={{ color: "#fff" }} />
        ) : (
          "Verify Otp"
        )}
      </PrimaryButton>
    </AppointmentSectionLayout>
  );
};

export default VerifyOtp;
