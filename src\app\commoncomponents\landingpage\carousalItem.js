import * as React from "react";
import { Box, Dialog, IconButton, Typography, useTheme } from "@mui/material";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import CloseIcon from '@mui/icons-material/Close';
import { keyframes } from "@emotion/react";



// Gradient animation for the border
const gradientMove = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

const CarousalItem = ({ code, imgUrl, position = null, redirectionUrl = null, title = "" }) => {
  const [open, setOpen] = React.useState(false);
  const [loaded, setLoaded] = React.useState(false);
  const theme = useTheme();

  const handleClickOpen = () => {
    if (redirectionUrl) {
      window.open(redirectionUrl, "_blank");
    } else setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleImageLoad = () => {
    setLoaded(true);
  };

  return (
    <React.Fragment>
      <Box
        id={`carousalItem${code}Pos${position}`}
        key={code}
        onClick={handleClickOpen}
        sx={{
          cursor: "pointer",
          position: "relative",
          overflow: "hidden",
          borderRadius: "16px",
          height: "100%",
          transition: "all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1)",
          transform: loaded ? "translateY(0)" : "translateY(20px)",
          opacity: loaded ? 1 : 0,
          "&::after": {
            content: '""',
            position: "absolute",
            inset: 0,
            borderRadius: "10px",
            opacity: 0,
            transition: "opacity 0.4s ease",
          },
          "&:hover": {
            "&::after": {
              opacity: 1,
              animation: `${gradientMove} 3s ease infinite`,
            },

            "& img": {
              transform: "scale(1.08)",
            },
            "& .overlay": {
              opacity: 1,
            },
            "& .image-title": {
              opacity: 1,
              transform: "translateY(0)",
            }
          }
        }}
      >
        {/* {title && (
          <Typography
            className="image-title"
            variant="subtitle1"
            sx={{
              position: "absolute",
              bottom: 16,
              left: 16,
              right: 16,
              color: "white",
              fontWeight: 600,
              zIndex: 2,
              opacity: 0,
              transform: "translateY(10px)",
              transition: "all 0.4s ease",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            {title || "kb"}
          </Typography>
        )} */}

        <Box
          component="img"
          alt={`Carousel image ${code}`}
          src={getThumborUrl(imgUrl)}
          onLoad={handleImageLoad}
          sx={{
            height: "100%",
            width: "100%",
            objectFit: "cover",
            transition: "transform 0.7s cubic-bezier(0.165, 0.84, 0.44, 1)",
            display: "block",
          }}
          onError={(e) => {
            e.target.src = "/placeholder-image.jpg";
            setLoaded(true);
          }}
        />
      </Box>

      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="image-dialog"
        maxWidth="xl"
        sx={{
          ".MuiBackdrop-root": {
            backgroundColor: "rgba(0, 0, 0, 0.85)",
          },
          ".MuiDialog-paper": {
            maxHeight: "calc(100vh - 64px)",
            maxWidth: "calc(100vw - 64px)",
            padding: 0,
            borderRadius: "16px",
            overflow: "hidden",
            backgroundColor: "transparent",
            boxShadow: "0 20px 60px rgba(0, 0, 0, 0.4)",
          },
        }}
      >
        <Box sx={{ position: "relative" }}>
          <IconButton
            onClick={handleClose}
            aria-label="close"
            sx={{
              position: "absolute",
              top: 16,
              right: 16,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              color: "white",
              width: 40,
              height: 40,
              "&:hover": {
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                transform: "rotate(90deg)",
              },
              transition: "transform 0.3s ease, background-color 0.3s ease",
              zIndex: 1,
            }}
          >
            <CloseIcon />
          </IconButton>

          <Box
            component="img"
            alt={`Carousel image ${code} - enlarged`}
            src={getThumborUrl(imgUrl)}
            sx={{
              height: "100%",
              width: "100%",
              objectFit: "contain",
              objectPosition: "center",
            }}
            onError={(e) => {
              e.target.src = "/placeholder-image.jpg";
            }}
          />

          {title && (
            <Typography
              variant="h6"
              sx={{
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                color: "white",
                padding: "16px",
                textAlign: "center",
              }}
            >
              {title}
            </Typography>
          )}
        </Box>
      </Dialog>
    </React.Fragment>
  );
};

export default CarousalItem;

// import * as React from "react";
// import Button from "@mui/material/Button";
// import Dialog from "@mui/material/Dialog";
// import { getThumborUrl } from "@/app/utils/getThumborUrl";

// const CarousalItem = ({ code, imgUrl, position = null, redirectionUrl = null }) => {
//   const [open, setOpen] = React.useState(false);

//   const handleClickOpen = () => {
//     if (redirectionUrl) {
//       window.open(redirectionUrl, "_blank");
//     } else setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//   };

//   return (
//     <React.Fragment>
//       <div id={`carousalItem${code}Pos${position}`} key={code} onClick={handleClickOpen} style={{ cursor: "pointer" }}>
//         <div
//           style={{
//             display: "flex",
//             padding: "8px",
//             justifyContent: "center",
//             // height: "300px",
//           }}
//         >
//           <img
//             alt="slider1"
//             src={getThumborUrl(imgUrl)}
//             style={{
//               height: "auto",
//               width: "100%",
//               objectFit: "cover",
//               borderRadius: "8px",
//             }}
//           />
//         </div>
//       </div>
//       <Dialog
//         open={open}
//         onClose={handleClose}
//         aria-labelledby="alert-dialog-title"
//         aria-describedby="alert-dialog-description"
//         sx={{
//           ".MuiDialog-paper": {
//             maxHeight: "calc(100vh - 64px)",
//             maxWidth: "calc(100vw - 64px)",
//             padding: 0,
//             borderRadius: "8px",
//             overflow: "hidden"
//           },
//         }}
//       >
//         <img
//           alt="slider1"
//           src={getThumborUrl(imgUrl)}
//           style={{
//             height: "100%",
//             width: "100%",
//             objectFit: "contain",
//             borderRadius: "8px",
//             objectPosition: "center"
//           }}
//         />
//       </Dialog>
//     </React.Fragment>
//   );
// };

// export default CarousalItem;
