"use client";

import React, {useContext, useEffect, useState} from "react";
import {getPlugins} from "@/api/marketplace.service";
import dynamic from "next/dynamic";
import {AppContext} from "@/app/AppContextLayout";
const BubbleChat = dynamic(() =>
        import("flowise-embed-react").then(module => module.BubbleChat),
    { ssr: false }
);

function Chatbot({}) {
    const { websiteData } = useContext(AppContext);
    const {enterprise_code: enterpriseCode = null} = websiteData;
    const [isLoading, setIsLoading] = useState(false);
    const [chaBotInstalled, setChaBotInstalled] = useState(false);
    const [chatFlowId, setChatFlowId] = useState(null);
    const [apiHost, setApiHost] = useState(null);
    const [primaryColor, setPrimaryColor] = useState("#0a5687");
    const [secondaryColor, setSecondaryColor] = useState("#fec84b");
    const [welcomeMessage, setWelcomeMessage] = useState("Hi, how can I help you today?");
    const [icon, setIcon] = useState("https://cdn.docfyn.com/com.harbor/documents/2025/03/06/3d340b816bef743b0dc942d0d33ccbc5c7f6a634/robot.webp");
    const [tooltipMessage, setTooltipMessage] = useState("Hi There 👋!");
    // const response =  getPlugins(enterpriseCode)
    useEffect(() => {
        const fetchPlugins = async () => {
            if (!enterpriseCode) return;
            setIsLoading(true);
            const res = await getPlugins(enterpriseCode);
            const jsonRes = await res.json();
            const aibotConfig = jsonRes.result.aibot;
            if (aibotConfig) {
                aibotConfig.forEach(config => {
                    switch (config.configName) {
                        case "chatflowid":
                            setChatFlowId(config.configValue);
                            break;
                        case "apiHost":
                            setApiHost(config.configValue);
                            break;
                        case "primaryColor":
                            setPrimaryColor(config.configValue);
                            break;
                        case "secondaryColor":
                            setSecondaryColor(config.configValue);
                            break;
                        case "welcomeMessage":
                            setWelcomeMessage(config.configValue);
                            break;
                        case "iconUrl":
                            setIcon(config.configValue);
                            break;
                        case "tooltipMessage":
                            setTooltipMessage(config.configValue);
                            break;
                        default:
                            break; // Handle any unknown configNames if needed
                    }
                });
                setChaBotInstalled(true)
            } else {
                setChaBotInstalled(false)
            }
            setIsLoading(false)
        };

        fetchPlugins();
    }, [enterpriseCode]);

    // Conditional rendering: show nothing until data is fetched
    if (isLoading || !chaBotInstalled || !chatFlowId || !apiHost) {
        return null;
    }

    return (

        <BubbleChat
            chatflowid={chatFlowId}
            apiHost={apiHost}
            theme={{
                button: {
                    backgroundColor: primaryColor,
                    right: 30,
                    bottom: 60,
                    size: 48, // small | medium | large | number
                    dragAndDrop: true,
                    iconColor: "white",
                    customIconSrc: icon,
                },
                tooltip: {
                    showTooltip: true,
                    tooltipMessage: tooltipMessage,
                    tooltipBackgroundColor: 'black',
                    tooltipTextColor: 'white',
                    tooltipFontSize: 16,
                },
                chatWindow: {
                    showTitle: true,
                    title: '',
                    titleAvatarSrc: '',
                    welcomeMessage: welcomeMessage,
                    errorMessage: 'This is a custom error message',
                    backgroundColor: "#ffffff",
                    height: 600,
                    width: 400,
                    fontSize: 16,
                    poweredByTextColor: "#303235",
                    botMessage: {
                        backgroundColor: "#f7f8ff",
                        textColor: "#303235",
                        showAvatar: false,
                        avatarSrc: "https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/parroticon.png",
                    },
                    userMessage: {
                        backgroundColor: secondaryColor,
                        textColor: "#ffffff",
                        showAvatar: false,
                        avatarSrc: "https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/usericon.png",
                    },
                    textInput: {
                        placeholder: 'Type your question',
                        backgroundColor: '#ffffff',
                        textColor: '#303235',
                        sendButtonColor: primaryColor,
                        maxChars: 200,
                        maxCharsWarningMessage: 'You exceeded the characters limit. Please input less than 200 characters.',
                        autoFocus: true, // If not used, autofocus is disabled on mobile and enabled on desktop. true enables it on both, false disables it on both.
                    },
                    feedback: {
                        color: '#303235',
                    },
                    footer: {
                        textColor: '#303235',
                        text: 'Powered by',
                        company: 'Docfyn',
                        companyLink: 'https://www.docfyn.com',
                    }
                }
            }}
        />

    );
}

export default Chatbot
