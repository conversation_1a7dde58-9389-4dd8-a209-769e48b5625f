'use client';

import React from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  CircularProgress,
  Paper,
  Avatar,
  MenuItem,
  Fade
} from '@mui/material';
import PhoneIcon from '@mui/icons-material/Phone';
import { COUNTRIES, countryToFlag } from '@/constants';

const PhoneNumberStep = ({
  phone,
  dialCode,
  error,
  isLoading,
  handlePhoneChange,
  setDialCode,
  handleRequestOtp,
  theme,
  formatPhoneNumber
}) => {
  return (
    <Fade in={true} timeout={500}>
      <Box>
        {/* Phone Icon and Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Avatar
            sx={{
              width: 80,
              height: 80,
              bgcolor: `${theme.palette.primary.main}15`,
              color: theme.palette.primary.main,
              margin: '0 auto 16px',
              p: 2
            }}
          >
            <PhoneIcon sx={{ fontSize: 40 }} />
          </Avatar>
          
          <Typography variant="h5" sx={{ fontWeight: 700, mb: 1.5, color: 'text.black' }}>
            Verify Your Phone Number
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
            We'll send a verification code to your phone to confirm your identity
          </Typography>
        </Box>
        
        {/* Phone Input Form */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            borderRadius: 3,
            border: '1px solid',
            borderColor: 'divider',
            '&:hover': {
              borderColor: 'text.black'
            },
            bgcolor: 'background.paper',
            mb: 3
          }}
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: 'text.black' }}>
            Enter your mobile number
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
            {/* Country Code Dropdown */}
            <TextField
              id="otpVerificationCountryCode"
              labelId="otp-country-code-select-label"
              value={dialCode}
              size="small"
              select
              sx={{
                width: '110px',
                flexShrink: 0,
                borderRadius: "4px",
                background: "#fff",
                color: "text.black",
                '& .MuiInputBase-input': {
                  color: "text.black",
                  py: 1.5
                },
                '& .MuiSelect-select': {
                  color: "text.black"
                },
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'text.black'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2
                  }
                }
              }}
              onChange={(e) => setDialCode(e.target.value)}
            >
              {COUNTRIES.map((country) => {
                const {
                  dial_code: dialCode = "",
                  code = "IN",
                  value = "",
                } = country || {};
                return (
                  <MenuItem
                    key={code}
                    sx={{color:"text.black"}}
                    value={dialCode}
                  >
                    {`${countryToFlag(code)} ${value}`}
                  </MenuItem>
                );
              })}
            </TextField>
            
            {/* Phone Number Input */}
            <TextField
              fullWidth
              placeholder="10-digit mobile number"
              variant="outlined"
              value={phone}
              onChange={handlePhoneChange}
              error={!!error}
              helperText={error}
              autoFocus
              sx={{
                '& .MuiInputBase-input': {
                  color: 'text.black',
                  fontSize: '1.1rem',
                  fontWeight: 500,
                  letterSpacing: '0.5px',
                  py: 1.5
                },
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'text.black'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2
                  }
                }
              }}
              inputProps={{
                inputMode: 'numeric',
                pattern: '[0-9]*'
              }}
            />
          </Box>
          
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1.5, textAlign: 'center' }}>
            We'll only use this number for verification purposes
          </Typography>
        </Paper>
        
        {/* Submit Button */}
        <Button
          variant="contained"
          fullWidth
          onClick={handleRequestOtp}
          disabled={isLoading || phone.length !== 10}
          sx={{
            py: 1.5,
            borderRadius: 2,
            fontSize: '1rem',
            fontWeight: 600,
            textTransform: 'none',
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            },
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {isLoading ? (
            <CircularProgress size={24} color="inherit" />
          ) : (
            'Send Verification Code'
          )}
        </Button>
      </Box>
    </Fade>
  );
};

export default PhoneNumberStep;
