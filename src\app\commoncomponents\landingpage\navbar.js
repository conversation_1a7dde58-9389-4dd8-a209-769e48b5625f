"use client";

import { Box, But<PERSON>, Typography, Container, useTheme } from "@mui/material";
import PhoneIcon from "@mui/icons-material/Phone";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { formatPhoneNumber } from "@/app/utils/formatPhoneNumber";
import Image from "next/image";
import { useState, useEffect } from "react";
import CallUsButton from "@/app/aspire/components/callUsButton";

const Navbar = ({ details = {} }) => {
  const { logoUrl = "", phone = "" } = details || {};
  const theme = useTheme();
  const [scrolled, setScrolled] = useState(false);
  const [phoneHovered, setPhoneHovered] = useState(false);

  // Format phone number for display
  const formattedPhone = phone ? formatPhoneNumber(phone) : "";

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleCallUs = () => {
    if (phone) {
      window.open(`tel:${phone}`, "_blank");
    }
  };

  return (
    <Box
      sx={{
        position: 'sticky',
        top: 0,
        zIndex: 1100,
        backgroundColor: scrolled ? 'white' : 'rgba(255, 255, 255, 0.95)',
        transition: 'all 0.3s ease',
        backdropFilter: 'blur(10px)',
        borderBottom: scrolled ? 'none' : '1px solid rgba(0, 0, 0, 0.05)',
        boxShadow: scrolled ? '0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12)' : 'none',
      }}
    >
      <Box
        sx={{
          maxWidth: '1400px',
          mx: 'auto',
          px: { xs: 2, md: 4 },
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          py: { xs: 1, md: 1.5 },
        }}
      >
        {/* Logo */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            transition: 'transform 0.3s ease',
            '&:hover': {
              transform: 'scale(1.02)',
            }
          }}
        >
          <Image
            alt="logo"
            src={getThumborUrl(logoUrl)}
                height={scrolled ? 50 : 60}
                width={scrolled ? 150 : 180}
                style={{
                  objectFit: "contain",
                  transition: 'all 0.3s ease',
                }}
              />
            </Box>

            {/* Contact Buttons */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {/* Call Button - Desktop */}
              <Button
                variant="outlined"
                color="primary"
                sx={{
                  borderRadius: "50px",
                  alignItems: "center",
                  gap: "8px",
                  padding: "8px 24px",
                  display: { xs: "none", sm: "flex" },
                  borderWidth: "2px",
                  borderColor: phoneHovered ? theme.palette.primary.main : theme.palette.primary.light,
                  color: phoneHovered ? theme.palette.primary.main : theme.palette.primary.main,
                  backgroundColor: phoneHovered ? 'rgba(238, 66, 102, 0.05)' : 'transparent',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    backgroundColor: 'rgba(238, 66, 102, 0.05)',
                    transform: 'translateY(-2px)',
                  }
                }}
                onClick={handleCallUs}
                onMouseEnter={() => setPhoneHovered(true)}
                onMouseLeave={() => setPhoneHovered(false)}
              >
                <PhoneIcon sx={{ color: theme.palette.primary.main }} />
                {formattedPhone || phone || "Call Us"}
              </Button>
              {/* Call Button - Mobile */}
              <Box sx={{ display: { xs: "flex", sm: "none" } }}>
              <Box
        id="navbarCallUsBtn"
        onClick={handleCallUs}
        sx={{
        display: "flex",
        flexDirection: "column", // Stack children vertically
        alignItems: "center", // Center align items horizontally
            cursor: "pointer",
            color: "primary.main",
            transition: "all .3s",
            "&:hover": {
                transform: "scale(1.1)",
            },
    }}>

        <Box
            sx={{
                backgroundColor: "primary.main",
                mask: "url(/call_2.svg) no-repeat center / contain",
                height: "36px",
                width: "36px",
            }}
        />

        <Typography
            fontSize="12px"
            color="primary.main"
        >
            Call Us
        </Typography>

    </Box>
              </Box>
            </Box>
          </Box>
        </Box>
  );
};

export default Navbar;
