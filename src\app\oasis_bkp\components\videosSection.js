"use client";

import { Box, Typography } from "@mui/material";
import SectionLayout from "../styledComponents/SectionLayout";
import { useTheme } from "@emotion/react";
import SectionHeading from "./sectionHeading";
import PlayCircleIcon from "@mui/icons-material/PlayCircle";
import { useEffect, useState } from "react";
import { YouTubeEmbed } from "@next/third-parties/google";

const VideosSection = ({ multiMedia = [] }) => {
  const theme = useTheme();
  const [videos, setVideos] = useState([]);
  const [selectedVideo, setSelectedVideo] = useState(null);

  const handleVideoSelection = (videoUrl, isCurrentVideo) => {
    if (!isCurrentVideo) setSelectedVideo(videoUrl);
  };

  useEffect(() => {
    if (multiMedia.length > 0) {
      const videos = multiMedia.filter((media) => media.video_url) || [];
      const currentVideo = videos[0]?.video_url;
      setSelectedVideo(currentVideo);
      setVideos(videos);
    }
  }, [multiMedia]);

  if (videos.length === 0) return <></>;
  return (
    <SectionLayout>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
        <SectionHeading align="right">Health Education Videos</SectionHeading>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
            gap: "32px",
          }}
        >
          <Box sx={{ height: { xs: "300px", md: "400px" } }}>
            <YouTubeEmbed
              videoid={selectedVideo}
              params="controls=0"
              style={{
                backgroundSize: "contain",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "center",
                borderRadius: "16px",
              }}
            />
            {/* <VideoView selectedVideo={selectedVideo} /> */}
            {/* <iframe
              id={selectedVideo}
              height="100%"
              width="100%"
              src={`https://www.youtube.com/embed/${selectedVideo}`}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
              style={{
                backgroundSize: "contain",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "center",
                borderRadius: "16px",
              }}
            ></iframe> */}
          </Box>
          <Box
            sx={{
              maxHeight: { xs: "auto", md: "500px" },
              overflowY: "auto",
              display: "flex",
              flexDirection: "column",
              gap: "16px",
            }}
          >
            {videos.map((video, index) => {
              const { video_url: videoUrl = "", title = "" } = video || {};
              const isCurrentVideo = selectedVideo === videoUrl;
              return (
                <Box
                  id={`video${index}`}
                  key={index}
                  sx={{
                    display: "flex",
                    gap: "16px",
                    padding: "8px",
                    background: isCurrentVideo
                      ? "rgba(0,0,0,.025)"
                      : "transparent",
                  }}
                  onClick={() => handleVideoSelection(videoUrl, isCurrentVideo)}
                >
                  <Box
                    sx={{
                      height: "100px",
                      width: "150px",
                      objectFit: "cover",
                      borderRadius: "8px",
                      position: "relative",
                    }}
                  >
                    <YouTubeEmbed
                      videoid={videoUrl}
                      params="controls=0"
                      style={{
                        backgroundSize: "contain",
                        backgroundRepeat: "no-repeat",
                        backgroundPosition: "center",
                        borderRadius: "16px",
                      }}
                    />
                    {/* <iframe
                      height="100%"
                      width="100%"
                      src={`https://www.youtube.com/embed/${videoUrl}`}
                      title="YouTube video player"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      allowFullScreen
                      style={{
                        backgroundSize: "contain",
                        backgroundRepeat: "no-repeat",
                        backgroundPosition: "center",
                        pointerEvents: "none",
                        borderRadius: "8px",
                      }}
                    ></iframe> */}
                    {/*<Box*/}
                    {/*  sx={{*/}
                    {/*    position: "absolute",*/}
                    {/*    top: "50%",*/}
                    {/*    left: "50%",*/}
                    {/*    transform: "translate(-50%, -50%)",*/}
                    {/*  }}*/}
                    {/*>*/}
                    {/*  <PlayCircleIcon*/}
                    {/*    fontSize="large"*/}
                    {/*    sx={{ cursor: "pointer" }}*/}
                    {/*  />*/}
                    {/*</Box>*/}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: theme.palette.primary.main,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        display: "-webkit-box",
                        WebkitLineClamp: "2",
                        WebkitBoxOrient: "vertical",
                      }}
                    >
                      {title || ""}
                    </Typography>
                    {/* <Typography variant="subtitle1">
                      Treated by Dr Kartikay Dhingra
                    </Typography> */}
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Box>
      </Box>
    </SectionLayout>
  );
};

export default VideosSection;
