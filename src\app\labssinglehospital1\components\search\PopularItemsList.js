'use client'
import { Box, Typography, useTheme, useMediaQuery } from '@mui/material';
import PopularItemCard from './PopularItemCard';
import LoadingIndicator from './LoadingIndicator';
import NoResultsMessage from './NoResultsMessage';

const PopularItemsList = ({
  popularPackages,
  popularTests,
  isLoading,
  isItemInCart,
  onAddToCart,
  onRemoveFromCart,
  onNavigate
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  if (isLoading) {
    return <LoadingIndicator message="Loading popular items..." />;
  }

  if ((!popularPackages || popularPackages.length === 0) &&
      (!popularTests || popularTests.length === 0)) {
    return <NoResultsMessage message="No popular items available." />;
  }

  return (
    <Box sx={{
      maxHeight: 'calc(100vh - 150px)', // Max height to enable scrolling when needed
      overflowY: 'auto', // Auto scroll - only shows scrollbar when needed
      pr: { xs: 1, md: 2 },
      '&::-webkit-scrollbar': {
        width: '6px'
      },
      '&::-webkit-scrollbar-track': {
        backgroundColor: '#f1f1f1',
        borderRadius: '10px'
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: '#888',
        borderRadius: '10px',
        '&:hover': {
          backgroundColor: '#555'
        }
      }
    }}>
      {popularPackages && popularPackages.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant={isMobile ? "subtitle1" : "h6"} sx={{ mb: 2, color: 'text.black' }}>
            Popular Health Packages
          </Typography>
          {/* Only show first 3 packages */}
          {popularPackages.slice(0, 3).map((item) => (
            <PopularItemCard
              key={item.id}
              item={item}
              isPackage={true}
              isInCart={isItemInCart(item.id, 'package')}
              onAddToCart={onAddToCart}
              onRemoveFromCart={onRemoveFromCart}
              onNavigate={onNavigate}
            />
          ))}
        </Box>
      )}

      {popularTests && popularTests.length > 0 && (
        <Box>
          <Typography variant={isMobile ? "subtitle1" : "h6"} sx={{ mb: 2, color: 'text.black' }}>
            Popular Tests
          </Typography>
          {/* Only show first 3 tests */}
          {popularTests.slice(0, 3).map((item) => (
            <PopularItemCard
              key={item.id}
              item={item}
              isPackage={false}
              isInCart={isItemInCart(item.id, 'test')}
              onAddToCart={onAddToCart}
              onRemoveFromCart={onRemoveFromCart}
              onNavigate={onNavigate}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};

export default PopularItemsList;
