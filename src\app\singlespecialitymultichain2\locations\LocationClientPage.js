"use client";

import React from "react";
import Carousal from "../Carousal";
import Services from "../components/Homepage/Services";
import VideosClient from "../components/VideosClient";
import LocationBlogsWrapper from "./LocationBlogsWrapper";
import LocationReviewsWrapper from "./LocationReviewsWrapper";
import Footer from "../Footer";
import LocationFaqWrapper from "./LocationFaqWrapper";
import Gallery from "../components/Homepage/Gallery";

export default function LocationClientPage({
  websiteData,
  renderedSpecialities,
}) {
  const {
    banners = [],
    multiMedia = [],
    testimonials = [],
    websiteServices = [],
    blogs = [],
    faqs = [],
  } = websiteData || {};
  const images = multiMedia?.filter((item) => item.image_url);
  const videos = multiMedia?.filter((item) => item.video_url);
  const headingGallery = "Our Gallery";
  const headingVideos = "Health Related Videos";

  return (<>
      <Carousal banners={banners} />
      <Services services={websiteServices} />
      {renderedSpecialities}
      {multiMedia.length > 0 && (
        <Gallery images={images} heading={headingGallery} />
      )}
      {multiMedia.length > 0 && (
        <VideosClient videos={videos} heading={headingVideos} />
      )}
      {blogs.length > 0 && <LocationBlogsWrapper blogs={blogs} />}
      {testimonials.length > 0 && (
        <LocationReviewsWrapper testimonials={testimonials} />
      )}
      {faqs.length > 0 && <LocationFaqWrapper faqs={faqs} />}
      <Footer websiteData={websiteData} />
      </>
  );
}
