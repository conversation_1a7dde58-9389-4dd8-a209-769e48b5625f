"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { alpha, useTheme } from "@mui/material/styles";
import { Card, CardContent, Typography, Box, Button } from "@mui/material";
import { ArrowForward } from "@mui/icons-material";
import PrimaryButton from "../styledComponents/PrimaryButton"

const ProcedureCard = ({ icon, title, description,ProcedureCode, seoSlug, specialitySlug }) => {
  const theme = useTheme();
  const router = useRouter();

  // const handleReadMore = () => {
  //   router.push(`/procedure-details/${title.toLowerCase().replace(/\s+/g, '-')}`);
  // };
  const handleProcedureClick = (e) => {
    e.preventDefault();
    router.push(`/specialities/${specialitySlug}/procedures/${seoSlug || ProcedureCode}`);
  };

  return (
    <Box
    onClick={handleProcedureClick}
      sx={{
        width: 350,
        minHeight:330,
        cursor:"pointer",
        borderRadius: "20px",
        bgcolor:"white",
        overflow: "hidden",
        transition: "transform 0.3s ease",
        // border:"1px solid black",
        border: "1px solid #eee",
        boxShadow:'0 4px 8px rgba(0,0,0,0.05)',
        // boxShadow:"0px 1px 5px black",
        "&:hover": {
          transform: "scale(1.03)",
        },
      }}
    >
      {/* Image Section */}
      <Box sx={{ width: "100%", height: 200, position: "relative", bgcolor:"white" }}>
        <Image
          src={icon || "/default-procedure.jpg"}
          fill={true}
          style={{ objectFit: "cover" }}
          sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 50vw"
          alt={title}
        />
      </Box>

      {/* Card Content */}
      <Box sx={{ textAlign: "center", p: 0.5, bgcolor:"white" }}>
        {/* Title */}
        <Typography
        aria-label="Title"
          variant="h6"
          component="h3"
          sx={{  color: "#1a1a1a", mb: 1 }}
        >
          {title}
        </Typography>

        {/* Description */}
        <Typography
        aria-label="Description"
          variant="body2"
          color="rgb(75, 75, 75)"
          sx={{
            mb: 2,
            display: "-webkit-box",
            WebkitLineClamp: 3,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
          }}
        >
          {description}
        </Typography>

        {/* Read More Button */}
        {/* <Button
        aria-label="Read More"
          onClick={handleReadMore}
          endIcon={<ArrowForward />}
          sx={{
            color: theme.palette.primary.main,
            fontWeight: 600,
            "&:hover": {
              backgroundColor: alpha(theme.palette.primary.main, 0.05),
            },
          }}
        >
          Read More
        </Button> */}
          {/* <PrimaryButton>
            Read More <ArrowForward/>
          </PrimaryButton> */}
      </Box>
    </Box>
  );
};

export default ProcedureCard;