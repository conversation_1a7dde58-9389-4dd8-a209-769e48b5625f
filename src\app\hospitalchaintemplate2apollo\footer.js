"use client";

import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Image from "next/image";
import Link from "next/link";
import FmdGoodIcon from "@mui/icons-material/FmdGood";
import PhoneIcon from "@mui/icons-material/Phone";
import MailIcon from "@mui/icons-material/Mail";
import InstagramIcon from "@mui/icons-material/Instagram";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import XIcon from "@mui/icons-material/X";
import YouTubeIcon from "@mui/icons-material/YouTube";
import { useContext } from "react";
import { AppContext } from "../AppContextLayout";
import { getThumborUrl } from "../utils/getThumborUrl";
import {alpha} from "@mui/material";
import {useTheme} from "@emotion/react";
import PoweredByText from "../commoncomponents/poweredByText";

import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";

export const SOCIAL_LINKS = {
  1: {
    icon: FacebookOutlinedIcon,
  },
  2: {
    icon: InstagramIcon,
  },
  3: {
    icon: XIcon,
  },
  4: {
    icon: LinkedInIcon,
  },
  5: {
    icon: YouTubeIcon,
  },
};

const Footer = () => {
  const { websiteData } = useContext(AppContext);
  const theme = useTheme();
  const {
    //phoneNumbers = [],
    emails = [],
    addresses = [],
    socialMediaLinks = [],
    logo_url: logoUrl = "",
    centers = []
  } = websiteData || {};
  const phoneNumbers = [{phone: "+91 7604019943", label: "Whatsapp"}, {phone: "033-40365500", label: "Call for Appointment"}]
  return (
    <Box sx={{ marginBottom: { xs: "56px", md: 0 } }}>
      <Box
        sx={{
          bgcolor: alpha(theme.palette.primary.main, 0.8),
          padding: { xs: "16px 16px", sm:"24px 16px",  md: "32px 80px", lg: "48px 80px" },
          display: "grid",
          gridTemplateColumns: { sm: "1fr", md: "1fr 200px 1.5fr" },
          gap: "32px",
        }}
      >
        <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>
            {logoUrl ? (
                <Image
                    alt="logo"
                    src={getThumborUrl(logoUrl)}
                    style={{
                        backgroundColor: "#fff"
                    }}
                    width={210}
                    height={70}
                />
            ) : <></>}
          <Box sx={{ display: "flex", flexDirection: "column", gap: "16px" }}>
            {emails.map((item, index) => {
              const { email = "" } = item || {};
              return (
                <Box
                  id={`footerMail${index}`}
                  key={index}
                  sx={{ display: "flex", gap: "8px", cursor: "pointer" }}
                  onClick={() => window.open(`mailto:${email}`)}
                >
                  <MailIcon sx={{ color: "#fff" }} fontSize="small" />
                  <Typography
                    variant="body1"
                    sx={{ color: "#fff", fontSize: "14px" }}
                  >
                    {email || ""}
                  </Typography>
                </Box>
              );
            })}
            {phoneNumbers.map((item, index) => {
              const { phone = "", label = "" } = item || {};
              return (
                <Link id={`footerPhone${index}`} href={`tel:${phone}`}>
                  <Box
                    key={index}
                    sx={{ display: "flex", gap: "8px", cursor: "pointer" }}
                  >
                    <PhoneIcon sx={{ color: "#fff" }} fontSize="small" />
                    <Typography
                      variant="body1"
                      sx={{ color: "#fff", fontSize: "14px" }}
                    >
                      {phone || ""}{" - "} {label}
                    </Typography>
                  </Box>
                </Link>
              );
            })}
              {addresses.map((address, index) => {
                  const {
                      line1 = "",
                      line2 = "",
                      city = "",
                      country = "",
                      map_url = ""
                  } = address || {};

                  // Construct the address string
                  const addressString = [line1, line2, city, country]
                      .filter(Boolean) // Removes any falsy values (like null, undefined, or empty strings)
                      .join(", "); // Joins the remaining parts with a comma and space
                  const mapUrl = map_url
                      ? map_url
                      : `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(addressString)}`;

                  return (
                      <Box key={index} sx={{ display: "flex", gap: "8px" }}>
                          <FmdGoodIcon sx={{ color: "#fff" }} fontSize="small" />
                          {mapUrl ? ( // Render link only if map_url exists
                              <Typography
                                  variant="body1"
                                  sx={{ color: "#fff", fontSize: "14px", cursor: "pointer" }}
                                  component="a" // Use <a> element for link
                                  href={mapUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                              >
                                  {addressString}
                              </Typography>
                          ) : (
                              <Typography
                                  variant="body1"
                                  sx={{ color: "#fff", fontSize: "14px", cursor: "default" }}
                              >
                                  {addressString}
                              </Typography>
                          )}
                      </Box>
                  );
              })}

          </Box>

            <Box
                sx={{
                    display: "flex",
                    gap: "16px",
                    color: "#fff",
                    fontSize: "14px",
                }}
            >
                {socialMediaLinks.map((item, index) => {
                    const { link = "", type = null } = item || {};
                    const Icon = SOCIAL_LINKS[type]?.icon;
                    if (!link) return <></>;
                    return (
                        <Box
                            id={`footerSocialMediaLink${type}`}
                            key={index}
                            sx={{
                                height: "36px",
                                width: "36px",
                                borderRadius: "100%",
                                background: "#fff",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                cursor: "pointer",
                            }}
                            onClick={() => window.open(link, "_blank")}
                        >
                            <Icon sx={{ color: "#000" }} />
                        </Box>
                    );
                })}
            </Box>
        </Box>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "24px" }}>
          <Typography variant="h6" sx={{ color: "#fff" }}>
            Quick Links
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "8px",
              color: "#fff",
            }}
          >
            <Link id="footerFindADoctor" href="/doctors">
                <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                }}>
                    <KeyboardArrowRightIcon />
                    <Typography
                        fontSize="14px"
                    >
                        Find a doctor
                    </Typography>
                </div>
            </Link>
            <Link id="footerAboutUs" href="/about-us">
                <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                }}>
                    <KeyboardArrowRightIcon />

                    <Typography
                        fontSize="14px"
                    >
                        About us
                    </Typography>
                </div>
            </Link>
            <Link id="footerBlogs" href="/blogs">
                <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                }}>
                    <KeyboardArrowRightIcon />
                    <Typography
                        fontSize="14px"
                    >
                        Blogs
                    </Typography>
                </div>
            </Link>
            <Link id="footerSpecialities" href="/specialities">
                <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                }}>
                    <KeyboardArrowRightIcon />
                    <Typography
                        fontSize="14px"
                    >
                        Specialities
                    </Typography>
                </div>
            </Link>
            <Link id="footerContactUs" href="/contact-us">
                <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "16px",
                }}>
                    <KeyboardArrowRightIcon />
                    <Typography
                        fontSize="14px"
                    >
                        Contact us
                    </Typography>
                </div>
            </Link>

              <Link id="footerPrivacyPolicy" href="/privacy-policy">
                  <div style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "16px",
                  }}>
                      <KeyboardArrowRightIcon />
                      <Typography
                          fontSize="14px"
                      >
                          Privacy Policy

                      </Typography>
                  </div>
              </Link>
              <Link id="footerDisclaimer" href="/disclaimer">
                  <div style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "16px",
                  }}>
                      <KeyboardArrowRightIcon />
                      <Typography
                          fontSize="14px"
                      >
                          Disclaimer

                      </Typography>
                  </div>
              </Link>
              <Link id="footerTnC" href="/terms-and-conditions">
                  <div style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "16px",
                  }}>
                      <KeyboardArrowRightIcon />
                      <Typography
                          fontSize="14px"
                      >
                          Terms and Conditions

                      </Typography>
                  </div>
              </Link>
          </Box>
        </Box>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
            {/*{socialMediaLinks.length > 0 &&*/}
            {/*    <Box sx={{ display: "flex", flexDirection: "column", gap: "32px" }}>*/}
            {/*        <Typography variant="h6" sx={{ color: "#fff" }}>*/}
            {/*            Stay in touch*/}
            {/*        </Typography>*/}
            {/*        <Box*/}
            {/*            sx={{*/}
            {/*                display: "flex",*/}
            {/*                gap: "16px",*/}
            {/*                color: "#fff",*/}
            {/*                fontSize: "14px",*/}
            {/*            }}*/}
            {/*        >*/}
            {/*            {socialMediaLinks.map((item, index) => {*/}
            {/*                const { link = "", type = null } = item || {};*/}
            {/*                const Icon = SOCIAL_LINKS[type]?.icon;*/}
            {/*                if (!link) return <></>;*/}
            {/*                return (*/}
            {/*                    <Box*/}
            {/*                        id={`footerSocialMediaLink${type}`}*/}
            {/*                        key={index}*/}
            {/*                        sx={{*/}
            {/*                            height: "36px",*/}
            {/*                            width: "36px",*/}
            {/*                            borderRadius: "100%",*/}
            {/*                            background: "#fff",*/}
            {/*                            display: "flex",*/}
            {/*                            justifyContent: "center",*/}
            {/*                            alignItems: "center",*/}
            {/*                            cursor: "pointer",*/}
            {/*                        }}*/}
            {/*                        onClick={() => window.open(link, "_blank")}*/}
            {/*                    >*/}
            {/*                        <Icon sx={{ color: "#000" }} />*/}
            {/*                    </Box>*/}
            {/*                );*/}
            {/*            })}*/}
            {/*        </Box>*/}
            {/*    </Box>}*/}
          <Box sx={{ display: "flex", flexDirection: "column", gap: "24px" }}>
            <Typography variant="h6" sx={{ color: "#fff" }}>
              Locations
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: "8px",
                color: "#fff",
              }}
            >
              {centers.map((center) => {
                const { code = null, name = "", phone = "", domain_slug: slugTitle, area } = center || {};
                const {name:areaName} = area || {}
                return (
                  <Link key={code} id="footerFindADoctor" href={`/locations/${slugTitle}`}>

                      <Typography
                          fontSize="14px"
                      >
                          {`${name || ""} - ${areaName || ""}`}
                      </Typography>
                  </Link>
                );
              })}
            </Box>
          </Box>
        </Box>
      </Box>
        <PoweredByText />
    </Box>
  );
};

export default Footer;
