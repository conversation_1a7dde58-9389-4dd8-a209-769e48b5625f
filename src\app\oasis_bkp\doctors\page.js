"use client";

import Box from "@mui/material/Box";
import SectionLayout from "../styledComponents/SectionLayout";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";
import {
  FormControl,
  InputLabel,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
} from "@mui/material";
import Pagination from "@mui/material/Pagination";
import DoctorsList from "./doctorsList";
import FaqsSection from "../components/faqsSection";
import { useContext, useEffect, useState } from "react";
import Image from "next/image";
import axios from "axios";
import { AppContext } from "../../AppContextLayout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_ENDPOINT_DOCTORS,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SEARCH,
} from "@/constants";
import { useRouter } from "next/navigation";

export default function Doctors() {
  const router = useRouter();
  const { setViewSnackbarMain, websiteData } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const [doctors, setDoctors] = useState([]);
  const [specialities, setSpecialities] = useState([]);
  const [selectedSpeciality, setSelectedSpeciality] = useState();
  const [searchInput, setSearchInput] = useState("");
  const [filteredDoctors, setFilteredDoctors] = useState([]);
  const [filteredBySpecialityDoctors, setFilteredBySpecialityDoctors] =
    useState([]);

  const handleSearchInput = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    const searchedDoctors = doctors.filter((doctor) => {
      const { doctorDetails = {} } = doctor || {};
      const { name: doctorName = "" } = doctorDetails || {};
      return doctorName.toLowerCase().includes(value.toLowerCase());
    });
    setFilteredDoctors(searchedDoctors);
  };

  const getDoctors = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_ENDPOINT_DOCTORS}?enterpriseCode=${enterpriseCode}`;
    try {
      const response = await axios.get(url);
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { doctors = [] } = result || {};
        setDoctors(doctors);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  const getSearchData = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${API_ENDPOINT_SEARCH}?enterpriseCode=${enterpriseCode}`;
    try {
      const response = await axios.get(url);
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { data: searchData = [] } = result || {};
        const { doctors = [], specialities = [] } = searchData || {};
        // setDoctors(doctors);
        setSpecialities(specialities);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  const handleDoctorRedirection = (code) => {
    router.push(`/doctors/${code}`);
  };

  const handleOnBlur = (e) => {
    setTimeout(() => {
      setFilteredDoctors([]);
    }, 200);
  };

  const handleSpecialityChange = (event) => {
    const value = event.target.value;
    if (value === "all") setFilteredBySpecialityDoctors(doctors);
    else {
      const filteredBySpecialities = doctors.filter((doctor) => {
        const { doctorDetails = {} } = doctor || {};
        const { medicalSpecialities = [] } = doctorDetails || {};
        // let doctorWithSpeciality = null;
        for (let i = 0; i < medicalSpecialities.length; i++) {
          const { code = null } = medicalSpecialities[i];
          if (code === value) {
            return doctor;
          }
        }
      });
      setFilteredBySpecialityDoctors(filteredBySpecialities);
    }
    setSelectedSpeciality(value);
  };

  useEffect(() => {
    if (enterpriseCode) {
      getDoctors();
      getSearchData();
    }
  }, [enterpriseCode]);

  useEffect(() => {
    setFilteredBySpecialityDoctors(doctors);
  }, [doctors]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayout>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
          <Box
            sx={{
              display: "flex",
              alignItems: { xs: "start", md: "center" },
              // justifyContent: "space-between",
              gap: { xs: "24px", md: "32px" },
              flexDirection: { xs: "column", md: "row" },
            }}
          >
            <Box
              sx={{
                position: "relative",
                zIndex: 1,
                width: { xs: "100%", sm: "fit-content" },
              }}
              onBlur={handleOnBlur}
            >
              <Box
                sx={{
                  backgroundColor: "#fff",
                  border: "1px solid rgba(0, 0, 0, 0.23)",
                  padding: "11px 12px",
                  borderRadius: "4px",
                  display: "flex",
                  alignItems: "center",
                  gap: "16px",
                  width: { xs: "100%", sm: "fit-content" },
                }}
              >
                <InputBase
                  id="doctorSearch"
                  placeholder="Search doctor"
                  value={searchInput}
                  onChange={handleSearchInput}
                  sx={{
                    width: {
                      xs: "100%",
                      sm: "300px",
                      ".MuiInputBase-input::placeholder": { color: "rgba(0, 0, 0, 0.6)" },
                    },
                  }}
                />
                <SearchIcon />
              </Box>
              {filteredDoctors.length > 0 && (
                <Box
                  sx={{
                    padding: "0 4px",
                    background: "#fff",
                    position: "absolute",
                    width: "100%",
                    maxHeight: "400px",
                    overflowY: "auto",
                    boxShadow: "0 2px 20px rgba(0, 0, 0, .1)",
                  }}
                >
                  <Box>
                    <List
                      subheader={
                        <ListSubheader
                          component="div"
                          id="nested-list-subheader"
                        >
                          Doctors
                        </ListSubheader>
                      }
                    >
                      {filteredDoctors.map((doctor, index) => {
                        const { doctorDetails = {} } = doctor || {};
                        const {
                          name: doctorName = "",
                          code = null,
                          seoSlug = "",
                        } = doctorDetails || {};
                        return (
                          <ListItemButton
                            id={`doctor${index}`}
                            key={code}
                            onClick={() => handleDoctorRedirection(seoSlug)}
                          >
                            <ListItemIcon sx={{ minWidth: "32px", mr: 2 }}>
                              <Image
                                alt="doctor"
                                src="/doctor-male.svg"
                                height={32}
                                width={32}
                              />
                            </ListItemIcon>
                            <ListItemText primary={doctorName || ""} />
                          </ListItemButton>
                        );
                      })}
                    </List>
                  </Box>
                </Box>
              )}
            </Box>
            <FormControl
              sx={{
                width: { xs: "100%", sm: "366px" },
              }}
            >
              <InputLabel id="demo-simple-select-label">
                Filter By Speciality
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={selectedSpeciality}
                label="Filter By Speciality"
                onChange={handleSpecialityChange}
                // input={<SpecialityFilterInput />}
              >
                <MenuItem value={"all"}>All</MenuItem>
                {specialities.map((speciality) => {
                  const { code: specialityCode = null, name = "" } =
                    speciality || {};
                  return (
                    <MenuItem value={specialityCode}>{name || ""}</MenuItem>
                  );
                })}
              </Select>
            </FormControl>
            {/* <FormControl>
              <Select
                displayEmpty
                // input={<InputBase variant="outlined" />}
                value="0"
                inputProps={{ "aria-label": "Without label" }}
              >
                <MenuItem disabled value="0">
                  Filter by speciality
                </MenuItem>
                <MenuItem value={"Cardiology"}>Cardiology</MenuItem>
              </Select>
            </FormControl> */}
          </Box>
          <DoctorsList doctors={filteredBySpecialityDoctors} />
          {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Pagination count={10} variant="outlined" shape="rounded" />
          </Box> */}
        </Box>
      </SectionLayout>
      {/* <FaqsSection /> */}
    </Box>
  );
}
