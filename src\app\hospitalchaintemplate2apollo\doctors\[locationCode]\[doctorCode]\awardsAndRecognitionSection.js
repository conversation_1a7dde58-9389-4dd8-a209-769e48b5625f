import { Box, Typography } from "@mui/material";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";

const AwardsAndRecognitionSection = ({awardsAndRecognitions = []}) => {
  return (
    awardsAndRecognitions.length > 0 && (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          fontFamily: "inherit",
          color: "inherit",
        }}
      >
        <Typography variant="h5">
          <Box
            sx={{
              display: "inline-block",
              "&::after": {
                content: "''",
                width: "50%",
                borderBottom: `3px solid`,
                borderColor: "primary.main",
                display: "block",
                marginBottom: "-1px",
                left: "50%",
                right: "50%",
              },
            }}
          >
            Awards and Recognition
          </Box>
        </Typography>
        {awardsAndRecognitions.map((details, index) => {
          const { description = "", year = "" } = details || {};
          return (
            <Box
              key={index}
              sx={{ display: "flex", alignItems: "start", gap: "8px" }}
            >
              <CheckCircleOutlineOutlinedIcon sx={{ color: "#373A40" }} />
              <Typography
                variant="subtitle1"
                sx={{
                  lineHeight: "1.25",
                  fontWeight: 300,
                  fontSize: "15px",
                }}
              >{`${description || ""}${year ? `, ${year}` : ""}`}</Typography>
            </Box>
          );
        })}
      </Box>
    )
  );
};

export default AwardsAndRecognitionSection;
