'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Divider,
  Paper
} from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import TimerIcon from '@mui/icons-material/Timer';
import EmailIcon from '@mui/icons-material/Email';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { useDispatch, useSelector } from 'react-redux';
import { addToCart, removeFromCart, selectIsItemInCart } from '../redux/slices/cartSlice';

const TestDetailHeader = ({ testData }) => {
  const dispatch = useDispatch();
  const [expanded, setExpanded] = useState(false);
  const [showReadMore, setShowReadMore] = useState(false);
  const descriptionRef = useRef(null);

  // Check if this item is already in the cart
  const isInCart = useSelector(selectIsItemInCart(testData.id, testData.itemType || 'test'));

  // Set a default value for showReadMore
  useEffect(() => {
    // Get the description text
    const descriptionText = testData.subtitle || testData.shortDescription || testData.alternativeNames || '';

    // Estimate if the text is long enough to need a "Read More" button
    // A rough estimate: if text is longer than 200 characters, it likely exceeds 5 lines
    const isLongText = descriptionText.length > 200;

    // Set the state to show the "Read More" button
    setShowReadMore(isLongText);

    // Force expanded to false when the description changes
    if (!expanded) {
      setExpanded(false);
    }
  }, [testData.subtitle, testData.shortDescription, testData.alternativeNames]);

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  const handleRemoveFromCart = () => {
    dispatch(removeFromCart({ id: testData.id, itemType: testData.itemType || 'test' }));
  };

  const handleAddToCart = () => {
    dispatch(addToCart({
      id: testData.id,
      title: testData.title,
      discountedPrice: testData.discountedPrice,
      originalPrice: testData.originalPrice,
      discount: testData.discount,
      icon: '🔬', // Default icon
      testsIncludedText: testData.testsIncludedText || `${testData.totalTests} Tests included`,
      description: testData.description || testData.shortDescription || (typeof testData.alternativeNames === 'string' ? testData.alternativeNames : ''),
      itemType: testData.itemType || 'test' // Use the itemType from the data if available
    }));
  };

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: 3,
        borderRadius: '12px',
        border: '1px solid #e0e0e0',
        position: { xs: 'static', md: 'sticky' },
        top: { md: 150 },
        zIndex: 10,
        maxHeight: { md: 'calc(100vh - 32px)' },
        overflowY: { md: 'auto' },
        backgroundColor: 'white',
        '&::-webkit-scrollbar': {
          width: '4px'
        },
        '&::-webkit-scrollbar-track': {
          backgroundColor: '#f1f1f1',
          borderRadius: '4px'
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: '#888',
          borderRadius: '4px',
          '&:hover': {
            backgroundColor: '#555'
          }
        }
      }}
    >
      <Typography variant="h5" component="h1" fontWeight="bold" gutterBottom sx={{color:"text.black"}}>
        {testData.title}
      </Typography>

      <Box sx={{ mb: 2, position: 'relative' }}>
        <Box sx={{ position: 'relative' }}>
          <Typography
            ref={descriptionRef}
            variant="body2"
            color="text.secondary"
            sx={{
              overflow: expanded ? 'visible' : 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: expanded ? 'unset' : 5,
              WebkitBoxOrient: 'vertical',
              mb: showReadMore ? 1 : 0
            }}
          >
            {testData.subtitle || testData.shortDescription || testData.alternativeNames || 'No detailed description available for this test.'}
          </Typography>

          {/* Add a gradient fade effect at the bottom when collapsed */}
          {!expanded && showReadMore && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: '24px',
              }}
            />
          )}
        </Box>

        {showReadMore && (
          <Button
            onClick={toggleExpand}
            endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            sx={{
              textTransform: 'none',
              color: 'primary.main',
              fontWeight: 'medium',
              p: 0,
              minWidth: 'auto',
              display: 'flex',
              mt: 0.5,
              '&:hover': {
                backgroundColor: 'transparent',
              }
            }}
            disableRipple
          >
            {expanded ? 'Read Less' : 'Read More'}
          </Button>
        )}
      </Box>

      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, flexWrap: 'wrap', gap: { xs: 1.5, sm: 3 }, mb: 3,color:"text.black" }}>
        {testData.nextSlot && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AccessTimeIcon color="primary" fontSize="small" />
            <Typography variant="body2">
              Next Slot: {testData.nextSlot}
            </Typography>
          </Box>
        )}

        {(testData.reportHours || testData.turnaroundTime) && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AccessTimeIcon color="primary" fontSize="small" />
            <Typography variant="body2">
              {testData.reportHours || testData.turnaroundTime}
            </Typography>
          </Box>
        )}

        {testData.testDuration && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TimerIcon color="primary" fontSize="small" />
            <Typography variant="body2">
              Test Duration: {testData.testDuration}
            </Typography>
          </Box>
        )}

        {testData.reportDeliveryMethod && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EmailIcon color="primary" fontSize="small" />
            <Typography variant="body2">
              Report Delivery: {testData.reportDeliveryMethod}
            </Typography>
          </Box>
        )}

        {testData.preparation && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CheckCircleOutlineIcon color="success" fontSize="small" />
            <Typography variant="body2">
              {testData.preparation}
            </Typography>
          </Box>
        )}
      </Box>

      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'column', md: 'row' },
        flexWrap: 'wrap',
        gap: { xs: 1.5, sm: 2, md: 3 },
        mb: 3,
        p: 2,
        bgcolor: 'rgba(0, 0, 0, 0.02)',
        borderRadius: '8px'
      }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: { xs: 0.5, sm: 1 },
          minWidth: { xs: '100%', md: 'auto' }
        }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'medium', minWidth: { sm: '90px' } }}>
            Sample Type:
          </Typography>
          <Typography variant="body2" fontWeight="medium" sx={{ wordBreak: 'break-word', color: 'text.black' }}>
            {testData.sampleType || 'Not specified'}
          </Typography>
        </Box>

        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: { xs: 0.5, sm: 1 },
          minWidth: { xs: '100%', md: 'auto' }
        }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'medium', minWidth: { sm: '90px' } }}>
            Gender:
          </Typography>
          <Typography variant="body2" fontWeight="medium" sx={{ wordBreak: 'break-word', color: 'text.black' }}>
            {testData.applicableGender || testData.gender || 'All'}
          </Typography>
        </Box>

        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: { xs: 0.5, sm: 1 },
          minWidth: { xs: '100%', md: 'auto' }
        }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'medium', minWidth: { sm: '90px' } }}>
            Age Group:
          </Typography>
          <Typography variant="body2" fontWeight="medium" sx={{ wordBreak: 'break-word', color: 'text.black' }}>
            {testData.targetAgeGroup || testData.ageGroup || 'All ages'}
          </Typography>
        </Box>
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        alignItems: { xs: 'flex-start', sm: 'center' },
        justifyContent: 'space-between',
        mb: 2,
        gap: { xs: 2, sm: 0 }
      }}>
        <Box>
          {/* If discount percentage is null or 0, show only original price */}
          {(!testData.discountPercentage || testData.discountPercentage <= 0) ? (
            <Typography variant="h5" component="span" fontWeight="bold" sx={{ color: 'text.black' }}>
              {testData.currency?.symbol || '₹'}{testData.originalPrice}
            </Typography>
          ) : (
            // Otherwise show discounted price with strikethrough original price
            <>
              <Typography variant="h5" component="span" fontWeight="bold" sx={{ color: 'text.black' }}>
                {testData.currency?.symbol || '₹'}{testData.discountedPrice}
              </Typography>
              <Typography
                variant="body1"
                component="span"
                sx={{
                  textDecoration: 'line-through',
                  color: 'text.secondary',
                  ml: 1
                }}
              >
                {testData.currency?.symbol || '₹'}{testData.originalPrice}
              </Typography>
              <Typography
                variant="body2"
                component="span"
                sx={{
                  color: 'success.main',
                  ml: 1,
                  fontWeight: 'medium'
                }}
              >
                {testData.discount}
              </Typography>
            </>
          )}
        </Box>

        {isInCart ? (
          <Button
            variant="outlined"
            color="error"
            onClick={handleRemoveFromCart}
            sx={{
              borderRadius: '8px',
              px: { xs: 3, sm: 4 },
              py: 1,
              textTransform: 'none',
              fontWeight: 'medium',
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            <Typography sx={{ fontWeight: 500 }}>
              Remove
            </Typography>
          </Button>
        ) : (
          <Button
            variant="contained"
            color="primary"
            onClick={handleAddToCart}
            sx={{
              borderRadius: '8px',
              px: { xs: 3, sm: 4 },
              py: 1,
              textTransform: 'none',
              fontWeight: 'medium',
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            <Typography sx={{ fontWeight: 500, color: 'text.primary' }}>
              Add to Cart
            </Typography>
          </Button>
        )}
      </Box>
    </Paper>
  );
};

export default TestDetailHeader;
