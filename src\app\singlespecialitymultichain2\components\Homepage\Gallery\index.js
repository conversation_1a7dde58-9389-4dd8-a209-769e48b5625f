"use client";
import { Box, Typography, Container } from "@mui/material";
import GallerySlider from "../../GallerySlider";
import GalleryFilters from "../../GalleryFilters";
import { useState, useEffect } from "react";

export default function Gallery({ images, tags = [], heading }) {
  const [filteredImages, setFilteredImages] = useState([]);
  const [selectedTag, setSelectedTag] = useState(null);

  useEffect(() => {
    if (selectedTag) {
      const filtered = images.filter((image) =>
        image.tags.some((tag) => tag.name === selectedTag)
      );
      setFilteredImages(filtered);
    } else {
      setFilteredImages(images);
    }
  }, [selectedTag, images]);

  return (
    <Container maxWidth="lg" sx={{ py: "32px" }}>
      <Typography
        variant="h4"
        align="center"
        fontWeight={400}
        sx={{
          mb: 2,
          fontSize: {
            xs: "24px",
            md: "36px",
          },
          color: "text.black",
        }}
      >
        {heading}
      </Typography>

      <Box sx={{ mb: 3 }}>
        {tags && tags.length > 0 && (
          <GalleryFilters
            tags={tags}
            onTagSelect={(tag) => setSelectedTag(tag)}
          />
        )}
      </Box>

      <Box sx={{ mt: 4 }}>
        <GallerySlider photos={filteredImages} />
      </Box>
    </Container>
  );
}
