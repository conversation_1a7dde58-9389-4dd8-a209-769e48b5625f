import { Box, Typography } from "@mui/material";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";

const EducationAndTrainingSection = ({ educationDetails = [] }) => {
  return (
    educationDetails.length > 0 && (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          fontFamily: "inherit",
          color: "inherit",
        }}
      >
        <Typography variant="h5">
          <Box
            sx={{
              display: "inline-block",
              "&::after": {
                content: "''",
                width: "50%",
                borderBottom: `3px solid`,
                borderColor: "primary.main",
                display: "block",
                marginBottom: "-1px",
                left: "50%",
                right: "50%",
              },
            }}
          >
            Education and Training
          </Box>
        </Typography>
        {educationDetails.map((details) => {
          const {
            passoutYear = "",
            degree = {},
            institute = {},
          } = details || {};
          const { name: degreeName = "", code: degreeCode = "" } = degree || {};
          const {
            institutionName = "",
            state = "",
            country = "",
          } = institute || {};
          return (
            <Box
              key={degreeCode}
              sx={{ display: "flex", alignItems: "start", gap: "8px" }}
            >
              <CheckCircleOutlineOutlinedIcon sx={{ color: "#373A40" }} />
              <Typography
                variant="subtitle1"
                sx={{
                  lineHeight: "1.25",
                  fontWeight: 300,
                  fontSize: "15px",
                }}
              >{`${degreeName || ""}${institutionName ? `, ${institutionName}` : ""}${state ? `, ${state}` : ""}${country ? `, ${country}` : ""}${passoutYear ? `, ${passoutYear}` : ""}`}</Typography>
            </Box>
          );
        })}
      </Box>
    )
  );
};

export default EducationAndTrainingSection;
