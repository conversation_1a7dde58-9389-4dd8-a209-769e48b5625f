import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import Script from 'next/script';

const TestStructureDataScript = ({ testData, itemType }) => {

    // Determine schema type based on item type
    const schemaType = itemType === 'package' ? 'MedicalTestPanel' : 'MedicalTest';

    // Create base structured data
    const structuredData = {
        "@context": "https://schema.org",
        "@type": schemaType,
        "name": testData.name,
        "description": testData.description || testData.shortDescription || testData.alternativeNames,
        "url": `https://${getWebsiteHost()}/tests/${testData.seoSlug}`
    };

    // Add preparation instructions (official Schema.org property for medical procedures)
    if (testData.preparationInstructions) {
        structuredData.preparation = testData.preparationInstructions;
    }

    // Add specimen requirement and turnaround time to description (Schema.org compliant approach)
    let enhancedDescription = structuredData.description || "";

    if (testData.sampleType) {
        enhancedDescription += enhancedDescription ? ` Sample required: ${testData.sampleType}.` : `Sample required: ${testData.sampleType}.`;
    }

    if (testData.turnaroundTime) {
        enhancedDescription += enhancedDescription ? ` Results available in ${testData.turnaroundTime}.` : `Results available in ${testData.turnaroundTime}.`;
    }

    if (testData.testDuration) {
        enhancedDescription += enhancedDescription ? ` Test duration: ${testData.testDuration}.` : `Test duration: ${testData.testDuration}.`;
    }

    if (testData.reportDeliveryMethod) {
        enhancedDescription += enhancedDescription ? ` Report delivery: ${testData.reportDeliveryMethod}.` : `Report delivery: ${testData.reportDeliveryMethod}.`;
    }

    if (enhancedDescription !== structuredData.description) {
        structuredData.description = enhancedDescription;
    }

    // Add target audience using official Schema.org MedicalAudience
    if (testData.applicableGender || testData.targetAgeGroup) {
        structuredData.audience = {
            "@type": "MedicalAudience"
        };

        if (testData.applicableGender) {
            structuredData.audience.suggestedGender = testData.applicableGender;
        }

        if (testData.targetAgeGroup) {
            structuredData.audience.suggestedAge = testData.targetAgeGroup;
        }
    }

    // Add pricing information to description (offers property not supported on MedicalTest)
    if (testData.discountedPrice || testData.originalPrice) {
        let enhancedDescription = structuredData.description || "";

        if (testData.discountedPrice) {
            enhancedDescription += enhancedDescription ? ` Price: ₹${testData.discountedPrice}.` : `Price: ₹${testData.discountedPrice}.`;

            // If there's also an original price, show the discount
            if (testData.originalPrice && testData.originalPrice !== testData.discountedPrice) {
                enhancedDescription += ` (Original price: ₹${testData.originalPrice}).`;
            }
        } else if (testData.originalPrice) {
            enhancedDescription += enhancedDescription ? ` Price: ₹${testData.originalPrice}.` : `Price: ₹${testData.originalPrice}.`;
        }

        structuredData.description = enhancedDescription;
    }

    // Package-specific fields
    if (itemType === 'package') {
        // Add number of tests to description (Schema.org compliant approach)
        if (testData.totalTestsIncluded) {
            let packageDescription = structuredData.description || "";
            packageDescription += packageDescription ? ` Includes ${testData.totalTestsIncluded} tests.` : `Includes ${testData.totalTestsIncluded} tests.`;
            structuredData.description = packageDescription;
        }

        // Add individual tests as subTest array (official Schema.org property)
        if (testData.labTests && testData.labTests.length > 0) {
            structuredData.subTest = testData.labTests.map(test => {
                let testDescription = test.shortDescription || test.alternativeNames || "";

                // Add test-specific details to description
                if (test.sampleType) {
                    testDescription += testDescription ? ` Sample: ${test.sampleType}.` : `Sample: ${test.sampleType}.`;
                }

                if (test.turnaroundTime) {
                    testDescription += testDescription ? ` Results in ${test.turnaroundTime}.` : `Results in ${test.turnaroundTime}.`;
                }

                return {
                    "@type": "MedicalTest",
                    "name": test.name,
                    "description": testDescription
                };
            });
        }
    }

    // Test-specific fields (test duration and delivery method already handled in enhanced description)
    if (itemType === 'test') {
        // Add individual test items to description if available
        if (testData.labTestItems && testData.labTestItems.length > 0) {
            let itemsDescription = structuredData.description || "";
            const itemNames = testData.labTestItems.map(item => item.name).join(", ");
            itemsDescription += itemsDescription ? ` Test components: ${itemNames}.` : `Test components: ${itemNames}.`;
            structuredData.description = itemsDescription;
        }
    }

    // Add additional type for better categorization (official Schema.org approach)
    structuredData.additionalType = "https://schema.org/DiagnosticProcedure";

    return (
        <Script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
    );
};

export default TestStructureDataScript;
