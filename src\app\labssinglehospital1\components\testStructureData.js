import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import Script from 'next/script';

const TestStructureDataScript = ({ testData, itemType, faqs = [] }) => {

    // Use Product schema type for better rich search results generation
    // Both individual tests and test packages are treated as products for SEO purposes
    const schemaType = 'Product';

    // Create base structured data using Product schema
    const structuredData = {
        "@context": "https://schema.org",
        "@type": schemaType,
        "name": testData.name,
        "description": testData.shortDescription || testData.alternativeNames,
        "url": `https://${getWebsiteHost()}/tests/${testData.seoSlug}`,
        "category": itemType === 'package' ? 'Medical Test Package' : 'Medical Test',
        "brand": {
            "@type": "Brand",
            "name": "Laboratory Services"
        }
    };

    // Add product image - use a default lab test image or icon if available
    if (testData.iconUrl) {
        structuredData.image = testData.iconUrl;
    } else {
        // Use a default lab test image
        structuredData.image = `https://${getWebsiteHost()}/images/lab-category.png`;
    }

    // Add preparation instructions as additional property
    if (testData.preparationInstructions) {
        structuredData.additionalProperty = structuredData.additionalProperty || [];
        structuredData.additionalProperty.push({
            "@type": "PropertyValue",
            "name": "Preparation Instructions",
            "value": testData.preparationInstructions
        });
    }

    // Build enhanced description with medical test specific information
    let enhancedDescription = structuredData.description || "";

    if (testData.testDuration) {
        enhancedDescription += enhancedDescription ? ` Test duration: ${testData.testDuration}.` : `Test duration: ${testData.testDuration}.`;
    }

    if (testData.reportDeliveryMethod) {
        enhancedDescription += enhancedDescription ? ` Report delivery: ${testData.reportDeliveryMethod}.` : `Report delivery: ${testData.reportDeliveryMethod}.`;
    }

    if (enhancedDescription !== structuredData.description) {
        structuredData.description = enhancedDescription;
    }

    // Add target audience as additional properties for medical tests
    if (testData.applicableGender || testData.targetAgeGroup) {
        structuredData.additionalProperty = structuredData.additionalProperty || [];

        if (testData.applicableGender) {
            structuredData.additionalProperty.push({
                "@type": "PropertyValue",
                "name": "Applicable Gender",
                "value": testData.applicableGender
            });
        }

        if (testData.targetAgeGroup) {
            structuredData.additionalProperty.push({
                "@type": "PropertyValue",
                "name": "Target Age Group",
                "value": testData.targetAgeGroup
            });
        }
    }

    // Add pricing information using Product schema offers (proper for rich snippets)
    if (testData.discountedPrice || testData.originalPrice) {
        const currentPrice = testData.discountedPrice || testData.originalPrice;
        const currency = testData.currency || "INR";

        structuredData.offers = {
            "@type": "Offer",
            "price": currentPrice,
            "priceCurrency": currency,
            "availability": "https://schema.org/InStock",
            "itemCondition": "https://schema.org/NewCondition",
            "url": `https://${getWebsiteHost()}/tests/${testData.seoSlug}`
        };

        // Add original price if there's a discount
        if (testData.originalPrice && testData.discountedPrice &&
            testData.originalPrice !== testData.discountedPrice) {
            structuredData.offers.priceSpecification = {
                "@type": "PriceSpecification",
                "price": testData.originalPrice,
                "priceCurrency": currency,
                "name": "Original Price"
            };
        }
    }

    // Package-specific fields using Product schema
    if (itemType === 'package') {
        // Add number of tests as additional property
        if (testData.totalTestsIncluded) {
            structuredData.additionalProperty = structuredData.additionalProperty || [];
            structuredData.additionalProperty.push({
                "@type": "PropertyValue",
                "name": "Total Tests Included",
                "value": testData.totalTestsIncluded
            });

            // Also add to description for better SEO
            let packageDescription = structuredData.description || "";
            packageDescription += packageDescription ? ` Includes ${testData.totalTestsIncluded} tests.` : `Includes ${testData.totalTestsIncluded} tests.`;
            structuredData.description = packageDescription;
        }

        // Add individual tests as included products (Product schema approach)
        if (testData.labTests && testData.labTests.length > 0) {
            structuredData.isRelatedTo = testData.labTests.map(test => {
                let testDescription = test.shortDescription || test.alternativeNames || "";

                // Add test-specific details to description
                if (test.sampleType) {
                    testDescription += testDescription ? ` Sample: ${test.sampleType}.` : `Sample: ${test.sampleType}.`;
                }

                if (test.turnaroundTime) {
                    testDescription += testDescription ? ` Results in ${test.turnaroundTime}.` : `Results in ${test.turnaroundTime}.`;
                }

                return {
                    "@type": "Product",
                    "name": test.name,
                    "description": testDescription,
                    "category": "Medical Test"
                };
            });
        }
    }

    // Test-specific fields using Product schema properties
    if (itemType === 'test') {
        // Add individual test items as additional properties if available
        if (testData.labTestItems && testData.labTestItems.length > 0) {
            structuredData.additionalProperty = structuredData.additionalProperty || [];
            structuredData.additionalProperty.push({
                "@type": "PropertyValue",
                "name": "Test Components",
                "value": testData.labTestItems.map(item => item.name).join(", ")
            });

            // Also add to description for better SEO
            let itemsDescription = structuredData.description || "";
            const itemNames = testData.labTestItems.map(item => item.name).join(", ");
            itemsDescription += itemsDescription ? ` Test components: ${itemNames}.` : `Test components: ${itemNames}.`;
            structuredData.description = itemsDescription;
        }
    }

    // Add sample type as additional property if available
    if (testData.sampleType) {
        structuredData.additionalProperty.push({
            "@type": "PropertyValue",
            "name": "Sample Type",
            "value": testData.sampleType
        });
    }

    // Add turnaround time as additional property if available
    if (testData.turnaroundTime) {
        structuredData.additionalProperty.push({
            "@type": "PropertyValue",
            "name": "Turnaround Time",
            "value": testData.turnaroundTime
        });
    }

    // Add additional type for better categorization while maintaining medical context
    structuredData.additionalType = "https://schema.org/MedicalTest";

    // Create breadcrumb structured data
    const breadcrumbStructuredData = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": `https://${getWebsiteHost()}/`
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Tests",
                "item": `https://${getWebsiteHost()}/tests`
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": testData.name,
                "item": `https://${getWebsiteHost()}/tests/${testData.seoSlug}`
            }
        ]
    };

    // Create FAQ structured data if FAQs are available
    let faqStructuredData = null;
    if (faqs && faqs.length > 0) {
        faqStructuredData = {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqs.map(faq => ({
                "@type": "Question",
                "name": faq.question,
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": faq.answer
                }
            }))
        };
    }

    return (
        <>
            {/* Product structured data */}
            <Script
                type="application/ld+json"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
            />

            {/* Breadcrumb structured data */}
            <Script
                type="application/ld+json"
                dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbStructuredData) }}
            />

            {/* FAQ structured data */}
            {faqStructuredData && (
                <Script
                    type="application/ld+json"
                    dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
                />
            )}
        </>
    );
};

export default TestStructureDataScript;
