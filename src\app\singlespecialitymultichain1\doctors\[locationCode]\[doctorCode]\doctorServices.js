"use client";

import { Box, Typography } from "@mui/material";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import { useEffect, useState } from "react";

const DoctorServices = ({ enterpriseSpecialities = [] }) => {
  const [services, setServices] = useState([]);
  const [viewAllServices, setViewAllServices] = useState(false);

  useEffect(() => {
    if (enterpriseSpecialities?.length) {
      const specialitiesServices = enterpriseSpecialities.map(
        (specialities) => {
          const { services = [] } = specialities || {};
          return services.map((service) => service);
        }
      );
      const flattenedServices = specialitiesServices.flat();
      setServices(flattenedServices);
    }
  }, [enterpriseSpecialities]);

  return (
    services.length > 0 && (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "24px",
          fontFamily: "inherit",
          color: "inherit",
        }}
      >
        <Typography variant="h5">
          <Box
            sx={{
              display: "inline-block",
              "&::after": {
                content: "''",
                width: "50%",
                borderBottom: `3px solid`,
                borderColor: "primary.main",
                display: "block",
                marginBottom: "-1px",
                left: "50%",
                right: "50%",
              },
            }}
          >
            Services
          </Box>
        </Typography>
        {services
          .slice(0, viewAllServices ? services.length : 6)
          .map((service) => {
            const { code = null, name: serviceName = "" } = service || {};
            return (
              <Box
                key={code}
                sx={{
                  display: "flex",
                  alignItems: "start",
                  gap: "8px",
                }}
              >
                <CheckCircleOutlineOutlinedIcon sx={{ color: "#373A40" }} />
                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      lineHeight: "1.25",
                      fontWeight: 300,
                      fontSize: "15px",
                    }}
                  >
                    {serviceName || ""}
                  </Typography>
                </Box>
              </Box>
            );
          })}
        {services.length > 6 && !viewAllServices ? (
          <Typography
            variant="subtitle2"
            sx={{ cursor: "pointer", textDecoration: "underline" }}
            onClick={() => setViewAllServices(true)}
          >
            View All
          </Typography>
        ) : (
          viewAllServices && (
            <Typography
              variant="subtitle2"
              sx={{ cursor: "pointer", textDecoration: "underline" }}
              onClick={() => setViewAllServices(false)}
            >
              View Less
            </Typography>
          )
        )}
      </Box>
    )
  );
};

export default DoctorServices;
