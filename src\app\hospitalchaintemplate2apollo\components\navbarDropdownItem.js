import { useState } from "react";
import Box from "@mui/material/Box";
import HealthAndSafetyOutlinedIcon from "@mui/icons-material/HealthAndSafetyOutlined";
import KeyboardArrowDownOutlinedIcon from "@mui/icons-material/KeyboardArrowDownOutlined";
import { useRouter } from "next/navigation";
import {Typography} from "@mui/material";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import SecondNavbarDropdownItem from "@/app/hospitalchaintemplate2apollo/components/navbarSecondDropdownItem";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import Image from "next/image";

const NavbarDropdownItem = ({ section = {}, setAnchorEl, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const {
    displayName = "",
    sections = [],
    redirection = {},
    iconUrl = "",
    type = 1,
  } = section || {};

  const handleClick = () => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setIsOpen((prev) => !prev);
  };

  return (
    <Box>
      <Box
        // sx={{
        //   display: "flex",
        //   alignItems: "center",
        //   gap: "8px",
        //   justifyContent: "space-between",
        //   fontSize: "14px",
        //   cursor: "pointer",
        //   transition: "all .3s",
        //   "&:hover": { color: "primary.main" }
        // }}
          sx={{
              display: "flex",
              alignItems: "center",
              fontSize: "14px",
              gap: "8px",
              cursor: "pointer",
              padding: "8px 16px", // Equal padding from top/bottom and left/right
              justifyContent: "space-between", // Align content with equal spacing
              borderBottom: "1px solid #e0e0e0", // Add a divider
              "&:hover": {
                  backgroundColor: "#f5f5f5", // Light grey hover effect
                  color: "secondary.main", // Change text color on hover
              },
          }}
        onClick={handleClick}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
            {
             iconUrl ?
                 <Image
                     alt="logo"
                     src={getThumborUrl(iconUrl)}
                     width={20}
                     height={20}
                 />
                 : <KeyboardArrowRightIcon
                 sx={{ color: "#333",}}
             />
            }
          {" "}
            <Typography
                fontSize="14px"
            >
                {displayName || ""}
            </Typography>
        </Box>
        {sections !== null && <KeyboardArrowDownOutlinedIcon />}
      </Box>
      {sections !== null &&
        isOpen &&
        sections.map((section, index) => {
          const { displayName = "" } = section || {};
          return (
            <SecondNavbarDropdownItem
              key={`${displayName}${index}`}
              section={section}
              setAnchorEl={setAnchorEl}
            />
          );
        })}
    </Box>
  );
};

export default NavbarDropdownItem;
