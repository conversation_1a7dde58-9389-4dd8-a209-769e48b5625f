
import { getHomeComponentsData, getHomeSectionHeadings } from "@/api/harbor.service";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import Gallery from "./Homepage/Gallery";
import { HOME_SECTION_HEADING_TYPE, HOME_WIDGET_TYPE } from "@/constants";

const getMultimediaData = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeComponentsData(
      { domainName: domainName },
      HOME_WIDGET_TYPE.MULTIMEDIA
    );

    if (data.code === 200) {
      const multimedia = data?.result?.multiMedia || [];
      const aggregatedTags = data?.result?.aggregatedTags || [];
      const images = multimedia?.filter((item) => item.image_url);

      return {
        images,
        aggregatedTags
      };
    }
  } catch (error) {
    console.error("Error fetching multimedia data:", error);
  }
  return { images: [], aggregatedTags: [] };
};

const getGalleryHeadings = async () => {
  try {
    const domainName = getWebsiteHost();
    const data = await getHomeSectionHeadings(
      { domainName: domainName },
      HOME_SECTION_HEADING_TYPE.PHOTOS
    );
    if (data.code === 200) {
      return data?.result || [];
    } else return [];
  } catch (error) {
    console.error("Error fetching photos data:", error);
    return [];
  }
};

export default async function GalleryWrapper() {
  const { images } = await getMultimediaData();
  const headings = await getGalleryHeadings();

  const allTags = images?.reduce((tags, image) => {
    image?.tags?.forEach((tag) => {
      if (!tags.includes(tag.name)) {
        tags.push(tag.name);
      }
    });
    return tags || [];
  }, []);

  return (
    <Gallery
      images={images}
      // aggregatedTags={aggregatedTags}
      tags={allTags} 
      heading={headings[0]?.heading || "Our Gallery"}
    />
  );
}
