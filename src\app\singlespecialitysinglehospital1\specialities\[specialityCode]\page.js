"use client";

import FaqsSection from "@/app/oasis/components/faqsSection";
import { <PERSON><PERSON><PERSON>, PrevArrow } from "@/app/oasis/components/photosSection";
// import DoctorCard from "@/app/oasis/doctors/doctorCard";
import { alpha, Box, Button, Typography, useMediaQuery } from "@mui/material";
import Slider from "react-slick";
// import ProcedureCard from "./procedureCard";
import { useContext, useEffect, useState } from "react";
import { AppContext } from "@/app/AppContextLayout";
import parse from "html-react-parser";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
// import BlogCard from "@/app/oasis/components/blogCard";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import ReviewsSection from "@/app/commoncomponents/reviewsSection";
import SectionLayoutSingleSpecialitySingleHospital from "../../styledComponents/SectionLayoutSingleSpecialitySingleHospital";
import DoctorCard from "../../components/DoctorCard";
import ProcedureCard from "../../components/procedureCard";
import BlogCard from "../../components/blogCard";
// import BlogCard from "../../components/blogCard";
import RelatedLinks from "@/app/singlespecialitysinglehospital1/components/RelatedLinks";

const SpecialityDetail = ({ params }) => {
  const router = useRouter();
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const theme = useTheme();
  const [speciality, setSpeciality] = useState({});
  const { specialityCode = null } = params || {};
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const {
    displayName: specialityName = "",
    shortDescription = "",
    description = "",
    bannerUrl = "",
    procedures = [],
    doctors = [],
    blogs = [],
    faqs = [],
    medicalSpecialityCode = "",
    relatedLinks = [],
  } = speciality || {};
  // console.log(speciality.blogs);

  const doctorsCarousalSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const proceduresCarousalSettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const getSpecialityDetails = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?code=${specialityCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { specialities = [] } = result || {};
        const { seoSlug = "" } = specialities[0] || {};
        if (specialityCode !== seoSlug) {
          router.replace(`/specialities/${seoSlug}`);
        }
        setSpeciality(specialities[0] || {});
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    getSpecialityDetails();
  }, []);

  // Preparing data for related links
  const formattedRelatedLinks = relatedLinks
    ? relatedLinks.map((link) => ({
        name: link.displayName || "",
        slug: link.seoSlug || "",
      }))
    : [];

  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        paddingTop: isMobile ? "0px" : "0px",
      }}
    >
      <Box
        sx={{
          position: "relative",
          minHeight: "280px",
          bgcolor: alpha(theme.palette.primary.main, 0.7),
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <SectionLayoutSingleSpecialitySingleHospital>
          <Box
            sx={{
              // position: "absolute",
              // left: "50%",
              // top: "50%",
              // transform: "translate(-50%, -50%)",
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              alignItems: "center",
            }}
          >
            <Box>
              <Typography
                variant="h3"
                align="center"
                sx={{ color: "#fff", fontSize: "40px" }}
              >
                {specialityName || ""}
              </Typography>
              <Typography
                variant="subtitle1"
                align="center"
                sx={{ color: "#fff", fontWeight: 300, mt: 2 }}
              >
                {shortDescription || ""}
              </Typography>
            </Box>
            {doctors.length > 0 && (
              <Link
                id={`speciality-${specialityCode}-findADoctor`}
                href={`/specialities/${specialityCode}#doctors`}
              >
                <Button
                  // color="primary"
                  sx={{
                    background: "#fff",
                    boxShadow: "none",
                    textTransform: "none",
                    color: "primary.main",
                    fontWeight: 600,
                    "&:hover": {
                      background: "#fff",
                      color: "primary.main",
                    },
                  }}
                >
                  Find a doctor
                </Button>
              </Link>
            )}
          </Box>
        </SectionLayoutSingleSpecialitySingleHospital>
      </Box>
      <SectionLayoutSingleSpecialitySingleHospital>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "64px" }}>
          <div
            className="ck-content"
            dangerouslySetInnerHTML={{ __html: description }} // Render HTML safely
          />
          {doctors.length > 0 &&
            (doctors.length > 3 ? (
              <Box
                id="doctors"
                sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
              >
                <Typography variant="h5">Our Team of Experts</Typography>
                <Slider {...doctorsCarousalSettings}>
                  {doctors.map((doctorDetails, index) => {
                    const { code = null } = doctorDetails || {};
                    return (
                      <Box
                        id={`speciality-${specialityCode}-doctor${index}Box`}
                        key={code}
                        sx={{ padding: "8px 24px" }}
                      >
                        {/* <DoctorCard id={`speciality-${specialityCode}-doctor${index}`} doctorDetails={doctorDetails} /> */}
                        <DoctorCard
                          cardsVisible={true}
                          name={doctorDetails.name}
                          specialization={
                            doctorDetails.medicalSpecialities
                              .map((speciality) => speciality.name)
                              .join(", ") || doctorDetails.description
                          }
                          imageUrl={doctorDetails.profilePicture || ""}
                          experience={
                            doctorDetails.additionalDetails.workExperience
                          }
                          consultationFee={
                            doctorDetails.additionalDetails.consultationFee
                          }
                          doctorCode={doctorDetails.code}
                          seoSlug={doctorDetails.seoSlug}
                        />
                      </Box>
                    );
                  })}
                </Slider>
              </Box>
            ) : (
              <Box
                id="doctors"
                sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
              >
                <Typography variant="h5">Our Team of Experts</Typography>
                <Box
                  sx={{
                    display: "grid",
                    gridTemplateColumns: {
                      xs: "1fr",
                      sm: "1fr 1fr",
                      lg: "repeat(4, 1fr)",
                    },
                  }}
                >
                  {doctors.map((doctorDetails, index) => {
                    const { code = null } = doctorDetails || {};
                    return (
                      <Box
                        id={`speciality-${specialityCode}-doctor${index}Box`}
                        key={code}
                        sx={{ padding: "8px 24px" }}
                      >
                        <DoctorCard
                          cardsVisible={true}
                          name={doctorDetails.name}
                          specialization={
                            doctorDetails.medicalSpecialities
                              .map((speciality) => speciality.name)
                              .join(", ") || doctorDetails.description
                          }
                          imageUrl={doctorDetails.profilePicture || ""}
                          experience={
                            doctorDetails.additionalDetails.workExperience
                          }
                          consultationFee={
                            doctorDetails.additionalDetails.consultationFee
                          }
                          doctorCode={doctorDetails.code}
                          seoSlug={doctorDetails.seoSlug}
                        />
                      </Box>
                    );
                  })}
                </Box>
              </Box>
            ))}
          {procedures.length > 0 &&
            (procedures.length > 3 ? (
              <Box
                sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
              >
                <Typography variant="h5">
                  Medical Procedures for {specialityName}
                </Typography>
                <Slider {...proceduresCarousalSettings}>
                  {procedures.map((procedure, index) => {
                    const { code = null } = procedure || {};
                    return (
                      <Box
                        id={`speciality-${specialityCode}-procedure${index}Box`}
                        key={code}
                        // sx={{ padding: "8px 24px" }}
                      >
                        <ProcedureCard
                          title={procedure.displayName}
                          icon={procedure.bannerUrl}
                          description={procedure.shortDescription}
                          procedureCode={procedure.code}
                          seoSlug={procedure.seoSlug}
                        />
                      </Box>
                    );
                  })}
                </Slider>
              </Box>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <Typography variant="h5" sx={{ textAlign: "center" }}>
                  Medical Procedures for {specialityName}
                </Typography>

                <Box
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", sm: "column", md: "row" },
                    flexWrap: "wrap",
                    justifyContent: "center",
                    gap: "16px",
                    padding: { xs: "0 16px", md: "0" },
                  }}
                >
                  {procedures.map((procedure, index) => {
                    const { code = null } = procedure || {};
                    return (
                      <Box
                        id={`speciality-${specialityCode}-procedure${index}Box`}
                        key={code}
                        sx={{
                          padding: "8px 0",
                          width: { xs: "100%", sm: "48%", md: "30%" },
                          boxSizing: "border-box",
                          display: "flex",
                          justifyContent: "center",
                        }}
                      >
                        <ProcedureCard
                          title={procedure.displayName}
                          icon={procedure.bannerUrl}
                          description={procedure.shortDescription}
                          procedureCode={procedure.code}
                          seoSlug={procedure.seoSlug}
                        />
                      </Box>
                    );
                  })}
                </Box>
              </Box>
            ))}
          {blogs.length > 0 &&
            (blogs.length > 3 ? (
              <Box
                sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
              >
                <Typography variant="h5">Blogs</Typography>
                <Slider {...proceduresCarousalSettings}>
                  {blogs.map((blog, index) => {
                    const { code = null } = blog || {};
                    return (
                      <Box
                        id={`speciality-${specialityCode}-blog${index}Box`}
                        key={code}
                        sx={{ padding: "8px 24px" }}
                      >
                        <BlogCard
                          id={`speciality-${specialityCode}-blog${index}`}
                          blog={blog}
                        />
                      </Box>
                    );
                  })}
                </Slider>
              </Box>
            ) : (
              <Box
                sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
              >
                <Typography variant="h5">Blogs</Typography>
                <Box
                  sx={{
                    display: "grid",
                    gridTemplateColumns: {
                      xs: "1fr",
                      sm: "1fr 1fr",
                      md: "repeat(3, 1fr)",
                    },
                  }}
                >
                  {blogs.map((blog, index) => {
                    const { code = null } = blog || {};
                    return (
                      <Box
                        id={`speciality-${specialityCode}-blog${index}Box`}
                        key={code}
                        sx={{ padding: "8px 24px" }}
                      >
                        <BlogCard
                          id={`speciality-${specialityCode}-blog${index}`}
                          blog={blog}
                        />
                      </Box>
                    );
                  })}
                </Box>
              </Box>
            ))}
        </Box>

        <ReviewsSection
          enterpriseCode={enterpriseCode}
          specialityCode={medicalSpecialityCode}
          isDetailPage={true}
        />
        <Box
          sx={{
            marginTop: {
              xs: "50px",
              sm: "50px",
              md: "50px",
              lg: "40px",
            },
          }}
        >
          {formattedRelatedLinks.length > 0 && (
            <RelatedLinks
              links={formattedRelatedLinks}
              basePath={`/specialities/`}
              title="Related Specialities"
            />
          )}
        </Box>
      </SectionLayoutSingleSpecialitySingleHospital>
      {faqs.length > 0 && <FaqsSection faqs={faqs} />}
    </Box>
  );
};

export default SpecialityDetail;
