import {getEnterpriseCode} from "@/app/oasis/blogs/[blogCode]/layout";
import {
  API_ENDPOINT_LANDING_PAGE,
  API_SECTION_API,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_VERSION,
  HARBOR_API_DOCFYN_DOMAIN
} from "@/constants";
import axios from "axios";

const fetchLandingPageDetails = async (pageCode,  epsCode) => {
  // Get enterprise code
  if (!epsCode) return;

  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_LANDING_PAGE}?pageCode=${pageCode}&enterpriseCode=${epsCode}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};

    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      return result;
    }
  } catch (error) {
    console.error("Error fetching landing page details:", error);
  }

};
export const generateMetadata = async ({ params }) => {
  const epsCode = await getEnterpriseCode();
  const { pageCode = null } = params || {};
  try {
    const result =  await fetchLandingPageDetails(pageCode, epsCode);
    const { seoTitle = "", seoDescription = "" } = result || {};
    return  {
      title: seoTitle || "",
      description: seoDescription || "",
    };

  } catch (error) {
    console.log(error);
  }
};

export default function CustomPageLayout({ children }) {
  return <>{children}</>;
}
