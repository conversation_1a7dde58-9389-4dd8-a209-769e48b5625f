"use client";

import { Box } from "@mui/material";
import Navbar from "../Navbar";
import React, { useEffect, useState } from "react";
import BottomNavBar from "@/app/singlespecialitymultichain2/components/bottomNavBar";

const Structure = ({ children }) => {
  const noNavbarPaths = ["landing-page", "chat"];
  const [showNavbar, setShowNavbar] = useState(true);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const isShow = !noNavbarPaths.includes(
      window.location.pathname.split("/")[1]
    );
    setShowNavbar(isShow);
    setIsChecking(false);
  }, []);

  if (isChecking) return <></>;
  return (
    <div
      style={{
        minHeight: "100vh",
        maxWidth: "100vw",
      }}
    >
      {/* <TopNavbar/> */}
      {showNavbar && <Navbar />}
      <Box sx={{ minHeight: "calc(100vh - 150px)", }} >{children}</Box>
      {/* {showNavbar && <Footer />} */}
      <BottomNavBar />
    </div>
  );
};

export default Structure;
