import React from 'react';
import { getEnterpriseCode } from "../blogs/location/[locationCode]/[blogCode]/layout";
import PopularPackages from './PopularPackages';
import { getLabPackages } from '@/api/harbor.service';

const PopularPackagesWrapper = async () => {
  // Start with loading state
  const loadingComponent = <PopularPackages isLoading={true} packagesData={{ data: [], totalCount: 0 }} />;

  try {
    // Get the enterprise code
    const enterpriseCode = await getEnterpriseCode();

    // Fetch initial data (first page)
    const initialData = await getLabPackages(enterpriseCode, { page: 1, perPage: 10 });

    // If no data, don't render the component
    if (!initialData?.data || initialData.data.length === 0) {
      return null;
    }

    // Pass the initial data and enterprise code to the client component
    return (
      <PopularPackages
        packagesData={initialData}
        isLoading={false}
        enterpriseCode={enterpriseCode}
      />
    );
  } catch (error) {
    console.error("Error in PopularPackagesWrapper:", error);
    return loadingComponent;
  }
};

export default PopularPackagesWrapper;
