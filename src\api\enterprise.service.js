import {getWebsiteHost} from "@/app/utils/clientOnly/clientUtils";
import {
    API_SECTION_API,
    API_SECTION_PUBLIC,
    API_SECTION_WEBSITE,
    API_VERSION,
    HARBOR_API_DOCFYN_DOMAIN
} from "@/constants";

export const getEnterpriseCode = async () => {
    const domainName = getWebsiteHost();
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true`;
    try {
        const res = await fetch(url, {
            cache: "no-store"
        });
        const jsonRes = await res.json();
        const { result = {} } = jsonRes || {};
        const { enterprise_code: enterpriseCode = null } = result || {};
        return enterpriseCode;
    } catch (error) {
        console.log("getWebsiteData", error);
    }
}

