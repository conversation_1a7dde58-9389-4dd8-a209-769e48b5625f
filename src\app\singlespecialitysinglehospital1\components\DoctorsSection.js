"use client";

import { useState, useEffect, useRef, useContext } from "react";
import {
  Box,
  Button,
  Container,
  Typography,
  CircularProgress,
} from "@mui/material";
import DoctorCard from "./DoctorCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
// import "swiper/css/navigation";
import "swiper/css/autoplay";

import "swiper/css/pagination";
import { AppContext } from "@/app/AppContextLayout";
import { getDoctors } from "@/api/harbor.service";
import { useRouter } from "next/navigation";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

// Custom Hook for Intersection Observer
const useObserver = () => {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stops observing after first reveal
        }
      },
      { threshold: 0.2 }
    );

    if (ref.current) observer.observe(ref.current);

    return () => observer.disconnect();
  }, []);

  return [ref, isVisible];
};

const DoctorsSection = () => {
  const [doctors, setDoctors] = useState([]);
  const [cardsRef, cardsVisible] = useObserver();
  const [isLoading, setIsLoading] = useState(true);
  const swiperRef = useRef(null);
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const router = useRouter();
  useEffect(() => {
    const fetchDoctors = async () => {
      setIsLoading(true);
      try {
        const data = await getDoctors(enterpriseCode);
        setDoctors(data?.result.doctors || []);  
      } catch (error) {
        setViewSnackbarMain({
          message: "Something went wrong!",
          type: "error",
        });
      } finally {
        setIsLoading(false);
      }
    };
    if (enterpriseCode) fetchDoctors();
  }, [enterpriseCode, setViewSnackbarMain]);

  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper && !isLoading) {
      setTimeout(() => {
        swiperRef.current.swiper.update();
        swiperRef.current.swiper.autoplay.start();
      }, 0);
    }
  }, [doctors, isLoading]);

  return (
    <>
    {doctors && <SectionLayoutSingleSpecialitySingleHospital>
     <Box
      sx={{
        position: "relative",
        py: { xs: 4, md: 0 },
        px: { xs: 2, md: 0 },
        maxWidth: "2150px",
        mx: "auto",
        width: "100%",
      }}
    >
       <Box 
            sx={{ 
              textAlign: "center", 
              mb: { xs: 4, sm: 5, md: 3 },
              maxWidth: "800px",
              mx: "auto"
            }}
          >
            <Typography
              variant="h5"
              sx={{
                color: "primary.main",
                mb: 1,
                display: "block",
                fontSize: { xs: "1.125rem", md: "1.25rem" },
              }}
            >
              OUR DOCTORS
            </Typography>
            <Typography
              variant="h3"
              sx={{
                color: "#1a1a1a",
                mb: 2,
                fontSize: { xs: "1.875rem", sm: "2.25rem", md: "2.5rem" },
                lineHeight: 1.2,
              }}
            >
              Our Eye Care Specialists
            </Typography>
          </Box>

      <Box
        ref={cardsRef}
        sx={{
          mt: 3,
          maxWidth: "2150px !important",
          width: "100%",
          "& .swiper": {
            width: "100%",
            height: "100%",
          },
          "& .swiper-slide": {
            display: "flex",
            justifyContent: "center",
          },
          "& .swiper-button-next, & .swiper-button-prev": {
            color: "#000",
            "&:after": {
              fontSize: "24px",
            },
          },
          "& .swiper-pagination-bullet-active": {
            backgroundColor: "#000",
          },
        }}
      >
        {isLoading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "200px",
            }}
          >
            <CircularProgress />
          </Box>
        ) : doctors.length > 0 ? (
          <Swiper
            modules={[Autoplay, Pagination]}
            spaceBetween={20}
            slidesPerView={1}
            loop={true}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            pagination={{ dynamicBullets: true, clickable: true }}
            breakpoints={{
              320: {
                slidesPerView: 1,
              },
              640: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            style={{ padding: "10px 0 50px 0", width: "100%" }}
          >
            {doctors.map((doctor, index) => (
              <SwiperSlide key={doctor.id}>
                <DoctorCard
                  index={index}
                  cardsVisible={cardsVisible}
                  name={doctor.doctorDetails.name}
                  specialization={
                    doctor.doctorDetails.medicalSpecialities
                      .map((speciality) => speciality.name)
                      .join(", ") || doctor.doctorDetails.description
                  }
                  imageUrl={doctor.doctorDetails.profilePicture || ""}
                  experience={
                    doctor.doctorDetails.additionalDetails.workExperience
                  }
                  consultationFee={
                    doctor.doctorDetails.additionalDetails.consultationFee
                  }
                  doctorCode={doctor.doctorDetails.code}
                  seoSlug={doctor.doctorDetails.seoSlug}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        ) : (
          <Typography>No Doctors available.</Typography>
        )}

        <Box sx={{ display: "flex", mt: 3, justifyContent: "center" }}>
          <Button
            onClick={() => router.push("/doctors")}
            color="primary"
            sx={{
              borderRadius: "100px",
              backgroundColor: "primary.main",
              transform: "scale(1)",
              color: "text.primary",
              textTransform: "capitalize",
              fontWeight: "normal",
              fontSize: "14px",
              padding: "10px 20px",
              transition: "transform 0.3s ease",
              "&:hover": {
                backgroundColor: "primary.main",
              },
            }}
          >
            View Doctors
          </Button>
        </Box>
      </Box>
    </Box>
     </SectionLayoutSingleSpecialitySingleHospital> }
     
    </>
  );
};

export default DoctorsSection;
