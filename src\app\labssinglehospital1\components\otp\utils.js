import React from 'react';
import { Slide } from '@mui/material';

// Format phone number with spaces for better readability
export const formatPhoneNumber = (phone) => {
  if (!phone) return '';

  // For a 10-digit number, format as XXX XXX XXXX
  if (phone.length === 10) {
    return phone.replace(/(.{3})(.{3})(.{4})/, '$1 $2 $3');
  }

  // For other lengths, just add a space every 3 digits
  return phone.replace(/(.{3})/g, '$1 ').trim();
};

// Transition component for dialog
export const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
