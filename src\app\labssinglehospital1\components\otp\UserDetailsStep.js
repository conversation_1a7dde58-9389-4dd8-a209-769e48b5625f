'use client';

import React from 'react';
import {
  Box,
  Button,
  Typography,
  TextField,
  CircularProgress,
  Fade,
  Avatar
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';

const UserDetailsStep = ({
  userName,
  userEmail,
  error,
  isLoading,
  handleUserNameChange,
  handleUserEmailChange,
  handleSubmitUserDetails,
  handleSkipUserDetails
}) => {
  return (
    <Fade in={true} timeout={500}>
      <Box>
        {/* Person Icon and Header */}
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Avatar
            sx={{
              width: 60,
              height: 60,
              bgcolor: `rgba(25, 118, 210, 0.1)`,
              color: 'primary.main',
              margin: '0 auto 12px',
            }}
          >
            <PersonIcon sx={{ fontSize: 30 }} />
          </Avatar>

          <Typography
            variant="h5"
            sx={{ fontWeight: 700, mb: 0.5, color: 'text.black' }}
          >
            Almost Done!
          </Typography>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ maxWidth: 400, mx: 'auto' }}
          >
            Add your details to receive booking updates
          </Typography>
        </Box>

        {/* User Details Form */}
        <form onSubmit={(e) => {
          e.preventDefault();
          // Submit with validation
          handleSubmitUserDetails(false);
        }}>
          <Box>

            {/* Name Input */}
            <Box sx={{ mb: 2 }}>
              <TextField
                label="Your Name"
                variant="outlined"
                fullWidth
                size="small"
                value={userName}
                onChange={handleUserNameChange}
                placeholder="Enter your name"
                InputProps={{
                  sx: {
                    borderRadius: 2,
                    color: 'text.black',
                  },
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: 'text.black',
                    },
                  },
                  '& .MuiInputBase-input': {
                    color: 'text.black',
                  },
                }}
              />
            </Box>

            {/* Email Input */}
            <Box sx={{ mb: 1 }}>
              <TextField
                label="Email Address"
                variant="outlined"
                fullWidth
                size="small"
                type="email"
                value={userEmail}
                onChange={handleUserEmailChange}
                placeholder="Enter your email address"
                InputProps={{
                  sx: {
                    borderRadius: 2,
                    color: 'text.black',
                  },
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '&:hover fieldset': {
                      borderColor: 'text.black',
                    },
                  },
                  '& .MuiInputBase-input': {
                    color: 'text.black',
                  },
                }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                We'll send your booking details to this email
              </Typography>
            </Box>

            {/* Error message */}
            {error && (
              <Typography color="error" variant="body2" sx={{ mt: 1, mb: 1 }}>
                {error}
              </Typography>
            )}
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            {/* Skip Button */}
            <Button
              variant="outlined"
              onClick={handleSkipUserDetails}
              disabled={isLoading}
              sx={{
                py: 1,
                borderRadius: 2,
                fontSize: '0.9rem',
                fontWeight: 500,
                textTransform: 'none',
                color: 'text.secondary',
                borderColor: 'divider',
                flex: 1,
                '&:hover': {
                  borderColor: 'text.black',
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              Skip
            </Button>

            {/* Submit Button */}
            <Button
              type="submit"
              variant="contained"
              disabled={isLoading}
              sx={{
                py: 1,
                borderRadius: 2,
                fontSize: '0.9rem',
                fontWeight: 600,
                textTransform: 'none',
                boxShadow: 'none',
                flex: 1,
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                },
              }}
            >
              {isLoading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                "Continue"
              )}
            </Button>
          </Box>
        </form>
      </Box>
    </Fade>
  );
};

export default UserDetailsStep;
