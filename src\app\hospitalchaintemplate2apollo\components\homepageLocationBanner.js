"use client"
import {Typography, Box} from "@mui/material";
import Image from "next/image";
import {getThumborUrl} from "../../utils/getThumborUrl";
import Slider from "react-slick";
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const HomepageLocationBanner = ({banners = [], enterpriseCode, locationCode = null}) => {
    let cpBanners = [...banners]
    if (cpBanners.length === 0) {
        cpBanners = [{image_url: "/banner.webp"}];
    }
    const settings = {
        dots: true,
        infinite: true,
        adaptiveHeight: true,
        lazyLoad: false,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        speed: 500,
        autoplaySpeed: 3000,
        cssEase: "linear",
        pauseOnHover: true,
        arrows: false,
    };
    return (
        <div
            style={{
                position: "relative",
                //height: "500px",
                display: "flex",
                justifyContent: "center",
                width: "100%",
            }}
        >
            <Slider {...settings} style={{width: "100%", height: "100%"}}>
                {cpBanners.map((banner, index) => {
                    const {
                        image_url: bannerImg = "",
                        title = "",
                        text = "",
                    } = banner || {};
                    return (
                        <Box sx={{position: "relative", width: "100%"}}>
                            <Image
                                alt="slider1"
                                src={getThumborUrl(bannerImg)}
                                layout="responsive"
                                width={1920}
                                height={1080}
                                objectFit="cover"
                                priority={true}
                                style={{
                                    height: "auto",
                                    width: "100%",
                                }}
                            />
                            <Box sx={{
                                position: "absolute",
                                left: "50%",
                                top: "50%",
                                transform: "translate(-50%, -50%)",
                                textAlign: 'center'
                            }}>
                                <Typography
                                    variant="h3"
                                    sx={{
                                        fontSize: {xs: '1.5rem', sm: '2rem', md: '3rem'},
                                        color: 'text.primary',
                                        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                                    }}
                                >
                                    {title || ""}
                                </Typography>
                                <Typography
                                    variant="subtitle1"
                                    sx={{
                                        fontSize: {xs: '1rem', sm: '1.25rem', md: '1.5rem'},
                                        color: 'text.primary',
                                        textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                                    }}
                                >
                                    {text || ""}
                                </Typography>
                            </Box>
                        </Box>

                    );
                })}
            </Slider>
        </div>
    );
};

export default HomepageLocationBanner;
