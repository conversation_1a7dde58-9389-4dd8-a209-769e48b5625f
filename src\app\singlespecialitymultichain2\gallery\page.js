"use client";

import { Box, Typography, IconButton, DialogContent } from "@mui/material";
import React, { useContext, useEffect, useState } from "react";
import useStyles from "../styles";
import homepageStyles from "../components/Homepage/styles";
import useGalleryStyles from "./styles";
import Dialog from "@mui/material/Dialog";
import { AppContext } from "@/app/AppContextLayout";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import CloseIcon from "@mui/icons-material/Close";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import Footer from "../Footer";

const Gallery = () => {
  const commonClasses = useStyles();
  const classes = useGalleryStyles();
  const homepageClasses = homepageStyles();
  const { websiteData } = useContext(AppContext);
  const { multiMedia = [] } = websiteData || {};
  const [photos, setPhotos] = useState([]);
  const [videos, setVideos] = useState([]);
  const [open, setOpen] = React.useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [imgUrl, setImgUrl] = React.useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(null);
   const [playingVideoId, setPlayingVideoId] = useState(null);
  // const handleClickOpen = (imageUrl) => {
  //   setImgUrl(imageUrl);
  //   setOpen(true);
  // };

  // const handleClose = () => {
  //   setOpen(false);
  // };
  const handleOpenModal = (index) => {
    setCurrentImageIndex(index);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === photos.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handlePrevImage = () => {
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? photos.length - 1 : prevIndex - 1
    );
  };

  const getYouTubeVideoId = (url) => {
    try {
      // If it's already just an ID (11 characters)
      if (url.length === 11 && !url.includes("/")) {
        return url;
      }
  
      // Try to extract ID from full URL
      const regExp =
        /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
      const match = url.match(regExp);
      return match && match[2].length === 11 ? match[2] : null;
    } catch (error) {
      console.error("Error extracting YouTube ID:", error);
      return null;
    }
  };
  
  const formatVideoUrl = (url) => {
    const videoId = getYouTubeVideoId(url);
    return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
  };
  
  const getThumbnailUrl = (url) => {
    const videoId = getYouTubeVideoId(url);
    return videoId
      ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
      : null;
  };

  useEffect(() => {
    if (multiMedia.length > 0) {
      const photosArr = multiMedia.filter((media) => Boolean(media.image_url));
      const videosArr = multiMedia.filter((media) => Boolean(media.video_url));
      setPhotos(photosArr);
      setVideos(videosArr);
    }
  }, [multiMedia]);

  return (
    <>
    <div>
      {photos.length > 0 && (
        <Box>
          <Box style={{ position: "relative" }}>
            <Box className={classes.galleryBackgroundHeading}>Photos</Box>
            <Typography
              className={`${homepageClasses.sectionHeading} ${classes.galleryHeading}`}
              sx={{ fontSize: "3rem" }}
            >
              Gallery
            </Typography>
          </Box>
          <Box className={`${classes.galleryLayout} ${classes.galleryGridBox}`}>
            {photos.map((photo, index) => {
              const { image_url: imageUrl = "" } = photo || {};
              return (
                <Box
                  key={index}
                  // onClick={() => handleClickOpen(imageUrl)}
                  onClick={() => handleOpenModal(index)}
                  style={{ cursor: "pointer" }}
                  className={classes.galleryImgBox}
                >
                  <img
                    alt="default-img"
                    src={getThumborUrl(imageUrl, 0, 0)}
                    className={classes.galleryImg}
                  />
                </Box>
              );
            })}
          </Box>
          <Dialog
            open={openModal}
            onClose={handleCloseModal}
            maxWidth="xl"
            fullScreen
            PaperProps={{
              sx: {
                backgroundColor: "rgba(0, 0, 0, 0.49)",
                backgroundImage:
                  "linear-gradient(rgba(0, 0, 0, 0.24), rgba(0, 0, 0, 0.13))",
              },
            }}
          >
            {/* Close button */}
            <IconButton
              onClick={handleCloseModal}
              sx={{
                position: "absolute",
                right: "20px",
                top: "20px",
                zIndex: 2,
                color: "white",
                backgroundColor: "rgba(255, 255, 255, 0.1)",
                backdropFilter: "blur(4px)",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.2)",
                },
              }}
            >
              <CloseIcon />
            </IconButton>

            {/* Image container */}
            <DialogContent
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: { xs: 2, sm: 4 },
                height: "100vh",
                overflow: "hidden",
              }}
            >
              <Box
                sx={{
                  position: "relative",
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                {/* Main Image */}
                <Box
                  component="img"
                  src={photos[currentImageIndex]?.image_url}
                  alt={`Image ${currentImageIndex + 1}`}
                  sx={{
                    maxWidth: "90%",
                    maxHeight: "85vh",
                    objectFit: "contain",
                    borderRadius: "8px",
                    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
                    transition: "transform 0.3s ease-in-out",
                  }}
                />

                {/* Image counter */}
                <Typography
                  variant="body2"
                  sx={{
                    position: "absolute",
                    bottom: "20px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    color: "white",
                    backgroundColor: "rgba(0, 0, 0, 0.6)",
                    padding: "8px 16px",
                    borderRadius: "20px",
                    backdropFilter: "blur(4px)",
                  }}
                >
                  {currentImageIndex + 1} / {photos.length}
                </Typography>
              </Box>
            </DialogContent>

            {/* Navigation buttons */}
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: 0,
                right: 0,
                transform: "translateY(-50%)",
                display: "flex",
                justifyContent: "space-between",
                px: { xs: 2, sm: 4, md: 6 },
                pointerEvents: "none",
              }}
            >
              <IconButton
                onClick={handlePrevImage}
                sx={{
                  color: "white",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(4px)",
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                  },
                  pointerEvents: "auto",
                }}
              >
                <ArrowBackIcon sx={{ fontSize: 30 }} />
              </IconButton>

              <IconButton
                onClick={handleNextImage}
                sx={{
                  color: "white",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(4px)",
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                  },
                  pointerEvents: "auto",
                }}
              >
                <ArrowForwardIcon sx={{ fontSize: 30 }} />
              </IconButton>
            </Box>
          </Dialog>
          {/* <Dialog
              open={open}
              onClose={handleClose}
              aria-labelledby="alert-dialog-title"
              aria-describedby="alert-dialog-description"
              sx={{
                ".MuiDialog-paper": {
                  maxHeight: "calc(100vh - 64px)",
                  maxWidth: "calc(100vw - 64px)",
                  padding: 0,
                  borderRadius: "8px",
                  overflow: "hidden",
                  display: "flex", // Center content in the dialog
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "rgba(0, 0, 0, 0)", // Optional for better contrast
                },
              }}
          >
            <Box
                sx={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  overflow: "auto", // Allow scrolling if the image is still too large
                }}
            >
              <img
                  alt="slider1"
                  src={imgUrl}
                  style={{
                    maxHeight: "calc(100vh - 128px)", // Ensure it doesn't exceed dialog height
                    maxWidth: "calc(100vw - 128px)", // Ensure it doesn't exceed dialog width
                    width: "auto",
                    height: "auto",
                    objectFit: "contain",
                    objectPosition: "center",
                  }}
              />
            </Box>
          </Dialog> */}
        </Box>
      )}
      {videos.length > 0 && (
        <Box>
          <Box style={{ position: "relative" }}>
            <Box className={classes.galleryBackgroundHeading}>Videos</Box>
            <Typography
              className={`${classes.galleryHeading} ${homepageClasses.sectionHeading}`}
              sx={{ fontSize: "3rem" }}
            >
              Videos
            </Typography>
          </Box>
          <Box
          sx={{ paddingBottom: "50px"}}
            className={`${classes.galleryLayout} ${classes.galleryVideosGridBox}`}
          >
            {videos.map((video, index) => {
              // const { video_url: videoUrl = "" } = video || {};
              return (
                <Box
                  sx={{
                    height: "100%",
                    boxShadow: 2,
                    borderRadius: "10px",
                    overflow: "hidden",
                    position: "relative",
                  }}
                >
                  {playingVideoId === video.id ? (
                    <Box
                      component="iframe"
                      src={`${formatVideoUrl(video.video_url)}?autoplay=1`}
                      title={video.title}
                      sx={{
                        width: "100%",
                        height: 240,
                        border: 0,
                      }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  ) : (
                    <Box
                      onClick={() => setPlayingVideoId(video.id)}
                      sx={{
                        position: "relative",
                        cursor: "pointer",
                        "&:hover": {
                          "& .play-overlay": {
                            opacity: 1,
                          },
                        },
                      }}
                    >
                      <Box
                        component="img"
                        src={
                          getThumbnailUrl(video.video_url) || video.image_url
                        }
                        alt={video.title}
                        sx={{
                          width: "100%",
                          height: 240,
                          transition: "transform 0.3s ease-in-out",
                          objectFit: "cover",
                          "&:hover": { transform: "scale(1.02)" },
                        }}
                        onError={(e) => {
                          // Fallback to default image if thumbnail fails to load
                          e.currentTarget.src = video.image_url;
                        }}
                      />
                      <Box
                        className="play-overlay"
                        sx={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          background: "rgba(0,0,0,0.3)",
                          opacity: 0,
                          transition: "opacity 0.3s ease",
                        }}
                      >
                        <Box
                          sx={{
                            width: 60,
                            height: 60,
                            borderRadius: "50%",
                            background: "rgba(255,255,255,0.9)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 0,
                              height: 0,
                              borderTop: "15px solid transparent",
                              borderBottom: "15px solid transparent",
                              borderLeft: "25px solid #000",
                              marginLeft: "5px",
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  )}
                  <Box sx={{ p: 1 }}>
                    <Typography
                      variant="h6"
                      fontSize="18px"
                      sx={{
                        display: "-webkit-box",
                        WebkitBoxOrient: "vertical",
                        WebkitLineClamp: 2,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        color: "text.black",
                      }}
                    >
                      {video.title || ""}
                    </Typography>
                  </Box>
                </Box>
                // <Box key={index}>
                //   <iframe
                //     width="100%"
                //     height="400"
                //     src={`https://www.youtube.com/embed/${videoUrl}`}
                //     title="YouTube video player"
                //     allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                //   ></iframe>
                // </Box>
              );
            })}
          </Box>
        </Box>
      )}
    </div>
    <Footer websiteData={websiteData}/>
    </>
  );
};

export default Gallery;
