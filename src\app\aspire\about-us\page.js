"use client";

import {useContext} from "react";
import SectionLayoutAspire from "../styledComponents/SectionLayoutAspire";
import {AppContext} from "../../AppContextLayout";
import parse from "html-react-parser";

export default function AboutUs() {
    const {websiteData} = useContext(AppContext);
    const {about_us: aboutUs = ""} = websiteData || {};
    return <SectionLayoutAspire>
      <div
          className="ck-content"
          dangerouslySetInnerHTML={{__html: aboutUs}} // Render HTML safely
      />
    </SectionLayoutAspire>;
}
