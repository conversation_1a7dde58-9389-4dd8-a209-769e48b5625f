"use client";

import Box from "@mui/material/Box";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useContext, useEffect, useRef, useState } from "react";
import {
  Button,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  Typography,
  alpha,
} from "@mui/material";
import Image from "next/image";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SEARCH,
} from "@/constants";
import { AppContext } from "../../AppContextLayout";
import axios from "axios";
import { useTheme } from "@emotion/react";
import Link from "next/link";
import { useRouter } from "next/navigation";

const HomepageSearch = ({enterpriseCode, locationCode}) => {
  const theme = useTheme();
  const router = useRouter();
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const [searchInput, setSearchInput] = useState("");
  const [doctors, setDoctors] = useState([]);
  const [specialities, setSpecialities] = useState([]);
  const [filteredDoctors, setFilteredDoctors] = useState([]);
  const [filteredSpecialities, setFilteredSpecialities] = useState([]);
  // const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const searchRef = useRef();

  const handleSearchInput = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    const searchedDoctors = doctors.filter((doctor) => {
      const { name: doctorName = "" } = doctor || {};
      return doctorName.toLowerCase().includes(value.toLowerCase());
    });
    const searchedSpecialities = specialities.filter((speciality) => {
      const { name = "" } = speciality || {};
      return name.toLowerCase().includes(value.toLowerCase());
    });
    setFilteredDoctors(searchedDoctors);
    setFilteredSpecialities(searchedSpecialities);
  };

  const getSearchData = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${API_ENDPOINT_SEARCH}?enterpriseCode=${enterpriseCode}`;
    try {
      const response = await axios.get(url);
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { data: searchData = [] } = result || {};
        const { doctors = [], specialities = [] } = searchData || {};
        setDoctors(doctors);
        setSpecialities(specialities);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  const handleDoctorRedirection = (code) => {
    router.push(`/doctors/${locationCode}/${code}`);
  };

  const handleSpecialityRedirection = (code) => {
    router.push(`/specialities/${locationCode}/${code}`);
  };

  const handleClearSearch = (e) => {
    setTimeout(() => {
      setSearchInput("");
      setFilteredDoctors([]);
      setFilteredSpecialities([]);
    }, 200);
  };

  useEffect(() => {
    getSearchData();
  }, []);

  return (
    <Box sx={{ width: "100%", position: "relative" }} tabIndex={0} onBlur={handleClearSearch}>
      <Box
        sx={{
          backgroundColor: alpha(theme.palette.primary.main, .1),
          border: `1px solid ${alpha(theme.palette.primary.main, .2)}`,
          padding: "12px 16px",
          borderRadius: "6px",
          // width: { sm: "100%", md: "750px", lg: "900px" },
          display: "flex",
          alignItems: "center",
          gap: "16px",
          width: "100%",
          // boxShadow: `0 1px 6px ${alpha(theme.palette.primary.main, .25)}`
        }}
      >
        <InputBase
          id="homepageSearchInput"
          placeholder="Search for doctors and specialities"
          value={searchInput}
          onChange={handleSearchInput}
          fullWidth
          // sx={{ "&.MuiBox-root": { backgroundColor: "red" } }}
        />
        {Boolean(searchInput) ? (
          <ClearIcon id="homepageClearSearch" sx={{ cursor: "pointer" }} onClick={handleClearSearch} />
        ) : (
          <SearchIcon sx={{ cursor: "pointer" }} />
        )}
      </Box>
      {(filteredDoctors.length > 0 || filteredSpecialities.length > 0) && (
        <Box
          sx={{
            padding: "0 4px",
            background: "#fff",
            position: "absolute",
            width: "100%",
            maxHeight: "400px",
            overflowY: "auto",
            boxShadow: "0 2px 20px rgba(0, 0, 0, .1)",
            zIndex: 1,
          }}
        >
          <Box>
            {filteredDoctors.length > 0 && (
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "4px 16px",
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{ fontSize: "14px", color: "rgba(0, 0, 0, 0.6)" }}
                  >
                    Doctors
                  </Typography>
                  <Link
                    id="homepageSearchViewAllDoctors"
                    href="/doctors"
                    style={{
                      color: theme.palette.primary.main,
                      fontSize: "14px",
                    }}
                  >
                    View all
                  </Link>
                </Box>
                <List>
                  {filteredDoctors.map((doctor) => {
                    const { code = null, name = "", seoSlug = "" } = doctor || {};
                    return (
                      <ListItemButton
                        id={`homepageSearchDoctor-${seoSlug}`}
                        key={code}
                        onClick={() => handleDoctorRedirection(seoSlug)}
                      >
                        <ListItemIcon sx={{ minWidth: "32px", mr: 2 }}>
                          <Image
                            alt="doctor"
                            src="/doctor-male.svg"
                            height={32}
                            width={32}
                          />
                        </ListItemIcon>
                        <ListItemText primary={name || ""} />
                      </ListItemButton>
                    );
                  })}
                </List>
              </Box>
            )}
            {filteredSpecialities.length > 0 && (
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "4px 16px",
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{ fontSize: "14px", color: "rgba(0, 0, 0, 0.6)" }}
                  >
                    Specialities
                  </Typography>
                  <Link
                    id="homepageSearchViewAllSpecialities"
                    href="/specialities"
                    style={{
                      color: theme.palette.primary.main,
                      fontSize: "14px",
                    }}
                  >
                    View all
                  </Link>
                </Box>
                <List>
                  {filteredSpecialities.map((speciality) => {
                    const { name = "", code = null, seoSlug = "" } = speciality || {};
                    return (
                      <ListItemButton
                        id={`homepageSearchSpeciality-${seoSlug}`}
                        key={code}
                        onClick={() => handleSpecialityRedirection(seoSlug)}
                      >
                        <ListItemIcon sx={{ minWidth: "32px", mr: 2 }}>
                          <Image
                            alt="speciality-icon"
                            src="/speciality-icon.svg"
                            height={32}
                            width={32}
                          />
                        </ListItemIcon>
                        <ListItemText primary={name} />
                      </ListItemButton>
                    );
                  })}
                </List>
              </Box>
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default HomepageSearch;
