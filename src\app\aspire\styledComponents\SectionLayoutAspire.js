import Box from "@mui/material/Box";

const SectionLayoutAspire = ({ children, ...props }) => {
  return (
    <Box
      {...props}
      sx={{
        // padding: { xs: "32px 16px", md: "32px 128px", lg: "48px 200px" },
          padding: { xs: "16px 16px", sm:"24px 16px",  md: "32px 80px", lg: "48px 100px" },
        ...props.sx
      }}
    >
      {children}
    </Box>
  );
};

export default SectionLayoutAspire;
