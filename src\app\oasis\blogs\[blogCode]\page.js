import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_BLOGS,
  LEAD_SOURCES,
} from "@/constants";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { getEnterpriseCode } from "@/app/oasis/blogs/[blogCode]/layout";
import dynamic from "next/dynamic";
import SectionLayoutOasis from "../../styledComponents/SectionLayoutOasis";

const QuickEnquiry = dynamic(
  () => import("@/app/commoncomponents/quickEnquiry"),
  {
    ssr: false,
  }
);

const BlogUrlReplacer = dynamic(() => import("./BlogUrlReplacer"), {
  ssr: false,
});

const getBlogDetails = async (blogCode, enterpriseCode) => {
  if (!blogCode || !enterpriseCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?code=${blogCode}`;
  try {
    const response = await fetch(url);
    const status = response.status;
    const data = await response.json();
    if (status === 200) {
      const { result = [] } = data || {};
      console.log("check here result " + result);
      return result;
    }
  } catch (error) {
    console.log(error);
  }
};
const BlogDetail = async ({ params }) => {
  const { blogCode = null } = params || {};
  const enterpriseCode = await getEnterpriseCode();
  const blogDetail = await getBlogDetails(blogCode, enterpriseCode);
  if (blogDetail.length === 0) return null;

  const {
    title = "",
    content = "",
    imageUrl = "",
    seoSlug = "",
  } = blogDetail[0] || {};

  return (
    <Box>
      <SectionLayoutOasis>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
          {imageUrl && (
            <Box
              sx={{
                minHeight: { xs: "240px", sm: "320px", md: "400px" },
                position: "relative",
              }}
            >
              <Image
                alt="blog-banner"
                fill
                sizes="(max-width: 768px) 100vw, 700px"
                priority
                src={getThumborUrl(imageUrl)}
                style={{
                  height: "100%",
                  width: "100%",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  objectFit: "contain",
                  borderRadius: "10px",
                }}
              />
            </Box>
          )}
          <Typography
            variant="h3"
            align="center"
            sx={{
              fontWeight: "500",
              color: "primary.main",
              fontSize: { xs: "1.75rem", sm: "40px" },
            }}
          >
            {title || ""}
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", lg: "row" }, // Stack on small screens, side-by-side on large screens
              gap: { xs: "24px", lg: "24px" },
            }}
          >
            <Box
              sx={{
                flex: { lg: "7", xs: "1" }, // 70% width for large screens, full width for small screens
                flexBasis: { lg: "70%", xs: "100%" }, // Explicitly set the percentage width
                maxWidth: { lg: "70%", xs: "100%" }, // Prevent overflow on small screens
              }}
            >
              <div
                className="ck-content"
                dangerouslySetInnerHTML={{ __html: content }} // Render HTML safely
              />
            </Box>
            <Box
              sx={{
                flex: { lg: "3", xs: "1" }, // 30% width for large screens, full width for small screens
                flexBasis: { lg: "30%", xs: "100%" }, // Explicitly set the percentage width
                maxWidth: { lg: "30%", xs: "100%" }, // Prevent overflow on small screens
                position: "sticky", // Sticky positioning for larger screens
                top: "200px", // Adjust how far from the top it sticks
                alignSelf: { xs: "center", lg: "flex-start" }, // Center on small screens, top align on large
                flexShrink: 0, // Prevent shrinking of the box
              }}
            >
              <QuickEnquiry
                pageData={{
                  leadSource: LEAD_SOURCES.BLOG_DETAIL_PAGE,
                  productCode: blogCode,
                  pageTitle: title,
                  enterpriseCode: enterpriseCode,
                }}
              />
            </Box>
          </Box>
        </Box>
      </SectionLayoutOasis>
      <BlogUrlReplacer blogCode={blogCode} seoSlug={seoSlug} />
    </Box>
  );
};

export default BlogDetail;
