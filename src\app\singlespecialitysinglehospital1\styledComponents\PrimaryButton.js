import Button from "@mui/material/Button";

const PrimaryButton = ({ children,isActive, ...props }) => {
  return (
    <Button
      color="primary"
      variant="contained"
      disableElevation
      sx={{
        borderRadius: "100px",
        fontWeight: "400",
        boxShadow: "none",
        textTransform: "none",
        padding:"10px 20px",
        color: "text.primary",
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

export default PrimaryButton;
