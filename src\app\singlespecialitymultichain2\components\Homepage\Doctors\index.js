import React, {useState} from "react";

import {Box, Typography} from "@mui/material";
import useStyles from "../../../styles";
import homepageStyles from "../styles";
import SectionLayout from "@/app/singlespecialitymultichain2/components/SectionLayout";

const Doctors = ({doctors = []}) => {
  const commonClasses = useStyles();
  const classes = homepageStyles();
  const [doctorInfo, setDoctorInfo] = useState({});
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);

  const handleMakeAppointment = (doctorInfo) => {
    setDoctorInfo(doctorInfo);
    setShowAppointmentDialog(true);
  };

  if (doctors.length === 0) return <></>
  return (
      <SectionLayout
          id="doctors"
          className={`${classes.doctorsSection}`}
      >
        <Box
            sx={{
              display: "grid",
              gridTemplateColumns: {xs: "1fr", md: "1fr 1fr"},
              columnGap: "24px",
              rowGap: "32px",
              flex: "1",
              width: "100%"
            }}
        >
            {doctors.map((doctor, index) => {
                const { doctorDetails = {} } = doctor || {};
                const {
                    medicalSpecialities = [],
                    name: doctorName = "",
                    bookAppointmentEnabled = false,
                    profilePicture = null,
                } = doctorDetails || {};
                if (doctors.length === 1) {
                    return (
                        <DoctorCard
                            key={index}
                            doctorDetails={doctorDetails}
                            id={`doctor${index}`}
                        />
                    );
                } else
                    return (
                        <DoctorCard
                            key={index}
                            doctorDetails={doctorDetails}
                            id={`doctor${index}`}
                        />
                    );
            })}
        </Box>
        <Box className={classes.doctorSectionHeadingBox} sx={{flex: 1}}>
          <Typography variant="h5"
                      sx={{
                          textTransform: "uppercase",
                          color: "primary.main",
                      }}>
            Meet Our
          </Typography>
          <Typography variant="h3" className={classes.homepageSectionSubHeading} sx={{
              marginTop: {xs:"-10px"}, color: "#333333"
          }}>
            Experts Doctor
          </Typography>
          <Typography variant="body1" style={{lineHeight: "1.75", color: "#333333"}}>
            Our team of specialist doctors is dedicated to providing you with the
            highest quality of care. Each doctor brings a wealth of knowledge,
            experience, and compassion to your healthcare journey. Whether you
            need a general check-up or require specialized treatment, our doctors
            are here to guide you every step of the way. We take pride in the
            expertise and dedication of our medical professionals, ensuring that
            you receive the best possible care and support for your health and
            well-being.
          </Typography>
        </Box>
        {showAppointmentDialog && (
            <AppointmentDialog
                doctorInfo={doctorInfo}
                isOpen={showAppointmentDialog}
                onClose={() => setShowAppointmentDialog(false)}
            />
        )}
      </SectionLayout>
  );
};

export default Doctors;
