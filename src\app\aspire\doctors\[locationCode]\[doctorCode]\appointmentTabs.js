"use client";

import { useTheme } from "@emotion/react";
import { useContext, useState } from "react";
import { format, parse } from "date-fns";
import PropTypes from "prop-types";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import LightModeOutlinedIcon from "@mui/icons-material/LightModeOutlined";
import WbTwilightOutlinedIcon from "@mui/icons-material/WbTwilightOutlined";
import DarkModeOutlinedIcon from "@mui/icons-material/DarkModeOutlined";
import { AppointmentSchedulerContext } from "./appointmentScheduler";
import { AppBar } from "@mui/material";

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box
          sx={{ p: 2, display: "flex", flexDirection: "column", gap: "32px" }}
        >
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const getTabLabel = (date, noOfSlots = 0) => {
  return (
    <div>
      <Typography variant="subtitle1" sx={{ fontSize: "14px" }}>
        {date}
      </Typography>
      <Typography
        variant="subtitle1"
        style={{ color: noOfSlots === 0 ? "gray" : "#01a400", fontSize: "12px" }}
      >
        {noOfSlots === 0 ? "No" : noOfSlots} Slots Available
      </Typography>
    </div>
  );
};

const AppointmentTabs = ({ slots = [] }) => {
  const theme = useTheme();
  const [value, setValue] = useState(0);
  const { setSelectedAppointment, handleComponentDisplay } = useContext(AppointmentSchedulerContext);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleSelectTimings = (slot) => {
    setSelectedAppointment(slot);
    handleComponentDisplay(1);
  };

  const divideSlots = (arr, type) => {
    let finalSlots = [];
    finalSlots = arr.filter((item) => {
      const { st: startTime = "" } = item || {};
      const time = parseInt(startTime.split(":")[0], 10);
      if (type === "morning" && time >= 0 && time < 12) {
        return item;
      } else if (type === "afternoon" && time >= 12 && time < 17) {
        return item;
      } else if (type === "evening" && time >= 17 && time < 24) return item;
      return null;
    });
    return finalSlots;
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Box sx={{ background: "#fff", position: "relative" }}>
          <Tabs
            value={value}
            onChange={handleChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="basic tabs example"
          >
            {slots.map((slot, index) => {
              const { day = "", date = "", sessions = [] } = slot || {};
              let allSlots = [];
              sessions.forEach((session) => {
                const { timeSlots = [] } = session || {};
                allSlots = [...allSlots, ...timeSlots];
              });

              const dateObject = new Date(date);
              const formattedDate = `${day.slice(0, 3)}, ${format(
                dateObject,
                "d MMM yy"
              )}`;
              return (
                <Tab
                  style={{ textTransform: "none" }}
                  label={getTabLabel(formattedDate, allSlots.length)}
                  {...a11yProps(index)}
                />
              );
            })}
          </Tabs>
      </Box>
      {slots.map((slot, index) => {
        const { day = "", date = "", sessions = [] } = slot || {};
        let allSlots = [];
        sessions.forEach((session) => {
          const { timeSlots = [] } = session || {};
          allSlots = [...allSlots, ...timeSlots];
        });
        const morningSlots = divideSlots(allSlots, "morning");
        const afternoonSlots = divideSlots(allSlots, "afternoon");
        const eveningSlots = divideSlots(allSlots, "evening");
        const dateObject = new Date(date);
        const formattedDate = `${day.slice(0, 3)}, ${format(
          dateObject,
          "d MMM yy"
        )}`;
        const isSlotAvailable =
          morningSlots.length > 0 ||
          afternoonSlots.length > 0 ||
          eveningSlots.length > 0;
        return (
          <CustomTabPanel key={index} value={value} index={index}>
            {!isSlotAvailable ? (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: "10px",
                }}
              >
                <img alt="no-slot" src="/no-slot.svg" />
                <Typography
                  variant="body1"
                  style={{
                    color: theme.palette.text.secondary,
                    fontSize: "14px",
                  }}
                >{`No slots available for ${formattedDate}`}</Typography>
              </div>
            ) : (
              <>
                {morningSlots.length > 0 && (
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "16px",
                    }}
                  >
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: "4px" }}
                    >
                      <LightModeOutlinedIcon />
                      <Typography variant="body1">Morning</Typography>
                    </Box>
                    <Box
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(3, 1fr)",
                        gap: "8px",
                      }}
                    >
                      {morningSlots.map((slot, index) => {
                        const { st: sessionStartTime = "" } = slot || {};
                        const parsedTime = parse(
                          sessionStartTime,
                          "HH:mm",
                          new Date()
                        );
                        const formattedStartTime = format(
                          parsedTime,
                          "h:mm aa"
                        );
                        return (
                          <Box
                            sx={{
                              padding: "16px",
                              borderRadius: "8px",
                              border: `1px solid ${theme.palette.primary.main}`,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "14px",
                              color: theme.palette.primary.main,
                              cursor: "pointer",
                              transition: "all .3s",
                              "&:hover": {
                                bgcolor: "primary.main",
                                color: "#fff",
                              },
                            }}
                            onClick={() => handleSelectTimings(slot)}
                          >
                            {formattedStartTime || ""}
                          </Box>
                        );
                      })}
                    </Box>
                  </Box>
                )}
                {afternoonSlots.length > 0 && (
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "16px",
                    }}
                  >
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: "4px" }}
                    >
                      <WbTwilightOutlinedIcon />
                      <Typography variant="body1">Afternoon</Typography>
                    </Box>
                    <Box
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(3, 1fr)",
                        gap: "8px",
                      }}
                    >
                      {afternoonSlots.map((slot, index) => {
                        const { st: sessionStartTime = "" } = slot || {};
                        const parsedTime = parse(
                          sessionStartTime,
                          "HH:mm",
                          new Date()
                        );
                        const formattedStartTime = format(
                          parsedTime,
                          "h:mm aa"
                        );
                        return (
                          <Box
                            sx={{
                              padding: "16px",
                              borderRadius: "8px",
                              border: `1px solid ${theme.palette.primary.main}`,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "14px",
                              color: theme.palette.primary.main,
                              transition: "all .3s",
                              "&:hover": {
                                bgcolor: "primary.main",
                                color: "#fff",
                              },
                              cursor: "pointer",
                            }}
                            onClick={() => handleSelectTimings(slot)}
                          >
                            {formattedStartTime || ""}
                          </Box>
                        );
                      })}
                    </Box>
                  </Box>
                )}
                {eveningSlots.length > 0 && (
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "16px",
                    }}
                  >
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: "4px" }}
                    >
                      <DarkModeOutlinedIcon />
                      <Typography variant="body1">Evening</Typography>
                    </Box>
                    <Box
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(3, 1fr)",
                        gap: "8px",
                      }}
                    >
                      {eveningSlots.map((slot, index) => {
                        const { st: sessionStartTime = "" } = slot || {};
                        const parsedTime = parse(
                          sessionStartTime,
                          "HH:mm",
                          new Date()
                        );
                        const formattedStartTime = format(
                          parsedTime,
                          "h:mm aa"
                        );
                        return (
                          <Box
                            sx={{
                              padding: "16px",
                              borderRadius: "8px",
                              border: `1px solid ${theme.palette.primary.main}`,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              fontSize: "14px",
                              color: theme.palette.primary.main,
                              transition: "all .3s",
                              "&:hover": {
                                bgcolor: "primary.main",
                                color: "#fff",
                              },
                              cursor: "pointer",
                            }}
                            onClick={() => handleSelectTimings(slot)}
                          >
                            {formattedStartTime || ""}
                          </Box>
                        );
                      })}
                    </Box>
                  </Box>
                )}
              </>
            )}
          </CustomTabPanel>
        );
      })}
    </Box>
  );
};

export default AppointmentTabs;
