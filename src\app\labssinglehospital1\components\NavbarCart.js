import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  IconButton,
  useMediaQuery,
  useTheme,
  Slide,
  Backdrop,
  Snackbar,
  Alert,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import { useEffect, useState, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  removeFromCart,
  selectCartItems,
  selectCartTotalAmount,
  clearCart,
} from "../redux/slices/cartSlice";
import { setCartOpen } from "../redux/slices/uiSlice";
import OtpVerificationModal from "./OtpVerificationModal";

const NavbarCart = ({ onClose }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [isOpen, setIsOpen] = useState(true);
  const [otpModalOpen, setOtpModalOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Get cart items and total from Redux store
  const cartItems = useSelector(selectCartItems);
  const total = useSelector(selectCartTotalAmount);
  const dispatch = useDispatch();

  const handleClose = () => {
    setIsOpen(false);
    // Update the global cart open state
    dispatch(setCartOpen(false));
    setTimeout(() => {
      onClose();
    }, 300);
  };

  // Handle proceed to checkout
  const handleProceedToCheckout = () => {
    setOtpModalOpen(true);
  };

  // Handle OTP verification success
  const handleOtpSuccess = useCallback(() => {
    // Show success message
    setSnackbar({
      open: true,
      message: "Your booking has been confirmed!",
      severity: "success",
    });

    // Clear the cart when user manually closes the OTP modal
    // The OTP modal will stay open until the user closes it
    dispatch(clearCart());
  }, [dispatch]);

  // Handle snackbar close
  const handleSnackbarClose = (_, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  // Set cart as open when component mounts
  useEffect(() => {
    dispatch(setCartOpen(true));
  }, [dispatch]);

  // Prevent body scrolling when cart is open on mobile
  useEffect(() => {
    if (isMobile) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMobile]);

  // Handle removing item from cart
  const handleRemoveItem = (id, itemType) => {
    dispatch(removeFromCart({ id, itemType }));
  };

  return (
    <>
      {/* Backdrop for mobile */}
      {isMobile && (
        <Backdrop
          open={isOpen}
          onClick={handleClose}
          sx={{
            zIndex: 1200,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            transition: "opacity 300ms ease-in-out",
          }}
        />
      )}

      {/* Cart container with different styling for mobile and desktop */}
      <Slide
        direction={isMobile ? "up" : "left"}
        in={isOpen}
        mountOnEnter
        unmountOnExit
        timeout={{
          enter: 300,
          exit: 300,
        }}
      >
        <Box
          sx={{
            width: isMobile
              ? "100%"
              : { sm: "450px", md: "500px", lg: "500px" },
            height: isMobile ? "80vh" : "auto",
            maxHeight: isMobile ? "80vh" : "90vh",
            position: "fixed",
            bottom: isMobile ? 0 : "auto",
            top: isMobile ? "auto" : { sm: "0px", md: "80px" },
            right: isMobile ? 0 : { sm: "25px" },
            left: isMobile ? 0 : "auto",
            backgroundColor: "white",
            boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
            borderRadius: isMobile ? "12px 12px 0 0" : "12px",
            zIndex: 1300,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Box
            sx={{
              px: { xs: 1.5, sm: 2 },
              py: { xs: 0.75, sm: 1 },
              borderBottom: "1px solid #e0e0e0",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              sx={{ color: "text.black" }}
            >
              Your Cart ({cartItems.length})
            </Typography>
            <IconButton onClick={handleClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>

          <Box
            sx={{
              flex: 1,
              overflowY: "auto",
              p: { xs: 1, sm: 2 },
              "&::-webkit-scrollbar": {
                width: "4px",
              },
              "&::-webkit-scrollbar-track": {
                backgroundColor: "#f1f1f1",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb": {
                backgroundColor: "#888",
                borderRadius: "4px",
                "&:hover": {
                  backgroundColor: "#555",
                },
              },
            }}
          >
            {cartItems.length === 0 ? (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                  minHeight: "200px",
                  p: 3,
                  textAlign: "center",
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: "50%",
                    backgroundColor: "#f5f5f5",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mb: 2,
                  }}
                >
                  <ShoppingCartIcon sx={{ fontSize: 40, color: "#bdbdbd" }} />
                </Box>
                <Typography
                  variant="h6"
                  sx={{ mb: 1, fontWeight: "bold", color: "text.black" }}
                >
                  Your cart is empty
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Add tests or health packages to your cart to proceed with
                  booking
                </Typography>
              </Box>
            ) : (
              cartItems.map((item, index) => (
                <Box
                  key={`${item.id}-${index}`}
                  sx={{
                    p: { xs: 1.5, sm: 2 },
                    mb: { xs: 1, sm: 2 },
                    backgroundColor: "white",
                    cursor: "pointer",
                    borderRadius: "12px",
                    border: "2px solid #e0e0e0",
                    display: "flex",
                    flexDirection: { xs: "column", md: "row" },
                    justifyContent: "space-between",
                    alignItems: { xs: "flex-start", md: "center" },
                    gap: { xs: 1.5, md: 2 },
                    transition: "all 0.2s ease",
                    "&:hover": {
                      transform: "translateY(-1px)",
                      boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                    },
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      gap: 2,
                      flex: 1,
                      width: "100%",
                    }}
                  >
                    <Box>
                      <Typography
                        variant={isMobile ? "subtitle1" : "h6"}
                        sx={{ mb: 0, color: "text.black" }}
                      >
                        {item.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 1,
                          display: "-webkit-box",
                          overflow: "hidden",
                          WebkitBoxOrient: "vertical",
                          WebkitLineClamp: 2,
                        }}
                      >
                        {item.description ||
                          item.shortDescription ||
                          (typeof item.alternativeNames === 'string' ? item.alternativeNames : '') ||
                          (item.itemType === 'package' ? 'Health Package' : 'Lab Test')}
                      </Typography>
                      <Box
                        sx={{
                          display: "flex",
                          gap: { xs: 1, sm: 2 },
                          alignItems: "center",
                          flexWrap: "wrap",
                        }}
                      >
                        <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                          {/* Show tests included if there are 1 or more tests */}
                          {((item.testsIncludedText &&
                            item.testsIncludedText.split(" ")[0] &&
                            parseInt(item.testsIncludedText.split(" ")[0]) >=
                              1) ||
                            (item.totalTests && item.totalTests >= 1)) && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: theme.palette.text.primary,
                                backgroundColor: "primary.main",
                                px: 1,
                                py: 0.5,
                                borderRadius: "4px",
                                fontSize: { xs: "0.65rem", sm: "0.75rem" },
                              }}
                            >
                              {item.testsIncludedText ||
                                (item.totalTests
                                  ? `${item.totalTests} ${item.totalTests === 1 ? 'Test' : 'Tests'} included`
                                  : "")}
                            </Typography>
                          )}

                          <Typography
                            variant="caption"
                            sx={{
                              color: "white",
                              backgroundColor:
                                item.itemType === "package"
                                  ? "#9c27b0"
                                  : "#2196f3",
                              px: 1,
                              py: 0.5,
                              borderRadius: "4px",
                              fontSize: { xs: "0.65rem", sm: "0.75rem" },
                            }}
                          >
                            {item.itemType === "package" ? "Package" : "Test"}
                          </Typography>
                        </Box>

                        {item.quantity > 1 && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: "white",
                              backgroundColor: "#ff7043",
                              px: 1,
                              py: 0.5,
                              borderRadius: "4px",
                              fontSize: { xs: "0.65rem", sm: "0.75rem" },
                            }}
                          >
                            Qty: {item.quantity}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: { xs: "row", md: "column" },
                      alignItems: { xs: "center", md: "flex-end" },
                      justifyContent: "space-between",
                      width: { xs: "100%", md: "auto" },
                      gap: { xs: 1, md: 3 },
                      minWidth: { xs: "auto", md: 150 },
                      mt: { xs: 1, md: 0 },
                      pt: { xs: 1, md: 0 },
                      borderTop: { xs: "1px solid #f0f0f0", md: "none" },
                    }}
                  >
                    <Box
                      sx={{
                        textAlign: { xs: "left", md: "right" },
                        mb: { xs: 0, md: 1 },
                      }}
                    >
                      {/* If discount percentage is null or 0, show only original price */}
                      {(!item.discountPercentage || item.discountPercentage <= 0) ? (
                        <Typography
                          variant={isMobile ? "subtitle1" : "h6"}
                          component="span"
                        >
                          ₹{item.originalPrice * item.quantity}
                        </Typography>
                      ) : (
                        // Otherwise show discounted price with strikethrough original price
                        <>
                          <Typography
                            variant={isMobile ? "subtitle1" : "h6"}
                            component="span"
                          >
                            ₹{item.discountedPrice * item.quantity}
                          </Typography>
                          <Typography
                            variant="body2"
                            component="span"
                            sx={{
                              textDecoration: "line-through",
                              color: "text.secondary",
                              ml: 1,
                              fontSize: { xs: "0.7rem", sm: "0.875rem" },
                            }}
                          >
                            ₹{item.originalPrice * item.quantity}
                          </Typography>
                          <Typography
                            variant="body2"
                            component="span"
                            sx={{
                              color: "success.main",
                              ml: 1,
                              fontSize: { xs: "0.7rem", sm: "0.875rem" },
                            }}
                          >
                            {item.discount}
                          </Typography>
                        </>
                      )}
                    </Box>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Button
                        variant="outlined"
                        color="error"
                        onClick={() => handleRemoveItem(item.id, item.itemType)}
                        sx={{
                          borderRadius: "8px",
                          textTransform: "none",
                          minWidth: { xs: "80px", sm: "100px" },
                          px: { xs: 1, sm: 2 },
                          py: { xs: 0.25, sm: 0.5 },
                          fontSize: { xs: "0.7rem", sm: "0.875rem" },
                        }}
                      >
                        Remove
                      </Button>
                    </Box>
                  </Box>
                </Box>
              ))
            )}
          </Box>

          {cartItems.length > 0 ? (
            <Box
              sx={{
                px: { xs: 1.5, sm: 2 },
                py: { xs: 1, sm: 1.5 },
                borderTop: "1px solid #e0e0e0",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: { xs: 0.5, sm: 1 },
                }}
              >
                <Typography
                  variant={isMobile ? "subtitle1" : "h6"}
                  sx={{ color: "text.black" }}
                >
                  Total Amount:
                </Typography>
                <Box sx={{ textAlign: "right" }}>
                  <Typography
                    variant={isMobile ? "subtitle1" : "h6"}
                    color="text.black"
                  >
                    ₹{total}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="success.main"
                    sx={{ fontSize: { xs: "0.65rem", sm: "0.75rem" } }}
                  >
                    You save ₹
                    {cartItems.reduce(
                      (sum, item) => {
                        // Only calculate savings if there's a discount
                        if (item.discountPercentage && item.discountPercentage > 0) {
                          return sum + ((item.originalPrice - item.discountedPrice) * item.quantity);
                        }
                        return sum; // No savings if no discount
                      },
                      0
                    )}
                  </Typography>
                </Box>
              </Box>
              <Button
                variant="contained"
                fullWidth
                onClick={handleProceedToCheckout}
                sx={{
                  py: { xs: 1, sm: 1.5 },
                  borderRadius: 2,
                  textTransform: "none",
                  fontSize: { xs: "0.9rem", sm: "1.1rem" },
                }}
              >
                <Typography
                  sx={{ fontWeight: 500, color: theme.palette.text.primary }}
                >
                  Proceed to Checkout
                </Typography>
              </Button>
            </Box>
          ) : (
            <Box sx={{ p: { xs: 1.5, sm: 2 } }}>
              <Button
                variant="contained"
                fullWidth
                color="primary"
                onClick={onClose}
                sx={{
                  py: { xs: 1, sm: 1.5 },
                  borderRadius: 2,
                  textTransform: "none",
                  fontSize: { xs: "0.9rem", sm: "1.1rem" },
                }}
              >
                <Typography
                  sx={{ fontWeight: 500, color: theme.palette.text.primary }}
                >
                  Book Lab Test
                </Typography>
              </Button>
            </Box>
          )}
        </Box>
      </Slide>

      {/* OTP Verification Modal */}
      <OtpVerificationModal
        open={otpModalOpen}
        onClose={() => setOtpModalOpen(false)}
        cartItems={cartItems}
        onSuccess={handleOtpSuccess}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default NavbarCart;
