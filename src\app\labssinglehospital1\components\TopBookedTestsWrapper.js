import React from 'react';
import { getEnterpriseCode } from "../blogs/location/[locationCode]/[blogCode]/layout";
import TopBookedTests from './TopBookedTests';
import { getLabTests } from '@/api/harbor.service';

const TopBookedTestsWrapper = async () => {
  // Start with loading state
  const loadingComponent = <TopBookedTests isLoading={true} testsData={{ data: [], totalCount: 0 }} />;

  try {
    // Get the enterprise code
    const enterpriseCode = await getEnterpriseCode();

    // Fetch initial data (first page)
    const initialData = await getLabTests(enterpriseCode, { page: 1, perPage: 10 });

    // If no data, don't render the component
    if (!initialData?.data || initialData.data.length === 0) {
      return null;
    }

    // Pass the initial data and enterprise code to the client component
    return (
      <TopBookedTests
        testsData={initialData}
        isLoading={false}
        enterpriseCode={enterpriseCode}
      />
    );
  } catch (error) {
    console.error("Error in TopBookedTestsWrapper:", error);
    return loadingComponent;
  }
};

export default TopBookedTestsWrapper;
