"use client";

import {Typography, Box} from "@mui/material";
import Image from "next/image";
import {getThumborUrl} from "../../utils/getThumborUrl";
import {NextArrow, PrevArrow} from "./photosSection";
import Slider from "react-slick";
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const HomepageBanner = ({banners = []}) => {
  const settings = {
    dots: true,
    infinite: true,
    adaptiveHeight: true,
    lazyLoad: false,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    speed: 500,
    autoplaySpeed: 3000,
    cssEase: "linear",
    pauseOnHover: true,
    arrows: false,
    nextArrow: <NextArrow/>,
    prevArrow: <PrevArrow/>,
  };

  const dummyBanners = [
    {
      image_url: "https://ips.docfyn.com/unsafe/${width}x${height}/staging.mydocsite/documents/2024/03/28/cd4a831e64e3383bb6eb1d8bc37b2d0692f24d20/ortho_banner.webp",
      title: "Health is Wealth",
      text: "Sharing is caring. Blah Blah Blash..."
    },
    {
      image_url: "https://ips.docfyn.com/unsafe/${width}x${height}/staging.mydocsite/documents/2024/02/29/d532df0cc61a34e51b6d896f43ef84caa1ae0890/pexels-photo-4269362.webp",
      title: "Your Health, Our Priority",
      text: ""
    },
    {
      image_url: "https://ips.docfyn.com/unsafe/${width}x${height}/staging.mydocsite/documents/2023/12/28/b5d819f49c2bfd432928b91ebef69f19e18b9fad/img-20231227-wa0005.webp",
      title: "",
      text: ""
    }
  ]

  return (
      <div
          style={{
            position: "relative",
            //height: "500px",
            display: "flex",
            justifyContent: "center",
            width: "100%",
          }}
      >
        <Slider {...settings} style={{width: "100%", height: "100%"}}>
          {banners.map((banner, index) => {
            const {
              image_url: bannerImg = "",
              title = "",
              text = "",
            } = banner || {};
            return (
                <Box sx={{position: "relative", width: "100%"}}>
                  <Image
                      alt="slider1"
                      src={getThumborUrl(bannerImg)}
                      width={1920}
                      height={1080}
                      objectFit="cover"
                      priority={true}
                      style={{
                        height: "auto",
                        width: "100%",
                      }}
                  />
                  <Box sx={{
                    position: "absolute",
                    left: "50%",
                    top: "50%",
                    transform: "translate(-50%, -50%)",
                    textAlign: 'center'
                  }}>
                    <Typography
                        variant="h3"
                        sx={{
                          fontSize: {xs: '1.5rem', sm: '2rem', md: '3rem'},
                          color: 'text.primary',
                          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                        }}
                    >
                      {title || ""}
                    </Typography>
                    <Typography
                        variant="subtitle1"
                        sx={{
                          fontSize: {xs: '1rem', sm: '1.25rem', md: '1.5rem'},
                          color: 'text.primary',
                          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
                        }}
                    >
                      {text || ""}
                    </Typography>
                  </Box>
                </Box>

            );
          })}
        </Slider>
      </div>
  );
};

export default HomepageBanner;