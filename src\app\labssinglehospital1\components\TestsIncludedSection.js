'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const TestsIncludedSection = ({ testsIncluded, totalTests, itemType }) => {
  // State to track if the main section is expanded
  const [mainExpanded, setMainExpanded] = useState(false);

  // Check if testsIncluded is an array of objects or a string
  const isArrayOfObjects = Array.isArray(testsIncluded) && testsIncluded.length > 0 && typeof testsIncluded[0] === 'object';

  // State is used to control the expansion of the main accordion

  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: '12px', border: '1px solid #e0e0e0' }}>
      {/* Main Accordion for the entire section */}
      <Accordion
        elevation={0}
        disableGutters
        expanded={mainExpanded}
        onChange={() => setMainExpanded(!mainExpanded)}
        sx={{
          '&:before': { display: 'none' },
          border: 'none',
          boxShadow: 'none',
          m: 0,
          p: 0
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            p: 0,
            minHeight: '48px',
            '& .MuiAccordionSummary-content': {
              margin: 0
            }
          }}
        >
          <Typography variant="h6" fontWeight="medium" color="text.black">
            Test(s) Included ({totalTests})
          </Typography>
        </AccordionSummary>

        <AccordionDetails sx={{ p: 0, pt: 2 }}>
          {isArrayOfObjects ? testsIncluded.map((test, index) => (
            <Accordion
              key={index}
              elevation={0}
              disableGutters
              sx={{
                '&:before': { display: 'none' },
                borderBottom: index < testsIncluded.length - 1 ? '1px solid #f0f0f0' : 'none',
                mb: 0
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{ px: 1 }}
              >
                <Typography fontWeight="medium" color="text.black">
                  {test.name}
                </Typography>
                {test.count >= 1 && (
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    {test.count} {test.count === 1 ? 'Test' : 'Tests'} Included
                  </Typography>
                )}
              </AccordionSummary>
              <AccordionDetails sx={{ px: 1, pt: 0, pb: 2 }}>
                {/* If this is a package with labTestItems, show them in a clean list */}
                {test.labTestItems && test.labTestItems.length > 0 ? (
                  <Box sx={{ mt: 0.5 }}>
                    {test.labTestItems.map((item, itemIndex) => (
                      <Box
                        key={itemIndex}
                        sx={{
                          py: 1,
                          display: 'flex',
                          alignItems: 'flex-start',
                          borderBottom: itemIndex < test.labTestItems.length - 1 ? '1px solid #f5f5f5' : 'none',
                        }}
                      >
                        <Box
                          sx={{
                            width: '6px',
                            height: '6px',
                            borderRadius: '50%',
                            backgroundColor: 'primary.main',
                            mt: 0.7,
                            mr: 1.5,
                            flexShrink: 0
                          }}
                        />
                        <Box>
                          <Typography
                            variant="body2"
                            color="text.black"
                            sx={{ fontWeight: item.shortDescription ? 'medium' : 'normal' }}
                          >
                            {item.name}
                          </Typography>
                          {item.shortDescription && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{ display: 'block', mt: 0.5 }}
                            >
                              {item.shortDescription}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    {test.description || 'No detailed description available for this test.'}
                  </Typography>
                )}
              </AccordionDetails>
            </Accordion>
          )) : (
            <Box sx={{ p: 1 }}>
              <Typography variant="body2" color="text.secondary">
                No detailed test information available. This {itemType || 'item'} includes a total of {totalTests} tests.
              </Typography>
            </Box>
          )}
        </AccordionDetails>
      </Accordion>

      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 2, fontStyle: 'italic' }}>
        *Conditional Tests: Testing of these is conditional depending on results of other tests
      </Typography>
    </Paper>
  );
};

export default TestsIncludedSection;
