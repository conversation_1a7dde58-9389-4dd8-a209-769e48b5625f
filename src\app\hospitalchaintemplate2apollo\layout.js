import { AppRouter<PERSON>acheProvider } from "@mui/material-nextjs/v14-appRouter";
import { Inter} from "next/font/google";
import "../globals.css";
import "../fonts.css";
import 'ckeditor5/ckeditor5.css';
import AppContextLayout from "../AppContextLayout";
import { getThumborUrl } from "../utils/getThumborUrl";
import { GoogleTagManager, GoogleAnalytics } from "@next/third-parties/google";
import Structure from "@/app/hospitalchaintemplate2apollo/components/structure";
import {getHostForServerComponent} from "@/app/utils/serverOnly/serverUtils";
import CustomThemeProviderApolloChainTemplate2 from "@/app/hospitalchaintemplate2apollo/CustomThemeProviderApolloChainTemplate2";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PLUGINS,
} from "@/constants";
import {fetchWebsiteData, getProceduresList} from "@/api/harbor.service";

const inter = Inter({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});
export const generateMetadata = async () => {
  const domainName = getHostForServerComponent()
  try {
    const data = await fetchWebsiteData({domainName: domainName})
    if (data.code === 200){
      const { seoData = [] } = data?.result || {};
      const { title = "", meta_description: metaDescription = "" } =
      seoData[0] || {};
      return {
        title: title || "My Hospital",
        description:
            metaDescription ||
            "A trusted platform for personalized patient care, offering tailored solutions and comprehensive services, prioritising comfort and satisfaction.",
      };
    }
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

const getWebsiteData = async () => {
  const domainName = getHostForServerComponent()
  try {
    const data  = await fetchWebsiteData({domainName: domainName})
    if (data.code === 200){
      const { result = {}} = data;
      return result;
    }
  }catch (error){
    console.log(error)
  }
};

const getPlugins = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PLUGINS}?details=true`;
  try {
    const res = await fetch(url);
    const jsonRes = await res.json();
    return jsonRes?.result;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function RootLayout({ children }) {
  const websiteData = await getWebsiteData();
  const {
    template = {},
    favicon_url: faviconUrl = "",
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};
  const {
    primary_font_family_cdn: fontFamilyCdn = "",
    primary_font_family: fontFamily = "",
  } = template || {};
  const configData = await getPlugins(enterpriseCode);
  const { ga = [], gtm = [] } = configData || {};
  const { configValue: gaConfigValue = null } = ga[0] || {};
  const { configValue: gtmConfigValue = null } = gtm[0] || {};

  return (
    <html lang="en">
      <link rel="icon" href={getThumborUrl(faviconUrl, 32, 32)} sizes="any" />
      <body style={{ height: "100%" }} className={inter.variable}>
        <AppRouterCacheProvider options={{ key: "css" }}>
          <AppContextLayout websiteData={websiteData}>
            <CustomThemeProviderApolloChainTemplate2 template={template} fontFamily={fontFamily}>
              <Structure children={children} />
            </CustomThemeProviderApolloChainTemplate2>
          </AppContextLayout>
        </AppRouterCacheProvider>
        {gaConfigValue && (
            <GoogleAnalytics gaId={gaConfigValue} strategy="afterInteractive" />
        )}
        {gtmConfigValue && (
            <GoogleTagManager gtmId={gtmConfigValue} strategy="afterInteractive" />
        )}
      </body>
    </html>
  );
}
