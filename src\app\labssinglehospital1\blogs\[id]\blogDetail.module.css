
/* Responsive table styles - only apply scrolling when needed */
.blogContent :global(.ck-content table) {
  width: 100% !important;
  margin-bottom: 1rem;
  border-collapse: collapse;
  border: 1px solid #ddd;
}

/* Only apply horizontal scrolling when table is wider than container */
@media (max-width: 768px) {
  .blogContent :global(.ck-content table) {
    display: block;
    overflow-x: auto;
    max-width: 100%;
    -webkit-overflow-scrolling: touch;
  }
}

.blogContent :global(.ck-content table td),
.blogContent :global(.ck-content table th) {
  padding: 0.75rem;
  border: 1px solid #ddd;
  word-break: normal;
}

.blogContent :global(.ck-content table th) {
  background-color: #f5f5f5;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 1;
}

/* Make sure the table takes up the full width of its container */
.blogContent :global(.ck-content figure.table) {
  width: 100% !important;
  max-width: 100% !important;
  margin: 1rem 0;
}

/* Handle CKEditor specific table wrappers */
.blogContent :global(.ck-content .table) {
  max-width: 100%;
}

/* Default table layout */
.blogContent :global(.ck-content table) {
  width: 100%;
  border-collapse: collapse;
}

/* For tables that need to fit content width */
.blogContent :global(.ck-content table.fit-content) {
  table-layout: auto;
  width: auto !important;
}

/* For tables with many columns */
.blogContent :global(.ck-content table.wide-table) {
  table-layout: auto; /* Let the browser determine column widths based on content */
}

/* For tables that fit within the container */
.blogContent :global(.ck-content table:not(.wide-table)) {
  table-layout: fixed; /* Equal column distribution */
}

/* Add a subtle shadow to indicate scrollable content */
.blogContent :global(.ck-content table::-webkit-scrollbar) {
  height: 6px;
}

.blogContent :global(.ck-content table::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 10px;
}

.blogContent :global(.ck-content table::-webkit-scrollbar-thumb) {
  background: #888;
  border-radius: 10px;
}

.blogContent :global(.ck-content table::-webkit-scrollbar-thumb:hover) {
  background: #555;
}

/* Handle nested tables or complex table structures */
.blogContent :global(.ck-content table table) {
  margin: 0;
  border: none;
}

.blogContent :global(.ck-content table table td),
.blogContent :global(.ck-content table table th) {
  padding: 0.5rem;
}

/* Mobile-specific table styles */
@media (max-width: 768px) {
  .blogContent :global(.ck-content table) {
    font-size: 0.9rem; /* Slightly smaller font on mobile */
    box-shadow: 0 0 5px rgba(0,0,0,0.1);
  }

  .blogContent :global(.ck-content table td),
  .blogContent :global(.ck-content table th) {
    padding: 0.5rem; /* Smaller padding on mobile */
  }

  /* Only apply white-space nowrap to tables that need horizontal scrolling */
  .blogContent :global(.ck-content table.wide-table td),
  .blogContent :global(.ck-content table.wide-table th) {
    white-space: nowrap;
  }

  /* For tables with many columns that need scrolling */
  .blogContent :global(.ck-content table.wide-table) {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    width: 100%;
  }

  /* For tables that fit within the screen width */
  .blogContent :global(.ck-content table:not(.wide-table)) {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  /* For tables that fit within the screen width, allow text wrapping */
  .blogContent :global(.ck-content table:not(.wide-table) td),
  .blogContent :global(.ck-content table:not(.wide-table) th) {
    white-space: normal;
    word-wrap: break-word;
  }
}
