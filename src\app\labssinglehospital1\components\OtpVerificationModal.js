'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  useTheme,
  useMediaQuery,
  Stack
} from '@mui/material';
import axios from 'axios';
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_OTP,
  API_ENDPOINT_REQUEST,
  API_ENDPOINT_VERIFY
} from '@/constants';
import { getEnterpriseCode } from '@/api/enterprise.service';

// Import modular components
import DialogHeader from './otp/DialogHeader';
import ProgressIndicator from './otp/ProgressIndicator';
import PhoneNumberStep from './otp/PhoneNumberStep';
import OtpVerificationStep from './otp/OtpVerificationStep';
import UserDetailsStep from './otp/UserDetailsStep';
import SuccessStep from './otp/SuccessStep';
import CustomSnackbar from './otp/CustomSnackbar';
import { Transition, formatPhoneNumber } from './otp/utils';

const OtpVerificationModal = ({ open, onClose, cartItems, onSuccess }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [step, setStep] = useState(1); // 1: Phone entry, 2: OTP verification, 3: User details, 4: Success
  const [phone, setPhone] = useState('');
  const [dialCode, setDialCode] = useState('+91'); // Default to India
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false); // Flag to prevent duplicate verification attempts
  const [error, setError] = useState('');
  const [enterpriseCode, setEnterpriseCode] = useState('');
  const [labBookingCode, setLabBookingCode] = useState('');
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'error'
  });

  // OTP input ref will be defined later

  // Fetch enterprise code on component mount
  useEffect(() => {
    const fetchEnterpriseCode = async () => {
      try {
        const code = await getEnterpriseCode();
        setEnterpriseCode(code);
      } catch (error) {
        console.error('Error fetching enterprise code:', error);
        setSnackbar({
          open: true,
          message: 'Error fetching enterprise code. Please try again.',
          severity: 'error'
        });
      }
    };

    fetchEnterpriseCode();
  }, []);

  // Reset state when modal is opened
  useEffect(() => {
    if (open) {
      setStep(1);
      setPhone('');
      setOtp('');
      setError('');
      setIsVerifying(false);
    }
  }, [open]);

  // Format cart items for the API request
  const formatCartItemsForRequest = () => {

    // Initialize arrays for tests and packages
    let tests = [];
    let packages = [];

    // Process each cart item
    cartItems.forEach(item => {

      const code = item.code || item.id || '';

      const isPackage =
        item.type === 'package' ||
        item.itemType === 'package';

      if (isPackage) {
        packages.push({ code });
      } else {
        tests.push({ code });
      }
    });


    // Return the appropriate structure based on what we have
    if (tests.length > 0 && packages.length > 0) {
      return { tests, packages };
    } else if (tests.length > 0) {
      return { tests };
    } else {
      return { packages };
    }
  };

  // Handle phone number input
  const handlePhoneChange = (e) => {
    const value = e.target.value;
    // Only allow numbers and limit to 10 digits
    if (/^\d*$/.test(value) && value.length <= 10) {
      setPhone(value);
      setError('');
    }
  };

  // Handle OTP input
  const handleOtpChange = (e) => {
    const value = e.target.value;

    // Only allow numbers and limit to 6 digits
    if (/^\d*$/.test(value) && value.length <= 6) {
      // Always update the OTP value
      setOtp(value);

      // Only clear validation errors when typing, not server errors
      if (error === 'Please enter a valid 6-digit OTP') {
        setError('');
      }

      // Auto-focus next input or submit when all digits are entered
      if (value.length === 6 && !isLoading && !isVerifying) {
        // Set verifying flag to prevent duplicate verification attempts
        setIsVerifying(true);

        // Small delay to allow state update
        setTimeout(() => {
          // Only clear validation errors before verification, not server errors
          if (error === 'Please enter a valid 6-digit OTP') {
            setError('');
          }
          handleVerifyOtp();
          // Reset the verifying flag after a longer timeout to prevent rapid re-attempts
          setTimeout(() => {
            setIsVerifying(false);
          }, 1000);
        }, 300);
      }
    }
  };

  // Handle clicking on a specific OTP digit box
  const handleOtpBoxClick = (index) => {
    // Focus the hidden input
    otpInputRef.current?.focus();

    // If clicking on a box beyond the current input length, position cursor at the end
    if (index > otp.length) {
      // Keep current value
    } else if (index < otp.length) {
      // Set cursor position by recreating the value up to that point
      setOtp(otp.substring(0, index));
    }
  };

  // Request OTP
  const handleRequestOtp = async () => {

    if (phone.length !== 10) {
      setError('Please enter a valid 10-digit phone number');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Prepare the request body
      const formattedItems = formatCartItemsForRequest();

      const requestBody = {
        phone: `${dialCode.replace('+', '')}${phone}`,
        ...formattedItems  // This will spread tests and/or packages as needed
      };


      // Make the API call
      const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_OTP}${API_ENDPOINT_REQUEST}`;

      const response = await axios.post(url, requestBody, {
        params: {
          enterpriseCode
        },
        headers: {
          'Content-Type': 'application/json',
          'source': 'website'
        }
      });


      // Check if the request was successful
      if (response.status >= 200 && response.status < 300) {
        // Store the labBookingCode if it's in the response
        if (response.data?.result?.labBookingCode) {
          setLabBookingCode(response.data.result.labBookingCode);
        }

        setStep(2); // Move to OTP verification step
        setSnackbar({
          open: true,
          message: 'OTP sent successfully!',
          severity: 'success'
        });
      } else {
        setError('Failed to send OTP. Please try again.');
      }
    } catch (error) {
      console.error('Error requesting OTP:', error);
      setError(error.response?.data?.message || 'Failed to send OTP. Please try again.');
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Failed to send OTP. Please try again.',
        severity: 'error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOtp = async () => {

    // Only clear validation errors, not server errors
    if (error === 'Please enter a valid 6-digit OTP') {
      setError('');
    }

    // Prevent duplicate verification attempts
    if (isVerifying || isLoading) {
      return;
    }

    // Only validate length if it's not exactly 6 digits
    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }


    // Set both loading and verifying flags
    setIsLoading(true);
    setIsVerifying(true);

    // Clear any existing error when verifying
    setError('');

    try {
      // Prepare the request body
      const requestBody = {
        isLabBookingFlow: true,
        otp,
        phone: `${dialCode.replace('+', '')}${phone}`
      };


      // Make the API call
      const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_OTP}${API_ENDPOINT_VERIFY}`;

      const response = await axios.post(url, requestBody, {
        params: {
          enterpriseCode,
          labBookingCode
        },
        headers: {
          'Content-Type': 'application/json',
          'source': 'website'
        }
      });


      // Check if the request was successful
      if (response.data?.success === true || response.status >= 200 && response.status < 300) {
        // Clear any error before moving to user details step
        setError('');
        setStep(3); // Move to user details step
        // Don't show the OTP verification success snackbar
        // as we're moving to the next step
      } else if (response.data?.error) {
        // Handle error from API response
        setError(response.data.error || 'Failed to verify OTP. Please try again.');
      } else {
        setError('Failed to verify OTP. Please try again.');
      }
    } catch (error) {
      // Extract error messages from the response
      const responseData = error.response?.data || {};

      // Check if it's an invalid OTP error
      let errorMessage;

      // Extract error messages from the response
      const errorMessages = responseData.error?.errors || [];
      const nestedErrorMessage = errorMessages.length > 0 ? errorMessages[0] : '';

      // Also check standard error locations
      const responseMessage = responseData.message || '';
      const errorMessage1 = responseData.error?.message || '';

      // Combine all possible error sources for checking
      const errorText = (responseMessage + ' ' + errorMessage1 + ' ' + nestedErrorMessage).toLowerCase();

      // Check for specific error patterns
      if (nestedErrorMessage === 'Incorrect OTP Entered !!' ||
          errorText.includes('incorrect otp') ||
          errorText.includes('invalid otp') ||
          errorText.includes('wrong otp') ||
          errorText.includes('otp mismatch') ||
          errorText.includes('otp does not match')) {
        errorMessage = 'The OTP you entered is incorrect. Please try again.';
      } else if (errorText.includes('expired')) {
        errorMessage = 'This OTP has expired. Please request a new one.';
      } else if (errorText.includes('internal server error') ||
                errorText.includes('500')) {
        // Check if we have a more specific error message in the nested structure
        if (nestedErrorMessage) {
          errorMessage = 'The OTP you entered is incorrect. Please try again.';
        } else {
          errorMessage = 'We encountered a problem verifying your OTP. Please try again.';
        }
      } else {
        errorMessage = 'Failed to verify OTP. Please try again.';
      }


      // Set the error message
      setError(errorMessage);

      // Show snackbar with error
      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });

    } finally {
      setIsLoading(false);
      // Reset the verifying flag after a short delay to prevent rapid re-attempts
      setTimeout(() => {
        setIsVerifying(false);
      }, 500);
    }
  };

  // Handle user name change
  const handleUserNameChange = (e) => {
    setUserName(e.target.value);
  };

  // Handle user email change
  const handleUserEmailChange = (e) => {
    setUserEmail(e.target.value);
  };

  // Skip user details and proceed to success
  const handleSkipUserDetails = () => {
    // Move directly to success step without validation
    setStep(4);

    // Call the onSuccess callback
    if (onSuccess) {
      onSuccess();
    }
  };

  // Handle user details submission with validation
  const handleSubmitUserDetails = async (skipValidation = false) => {
    // If skipping validation, just proceed to success
    if (skipValidation) {
      handleSkipUserDetails();
      return;
    }

    // Validate that at least one field is filled
    if (!userName && !userEmail) {
      setError('Please enter your name or email to continue.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Prepare the request body for the PATCH API
      const requestBody = {
        name: userName,
        email: userEmail
      };

      // Make the API call to update user details
      const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}/lab/book`;

      const response = await axios.patch(url, requestBody, {
        params: {
          labBookingCode,
          enterpriseCode
        },
        headers: {
          'Content-Type': 'application/json',
          'source': 'website'
        }
      });

      // Check if the request was successful
      if (response.status >= 200 && response.status < 300) {
        // Move to success step
        setStep(4);

        // Call the onSuccess callback
        if (onSuccess) {
          onSuccess(response.data);
        }
      } else {
        setError('Failed to update your details. Please try again.');
      }
    } catch (error) {
      console.error('Error updating user details:', error);
      setError('Failed to update your details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = (_, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  // Create refs for OTP input fields
  const otpInputRef = useRef(null);

  // formatPhoneNumber is now imported from utils

  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={step === 4 ? () => {
          // Only close if on success screen, and trigger the onSuccess callback again
          // to ensure cart is cleared when user manually closes the dialog
          if (onSuccess) {
            onSuccess();
          }
          onClose();
        } : onClose}
        fullScreen={isMobile}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: isMobile ? 0 : 3,
            height: isMobile ? '100%' : 'auto',
            maxHeight: isMobile ? '100%' : '90vh',
            overflowY: 'auto',
            background: '#ffffff',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)'
          }
        }}
      >
        {/* Header with step indicator */}
        {/* Dialog Header */}
        <DialogHeader step={step} setStep={setStep} onClose={onClose} />

        {/* Progress Indicator */}
        <ProgressIndicator currentStep={step} theme={theme} />

        <DialogContent sx={{ p: { xs: 2, sm: 3 }, pt: { xs: 1, sm: 2 } }}>
          <Stack spacing={3}>
            {/* Step 1: Phone Number Input */}
            {step === 1 && (
              <PhoneNumberStep
                phone={phone}
                dialCode={dialCode}
                error={error}
                isLoading={isLoading}
                handlePhoneChange={handlePhoneChange}
                setDialCode={setDialCode}
                handleRequestOtp={handleRequestOtp}
                theme={theme}
                formatPhoneNumber={formatPhoneNumber}
              />
            )}

            {/* Step 2: OTP Verification */}
            {step === 2 && (
              <OtpVerificationStep
                phone={phone}
                dialCode={dialCode}
                otp={otp}
                error={error}
                isLoading={isLoading}
                otpInputRef={otpInputRef}
                handleOtpChange={handleOtpChange}
                handleOtpBoxClick={handleOtpBoxClick}
                handleVerifyOtp={handleVerifyOtp}
                setStep={setStep}
                theme={theme}
                formatPhoneNumber={formatPhoneNumber}
              />
            )}

            {/* Step 3: User Details */}
            {step === 3 && (
              <UserDetailsStep
                phone={phone}
                dialCode={dialCode}
                userName={userName}
                userEmail={userEmail}
                error={error}
                isLoading={isLoading}
                handleUserNameChange={handleUserNameChange}
                handleUserEmailChange={handleUserEmailChange}
                handleSubmitUserDetails={handleSubmitUserDetails}
                handleSkipUserDetails={handleSkipUserDetails}
                formatPhoneNumber={formatPhoneNumber}
              />
            )}

            {/* Step 4: Success Screen */}
            {step === 4 && (
              <SuccessStep
                phone={phone}
                dialCode={dialCode}
                onSuccess={onSuccess}
                onClose={onClose}
                theme={theme}
                formatPhoneNumber={formatPhoneNumber}
              />
            )}
          </Stack>
        </DialogContent>
      </Dialog>

      {/* Snackbar will be rendered directly to document.body via createPortal */}
      <CustomSnackbar
        open={snackbar.open}
        message={snackbar.message}
        severity={snackbar.severity}
        onClose={handleSnackbarClose}
      />
    </>
  );
};

export default OtpVerificationModal;
