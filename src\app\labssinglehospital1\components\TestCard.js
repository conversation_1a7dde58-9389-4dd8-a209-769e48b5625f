"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  alpha,
  useTheme,
  Divider,
} from "@mui/material";
import { useDispatch, useSelector } from 'react-redux';
import { addToCart, removeFromCart, selectIsItemInCart } from '../redux/slices/cartSlice';
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const TestCard = ({ test }) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();

  // Check if this item is already in the cart (specifying it's a test)
  const isInCart = useSelector(selectIsItemInCart(test.id, 'test'));

  const handleAddToCart = (e) => {
    e.stopPropagation(); // Prevent navigation when clicking the Add button

    // Calculate discount percentage if not provided
    const discountPercentage = test.discountPercentage ||
      (test.originalPrice && test.discountedPrice ?
        Math.round(((test.originalPrice - test.discountedPrice) / test.originalPrice) * 100) : 0);

    dispatch(addToCart({
      ...test,
      itemType: 'test', // Specify this is a test, not a package
      title: test.name || test.title || 'Test',
      description: test.description || test.shortDescription || test.alternativeNames,
      totalTests: test.totalTestsIncluded || test.labTestItems?.length || 1,
      discount: test.discount || `${discountPercentage}% off`,
      discountPercentage: discountPercentage
    }));
  };

  const handleRemoveFromCart = (e) => {
    e.stopPropagation(); // Prevent navigation when clicking the Remove button
    dispatch(removeFromCart({ id: test.id, itemType: 'test' }));
  };

  const handleCardClick = () => {
    // Use seoSlug if available, otherwise generate a slug from the name/title
    const slug = test.seoSlug || (test.name || test.title).toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    router.push(`/tests/${slug}`);
  };

  return (
    <Box
      onClick={handleCardClick}
      sx={{
        p: { xs: 2, md: 2 },
        cursor: "pointer",
        height: "100%",
        minHeight: "250px",
        backgroundColor: "white",
        borderRadius: "12px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        transition: "all 0.2s ease",
        "&:hover": {
          transform: "translateY(-2px)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
        },
      }}
    >
      <Box>
        <Box
          sx={{
            display: "flex",
            gap: 2,
            mb: 2,
          }}
        >
          <Box
            sx={{
              width: { xs: 60, md: 80 },
              height: { xs: 60, md: 80 },
              minWidth: { xs: 60, md: 80 },
              borderRadius: "12px",
              // backgroundColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              border: `1px solid ${theme.palette.primary.main}`,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: { xs: "32px", md: "48px" }
            }}
          >
            {test.iconUrl ? (
              <Image
                src={test.iconUrl}
                alt={`${test.name || test.title || 'Test'} icon`}
                width={80}
                height={80}
                style={{ objectFit: "contain" }}
              />
            ) : (
              test.icon || '🔬'
            )}
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="subtitle1"
              fontWeight="medium"
              sx={{
                mb: 0.5,
                color: "text.black",
                height: "2.5rem",
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: 1.25
              }}
            >
              {test.name || test.title || 'Test'}
            </Typography>
            {test.description && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mb: 1,
                  fontSize: '0.8rem',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  lineHeight: 1.3
                }}
              >
                {test.description}
              </Typography>
            )}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {/* Show tests included tag if there are 1 or more tests included */}
              {(test.totalTestsIncluded >= 1 || (test.labTestItems && test.labTestItems.length >= 1)) && (
                <Typography
                  variant="caption"
                  sx={{
                    backgroundColor: theme.palette.primary.main,
                    px: 1,
                    py: 0.5,
                    borderRadius: "4px",
                    color: theme.palette.text.primary,
                    fontSize: "0.7rem",
                  }}
                >
                  {`${test.totalTestsIncluded || test.labTestItems?.length} ${(test.totalTestsIncluded || test.labTestItems?.length) === 1 ? 'Test' : 'Tests'} included`}
                </Typography>
              )}
              <Typography
                variant="caption"
                sx={{
                  color: 'white',
                  backgroundColor: '#2196f3',
                  px: 1,
                  py: 0.5,
                  borderRadius: '4px',
                  fontSize: '0.7rem',
                }}
              >
                Test
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box>
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
            {/* If discount percentage is null or 0, show only original price */}
            {(!test.discountPercentage || test.discountPercentage <= 0) ? (
              <Typography
                variant="h6"
                component="span"
                sx={{
                  fontSize: { xs: "1.1rem", md: "1.25rem" },
                  fontWeight: "bold",
                  color: "text.black",
                  mr: 1,
                }}
              >
                {test.currency?.symbol || '₹'}{test.originalPrice}
              </Typography>
            ) : (
              // Otherwise show discounted price with strikethrough original price
              <>
                <Typography
                  variant="h6"
                  component="span"
                  sx={{
                    fontSize: { xs: "1.1rem", md: "1.25rem" },
                    fontWeight: "bold",
                    color: "text.black",
                    mr: 1,
                  }}
                >
                  {test.currency?.symbol || '₹'}{test.discountedPrice}
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    textDecoration: "line-through",
                    color: "text.secondary",
                    fontSize: "0.8rem",
                  }}
                >
                  {test.currency?.symbol || '₹'}{test.originalPrice}
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    color: "success.main",
                    fontSize: "0.8rem",
                    fontWeight: "medium",
                  }}
                >
                  {test.discount || `${test.discountPercentage}% off`}
                </Typography>
              </>
            )}
          </Box>
          {isInCart ? (
            <Button
              variant="outlined"
              color="error"
              onClick={handleRemoveFromCart}
              sx={{
                borderRadius: "6px",
                textTransform: "none",
                minWidth: "80px",
                py: 0.75,
                fontSize: "0.9rem",
              }}
            >
              Remove
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleAddToCart}
              sx={{
                borderRadius: "6px",
                textTransform: "none",
                minWidth: "80px",
                py: 0.75,
                fontSize: "0.9rem",
                color: theme.palette.text.primary,
                bgcolor: theme.palette.primary.main,
                "&:hover": {
                  bgcolor: alpha(theme.palette.primary.main, 0.8),
                },
              }}
            >
              Add
            </Button>
          )}
        </Box>

        {(test.turnaroundTime || test.reportHours) && (
          <>
            <Divider sx={{ my: 1.5, borderColor: '#e0e0e0' }} />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <AccessTimeIcon sx={{ color: 'text.secondary', fontSize: '0.9rem' }} />
              <Typography
                variant="body2"
                sx={{
                  color: 'text.secondary',
                  fontSize: '0.8rem',
                  fontWeight: 'medium',
                  textAlign: 'left'
                }}
              >
                {test.turnaroundTime || `Reports within ${test.reportHours} hours`}
              </Typography>
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

export default TestCard;
