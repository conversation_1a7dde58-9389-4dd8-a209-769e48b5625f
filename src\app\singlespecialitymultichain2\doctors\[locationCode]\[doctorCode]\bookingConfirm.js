"use client";

import { Typography, Box } from "@mui/material";
import AppointmentSectionLayout from "./appointmentSectionLayout";
import PrimaryButton from "@/app/oasis/styledComponents/PrimaryButton";
import { useContext } from "react";
import { AppointmentSchedulerContext } from "./appointmentScheduler";
import { format, parse } from "date-fns";
import { SUCCESS_COLOR } from "@/constants";

const BookingConfirm = () => {
  const { selectedAppointment, bookingInfo, patientDetails, consultationFee, handleComponentDisplay, getAppointmentSlots } =
    useContext(AppointmentSchedulerContext);
  const { code: bookingCode = null, status: bookingStatus = null } =
    bookingInfo || {};
  const { ts: appointmentDateWithTime = "", st: startTime = "" } =
    selectedAppointment || {};
  const appointmentDate = appointmentDateWithTime.split(" ")[0] || "";
  const dateObject = new Date(appointmentDate);
  const parsedTime = parse(startTime, "HH:mm", new Date());
  const formattedStartTime = format(parsedTime, "h:mm aa");
  const formattedDated = format(dateObject, "d MMM y");

  const handleClose = () => {
    handleComponentDisplay(0);
    getAppointmentSlots();
  }

  return (
    <AppointmentSectionLayout>
      <Typography variant="subtitle1" align="center" sx={{ fontSize: "14px" }}>
        Appointment{" "}
        <span style={{ color: SUCCESS_COLOR, fontWeight: "500" }}>
          {bookingStatus || ""}
        </span>{" "}
        for {patientDetails["name"] || ""} -{" "}
        <span
          style={{ fontWeight: "500" }}
        >{`${formattedStartTime}, ${formattedDated}.`}</span>{" "}
        Please pay{" "}
        <span style={{ fontWeight: "500" }}>
          &#8377; {consultationFee || ""}
        </span>{" "}
        at the clinic.
      </Typography>
      <Box
        sx={{
          padding: "1rem",
          border: "1px solid #F5F5F5",
          borderRadius: "10px",
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          alignItems: "center",
          textAlign: "center",
        }}
      >
        <Typography variant="subtitle1" align="center">
          Your Booking ID is
        </Typography>
        <Typography
          variant="subtitle1"
          align="center"
          sx={{ fontSize: "20px", color: "primary.main" }}
        >
          {bookingCode || ""}
        </Typography>
        <Typography variant="subtitle1" align="center">
          We have sent an email to{" "}
          <span style={{ fontWeight: "500" }}>
            {patientDetails["email"] || ""}
          </span>{" "}
          with the details
        </Typography>
      </Box>
      <PrimaryButton
        sx={{
          width: "100%",
          borderRadius: "24px",
          textTransform: "none",
          boxShadow: "none",
        }}
        onClick={handleClose}
      >
        Close
      </PrimaryButton>
    </AppointmentSectionLayout>
  );
};

export default BookingConfirm;
