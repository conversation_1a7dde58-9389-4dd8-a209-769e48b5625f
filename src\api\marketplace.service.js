import {HARBOR_API_DOCFYN_DOMAIN} from "@/constants";

export const getPlugins = async (enterpriseCode) =>{
    try {
        const url = `${HARBOR_API_DOCFYN_DOMAIN}api/v1/public/enterprise/${enterpriseCode}/plugins?details=true`
        return  await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

    }catch (error){
        console.error('Error fetching plugins', error);
        throw error;
    }
}