"use client"

import { useState } from "react"
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown"
import Menu from "@mui/material/Menu"
import Typography from "@mui/material/Typography"
import Box from "@mui/material/Box"
import NavbarDropdownItem from "./navbarDropdownItem"
import { useRouter } from "next/navigation"
import { useTheme } from "@emotion/react"

const NavbarDropdown = ({ navbarItem = {}, isDrawerOpen = false, handleCloseDrawer, ...props }) => {
  const router = useRouter()
  const theme = useTheme()
  const [anchorEl, setAnchorEl] = useState(null)
  const open = Boolean(anchorEl)
  const { displayName = "", redirection = {}, iconUrl = null, sections = [], type = 1 } = navbarItem || {}

  const handleClick = (event) => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {}
      if (isDrawerOpen) handleCloseDrawer()
      setAnchorEl(null)
      if (type === 2) {
        window.open(redirectionUrl, "_blank")
      } else router.push(redirectionUrl)
    } else setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <li style={{ cursor: "pointer", listStyle: "none" }}>
      <Box
        sx={{
          fontSize: "inherit",
          display: "flex",
          alignItems: "center",
          gap: "4px",
          cursor: "pointer",
          "&:hover": { color: "text.black" },
          color: "text.black",
          fontWeight: 400,
          ...props?.sx,
        }}
        onClick={handleClick}
      >
        <Typography fontWeight={400} fontSize="16px">
          {displayName || ""}
        </Typography>
        {sections?.length > 0 && (
          <KeyboardArrowDownIcon
            fontSize="small"
            sx={{
              transition: "transform 0.2s ease",
              transform: open ? "rotate(180deg)" : "rotate(0deg)",
            }}
          />
        )}
      </Box>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
        sx={{
          mt: 1.5,
          "& .MuiPaper-root": {
            boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
            borderRadius: "8px",
            minWidth: "220px",
            padding: "8px 0",
            border: "1px solid #f0f0f0",
          },
        }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <Box
          sx={{
            maxHeight: "400px",
            display: "flex",
            flexDirection: "column",
            width: "100%",
            overflow: "auto",
          }}
        >
          {sections &&
            sections.map((section, index) => {
              const { displayName = "" } = section || {}
              return (
                <NavbarDropdownItem
                  key={`${displayName}${index}`}
                  section={section}
                  setAnchorEl={setAnchorEl}
                  isDrawerOpen={isDrawerOpen}
                  handleCloseDrawer={handleCloseDrawer}
                />
              )
            })}
        </Box>
      </Menu>
    </li>
  )
}

export default NavbarDropdown


// "use client";

// import React, { useState } from "react";
// import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
// import Menu from "@mui/material/Menu";
// import Typography from "@mui/material/Typography";
// import Box from "@mui/material/Box";
// import NavbarDropdownItem from "./navbarDropdownItem";
// import { useRouter } from "next/navigation";

// const NavbarDropdown = ({
//   navbarItem = {},
//   isDrawerOpen = false,
//   handleCloseDrawer,
//   ...props
// }) => {
//   const router = useRouter();
//   const [anchorEl, setAnchorEl] = useState(null);
//   const open = Boolean(anchorEl);
//   const {
//     displayName = "",
//     redirection = {},
//     iconUrl = null,
//     sections = [],
//     type = 1,
//   } = navbarItem || {};

//   const handleClick = (event) => {
//     if (!sections) {
//       const { redirectionUrl = "" } = redirection || {};
//       if (isDrawerOpen) handleCloseDrawer();
//       setAnchorEl(null);
//       if (type === 2) {
//         window.open(redirectionUrl, "_blank");
//       } else router.push(redirectionUrl);
//     } else setAnchorEl(event.currentTarget);
//   };

//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   return (
//     <li style={{ cursor: "pointer" }}>
//       <Box
//         sx={{
//           fontSize: "inherit",
//           display: "flex",
//           alignItems: "center",
//           gap: "4px",
//           // color: open ? "primary.main" : "inherit",
//           cursor: "pointer",
//           "&:hover": { color: "primary.main" },
//             color: "#333333",
//           ...props?.sx,
//         }}
//         onClick={handleClick}
//       >
//           <Typography
//               fontSize="16px"
//           >
//               {displayName || ""}
//           </Typography>
//         {sections?.length > 0 && <KeyboardArrowDownIcon fontSize="medium" />}
//       </Box>
//       <Menu
//         id="basic-menu"
//         anchorEl={anchorEl}
//         open={open}
//         onClose={handleClose}
//         MenuListProps={{
//           "aria-labelledby": "basic-button",
//         }}
//         sx={{ top: "16px" }}
//       >
//         <Box
//           sx={{
//             maxHeight: "400px",
//             display: "flex",
//             flexDirection: "column",
//             width: "100%",
//           }}
//         >
//           {sections &&
//             sections.map((section, index) => {
//               const { displayName = "" } = section || {};
//               return (
//                 <NavbarDropdownItem
//                   key={`${displayName}${index}`}
//                   section={section}
//                   setAnchorEl={setAnchorEl}
//                   isDrawerOpen={isDrawerOpen}
//                   handleCloseDrawer={handleCloseDrawer}
//                 />
//               );
//             })}
//         </Box>
//       </Menu>
//     </li>
//   );
// };

// export default NavbarDropdown;
