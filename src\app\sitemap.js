import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_BLOGS,
  API_ENDPOINT_DOCTORS,
  API_ENDPOINT_SPECIALITY,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PAGE,
  API_ENDPOINT_PROCEDURE,
} from "@/constants";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import { isEmptyObject } from "@/app/utils/isEmptyObject";
import { getLabTests, getLabPackages } from "@/api/harbor.service";

const getBlogs = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?list=true`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = [] } = data || {};
      return result;
    }
  } catch (error) {
    console.log(error);
  }
};

const getDoctors = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_ENDPOINT_DOCTORS}?enterpriseCode=${enterpriseCode}`;
  try {
    const response = await axios.get(url);
    const { data = {}, status = null } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { doctors = [] } = result || {};
      return doctors;
    }
  } catch (error) {
    console.log(error);
  }
};

const getSpecialities = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/sitemap/${API_ENDPOINT_SPECIALITY}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { specialities = [] } = result || {};
      return specialities;
    }
  } catch (error) {
    console.log(error);
  }
};

const getProcedures = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/sitemap/${API_ENDPOINT_PROCEDURE}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { procedures = [] } = result || {};
      return procedures;
    }
  } catch (error) {
    console.log(error);
  }
};
const getCustomPages = async (enterpriseCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PAGE}?list=true`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = [] } = data || {};
      return result;
    }
  } catch (error) {
    console.log(error);
  }
};

const getTemplateName = async () => {
  const domainName = getWebsiteHost();
  try {
    const response = await fetch(
      `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true`,
      {
        cache: "no-store", // Prevents caching
      }
    );

    const data = await response.json();
    const { result = {}, code } = data || {};
    if (code === 200) {
      const { template = {} } = result || {};
      const { name: templateName = "" } = template || {};
      return templateName?.toLowerCase();
    }
  } catch (error) {
    console.error("Error fetching template name:", error);
    return null;
  }
};

const getHospitalCenters = async () => {
  const domainName = getWebsiteHost();
  try {
    const response = await fetch(
      `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`,
      {
        cache: "no-store", // Prevents caching
      }
    );

    const data = await response.json();
    const { result = {}, code } = data || {};
    if (code === 200) {
      const { centers = [] } = result || {};
      return centers;
    }
  } catch (error) {
    console.error("Error fetching template name:", error);
    return null;
  }
};

const getEnterpriseCode = async (domain_slug) => {
  const domainName = getWebsiteHost();
  let url;
  if (domain_slug) {
    url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true&domainSlug=${domain_slug}`;
  } else {
    url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&websiteOnly=true`;
  }
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    const { result = {} } = jsonRes || {};
    const { enterprise_code: enterpriseCode = null } = result || {};
    return enterpriseCode;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function sitemap() {
  const domainName = getWebsiteHost();
  let staticSitemap = [
    {
      url: `https://${domainName}`,
    },
    {
      url: `https://${domainName}/about-us`,
    },
    {
      url: `https://${domainName}/contact-us`,
    },
    {
      url: `https://${domainName}/blogs`,
    },
    {
      url: `https://${domainName}/doctors`,
    },
    {
      url: `https://${domainName}/reviews`,
    },
    {
      url: `https://${domainName}/specialities`,
    },
    {
      url: `https://${domainName}/health-tips`,
    },
  ];

  let websiteTemplateName = await getTemplateName();
  websiteTemplateName = websiteTemplateName?.toLowerCase().replace(/\s/g, "");

  if (websiteTemplateName === "labssinglehospital1") {
    // Labs Single Hospital Template Sitemap
    const enterpriseCode = await getEnterpriseCode();

    // Lab-specific static sitemap
    const labStaticSitemap = [
      {
        url: `https://${domainName}`,
      },
      {
        url: `https://${domainName}/about-us`,
      },
      {
        url: `https://${domainName}/contact-us`,
      },
      {
        url: `https://${domainName}/blogs`,
      },
      {
        url: `https://${domainName}/reviews`,
      },
      {
        url: `https://${domainName}/tests`,
      },
      {
        url: `https://${domainName}/categories`,
      },
      {
        url: `https://${domainName}/disclaimer`,
      },
      {
        url: `https://${domainName}/privacy-policy`,
      },
      { 
        url: `https://${domainName}/terms-and-conditions`,
      },
    ];

    const labTestsResponse = (await getLabTests(enterpriseCode, { perPage: 10000 })) || { data: [] };
    const labPackagesResponse = (await getLabPackages(enterpriseCode, { perPage: 10000 })) || { data: [] };
    const blogs = (await getBlogs(enterpriseCode)) || [];
    const customPages = (await getCustomPages(enterpriseCode)) || [];

    const labTests = labTestsResponse.data || [];
    const labPackages = labPackagesResponse.data || [];

    // console.log(`Data fetched - Tests: ${labTests.length}, Packages: ${labPackages.length}, Blogs: ${blogs.length}, Custom Pages: ${customPages.length}`);

    // if (labTests.length > 0) {
    //   console.log('Sample test data:', JSON.stringify(labTests[0], null, 2));
    // }
    // if (labPackages.length > 0) {
    //   console.log('Sample package data:', JSON.stringify(labPackages[0], null, 2));
    // }

    const labTestsSitemap = labTests
      .map((test) => {
        const { seoSlug = "", updatedAt = "" } = test || {};
        const sitemapEntry = {
          url: `https://${domainName}/tests/${seoSlug}`,
          lastModified: new Date(updatedAt),
          // lastModified: new Date(),
        };
        
        return sitemapEntry;
      });

    const labPackagesSitemap = labPackages
      .map((packageItem) => {
        const { seoSlug = "", updatedAt = "" } = packageItem || {};
        const sitemapEntry = {
          url: `https://${domainName}/tests/${seoSlug}`,
          lastModified: new Date(updatedAt),
          // lastModified: new Date(),
        };
        return sitemapEntry;
      });

    const blogsSitemap = blogs
      .map((blog) => {
        const { seoSlug = "", updatedAt = "" } = blog || {};
        const sitemapEntry = {
          url: `https://${domainName}/blogs/${seoSlug}`,
          lastModified: new Date(updatedAt),
          // lastModified: new Date(),
        };
        return sitemapEntry;
      });

    const customPagesSitemap = customPages
      .map((page) => {
        const { seoSlug = "", updatedAt = "" } = page || {};
        const sitemapEntry = {
          url: `https://${domainName}/pages/${seoSlug}`,
          lastModified: new Date(updatedAt),
          // lastModified: new Date(),
        };
        return sitemapEntry;
      });

    // console.log(`Sitemap entries generated - Tests: ${labTestsSitemap.length}, Packages: ${labPackagesSitemap.length}, Blogs: ${blogsSitemap.length}, Custom Pages: ${customPagesSitemap.length}`);

    const totalSitemapEntries = [
      ...labStaticSitemap,
      ...labTestsSitemap,
      ...labPackagesSitemap,
      ...blogsSitemap,
      ...customPagesSitemap,
    ];

    // console.log(`Total sitemap entries: ${totalSitemapEntries.length}`);
    return totalSitemapEntries;
  } else if (
    websiteTemplateName &&
    (websiteTemplateName === "aspire" ||
      websiteTemplateName === "hospitalchaintemplate2apollo" ||
      websiteTemplateName === "singlespecialitymultichain1" ||
      websiteTemplateName === "singlespecialitymultichain2")
  ) {
    //Hospital chain Sitemap
    const chainEnterpriseCode = await getEnterpriseCode();
    const centers = (await getHospitalCenters()) || [];
    const centerSitemapPromises = centers.map(async (center) => {
      const { domain_slug } = center;
      const enterpriseCode = await getEnterpriseCode(domain_slug);
      const allSpecialities = (await getSpecialities(enterpriseCode)) || [];
      const allProcedures = (await getProcedures(enterpriseCode)) || [];

      // Generate specialities sitemap for this center
      const specialitiesSitemap = allSpecialities.flatMap((speciality) => {
        const { seoSlug = "", relatedLinks = [], updatedAt } = speciality || {};
        const url = {
          url: `https://${domainName}/specialities/${domain_slug}/${seoSlug}`,
          lastModified: new Date(updatedAt),
        };

        if (
          relatedLinks &&
          Array.isArray(relatedLinks) &&
          relatedLinks.length > 0
        ) {
          const relatedLinkUrls = relatedLinks
            .map((link) => {
              const { seoSlug: relatedLinkSlug = "", updatedAt } = link || {};
              if (!relatedLinkSlug) return null;

              return {
                url: `https://${domainName}/specialities/${domain_slug}/${relatedLinkSlug}`,
                lastModified: new Date(updatedAt),
              };
            })
            .filter(Boolean); // Filter out null values

          return [url, ...relatedLinkUrls];
        }

        return [url]; // Return as array for consistent flatMap behavior
      });

      // Generate procedures sitemap for this center
      const proceduresSitemap = allProcedures.flatMap((procedure) => {
        const {
          specialitySlug: specialitySeoSlug = "",
          seoSlug: procedureSeoSlug,
          relatedLinks = [],
          updatedAt,
        } = procedure || {};
        const url = {
          url: `https://${domainName}/specialities/${domain_slug}/${specialitySeoSlug}/procedures/${procedureSeoSlug}`,
          lastModified: new Date(updatedAt),
        };

        if (
          relatedLinks &&
          Array.isArray(relatedLinks) &&
          relatedLinks.length > 0
        ) {
          const relatedLinkUrls = relatedLinks
            .map((link) => {
              const { seoSlug: relatedLinkSlug = "", updatedAt } = link || {};
              if (!relatedLinkSlug) return null;

              return {
                url: `https://${domainName}/specialities/${domain_slug}/${specialitySeoSlug}/procedures/${relatedLinkSlug}`,
                lastModified: new Date(updatedAt),
              };
            })
            .filter(Boolean); // Filter out null values

          return [url, ...relatedLinkUrls];
        }

        return [url]; // Return as array for consistent flatMap behavior
      });

      // Return combined sitemap entries for this center
      return [...specialitiesSitemap, ...proceduresSitemap];
    });

    // Wait for all center sitemaps to resolve and combine them
    const centerSitemaps = await Promise.all(centerSitemapPromises);
    const allCenterSitemaps = centerSitemaps.flat();
    const relatedLinksSpecialities =
      (await getSpecialities(chainEnterpriseCode)) || [];
    const relatedLinksProcedures =
      (await getProcedures(chainEnterpriseCode)) || [];
    const blogs = (await getBlogs(chainEnterpriseCode)) || [];

    const doctors = (await getDoctors(chainEnterpriseCode)) || [];
    const customPages = (await getCustomPages(chainEnterpriseCode)) || [];

    const blogsSitemap = blogs.map((blog) => {
      const { seoSlug = "", updatedAt = "", centerDetails = {} } = blog || {};
      if (isEmptyObject(centerDetails)) {
        return {
          url: `https://${domainName}/blogs/${seoSlug}`,
          lastModified: new Date(updatedAt),
        };
      } else {
        return {
          url: `https://${domainName}/blogs/location/${centerDetails.domain_slug}/${seoSlug}`,
          lastModified: new Date(updatedAt),
        };
      }
    });

    const centersSitemap = centers.map((center) => {
      return {
        url: `https://${domainName}/locations/${center.domain_slug}`,
      };
    });

    const doctorsSitemap = doctors.flatMap((doctor) => {
      const { doctorDetails = {}, updatedAt = "" } = doctor || {};
      const { seoSlug = "", centers = [] } = doctorDetails || {};
      return centers.map((center) => {
        return {
          url: `https://${domainName}/doctors/${center.domain_slug}/${seoSlug}`,
          lastModified: new Date(updatedAt),
        };
      });
    });
    const specialitiesSitemap = relatedLinksSpecialities.flatMap(
      (speciality) => {
        const { seoSlug = "", relatedLinks = [], updatedAt } = speciality || {};

        const url = {
          url: `https://${domainName}/specialities/${seoSlug}`,
          lastModified: new Date(updatedAt),
        };

        if (relatedLinks && relatedLinks.length > 0) {
          const relatedLinkUrls = relatedLinks.map((link) => {
            const { seoSlug: relatedLinkSlug = "", updatedAt } = link || {};
            if (!relatedLinkSlug) return null;

            return {
              url: `https://${domainName}/specialities/${relatedLinkSlug}`,
              lastModified: new Date(updatedAt),
            };
          });
          return [url, ...relatedLinkUrls];
        }

        return url;
      }
    );

    const proceduresSitemap = relatedLinksProcedures.flatMap((procedure) => {
      const {
        specialitySlug: specialitySeoSlug = "",
        seoSlug: procedureSeoSlug,
        relatedLinks = [],
        updatedAt,
      } = procedure || {};
      const url = {
        url: `https://${domainName}/specialities/${specialitySeoSlug}/procedures/${procedureSeoSlug}`,
        lastModified: new Date(updatedAt),
      };

      if (relatedLinks && relatedLinks.length > 0) {
        const relatedLinkUrls = relatedLinks.map((link) => {
          const { seoSlug: relatedLinkSlug = "", updatedAt } = link || {};
          if (!relatedLinkSlug) return null;

          return {
            url: `https://${domainName}/specialities/${specialitySeoSlug}/procedures/${relatedLinkSlug}`,
            lastModified: new Date(updatedAt),
          };
        });

        return [url, ...relatedLinkUrls];
      }

      return url;
    });

    const customPagesSitemap = customPages.map((page) => {
      const { seoSlug = "", updatedAt } = page || {};
      return {
        url: `https://${domainName}/pages/${seoSlug}`,
        lastModified: new Date(updatedAt),
      };
    });

    return [
      ...staticSitemap,
      ...centersSitemap,
      ...blogsSitemap,
      ...doctorsSitemap,
      ...specialitiesSitemap,
      ...proceduresSitemap,
      ...allCenterSitemaps,
      ...customPagesSitemap,
    ];
  } else {
    //Hospital sitemap

    const enterpriseCode = await getEnterpriseCode();
    const blogs = (await getBlogs(enterpriseCode)) || [];
    const doctors = (await getDoctors(enterpriseCode)) || [];
    // const specialities = await getSpecialities(enterpriseCode) || [];
    const relatedLinksSpecialities =
      (await getSpecialities(enterpriseCode)) || [];
    // const procedures =  await getProcedures(enterpriseCode) || [];
    const relatedLinksProcedures = (await getProcedures(enterpriseCode)) || [];
    const customPages = (await getCustomPages(enterpriseCode)) || [];

    const blogsSitemap = blogs.map((blog) => {
      const { seoSlug = "", updatedAt = "" } = blog || {};
      return {
        url: `https://${domainName}/blogs/${seoSlug}`,
        lastModified: new Date(updatedAt),
      };
    });

    const doctorsSitemap = doctors.map((doctor) => {
      const { doctorDetails = {}, updatedAt = "" } = doctor || {};
      const { seoSlug = "" } = doctorDetails || {};
      return {
        url: `https://${domainName}/doctors/${seoSlug}`,
        lastModified: new Date(updatedAt),
      };
    });

    const specialitiesSitemap = relatedLinksSpecialities.flatMap(
      (speciality) => {
        const { seoSlug = "", relatedLinks = [], updatedAt } = speciality || {};

        const url = {
          url: `https://${domainName}/specialities/${seoSlug}`,
          lastModified: new Date(updatedAt),
        };
        if (
          relatedLinks &&
          Array.isArray(relatedLinks) &&
          relatedLinks.length > 0
        ) {
          const relatedLinkUrls = relatedLinks.map((link) => {
            const { seoSlug: relatedLinkSlug = "", updatedAt } = link || {};
            if (!relatedLinkSlug) return null;

            return {
              url: `https://${domainName}/specialities/${relatedLinkSlug}`,
              lastModified: new Date(updatedAt),
            };
          });

          return [url, ...relatedLinkUrls];
        }

        return url;
      }
    );

    const customPagesSitemap = customPages.map((page) => {
      const { seoSlug = "", updatedAt } = page || {};
      return {
        url: `https://${domainName}/pages/${seoSlug}`,
        lastModified: new Date(updatedAt),
      };
    });

    let proceduresSitemap = relatedLinksProcedures.map((procedure) => {
      const {
        specialitySlug: specialitySeoSlug = "",
        seoSlug: procedureSeoSlug,
        relatedLinks = [],
        updatedAt,
      } = procedure || {};
      const url = {
        url: `https://${domainName}/specialities/${specialitySeoSlug}/procedures/${procedureSeoSlug}`,
        lastModified: new Date(updatedAt),
      };
      if (
        relatedLinks &&
        Array.isArray(relatedLinks) &&
        relatedLinks.length > 0
      ) {
        const relatedLinkUrls = relatedLinks.map((link) => {
          const { seoSlug: relatedLinkSlug = "", updatedAt } = link || {};
          if (!relatedLinkSlug) return null;

          return {
            url: `https://${domainName}/specialities/${specialitySeoSlug}/procedures/${relatedLinkSlug}`,
            lastModified: new Date(updatedAt),
          };
        });

        return [url, ...relatedLinkUrls];
      }

      return url;
    });

    proceduresSitemap = proceduresSitemap.flat(proceduresSitemap.length - 1);
    staticSitemap = [
      ...staticSitemap,
      {
        url: `https://${domainName}/disclaimer`,
      },

      {
        url: `https://${domainName}/privacy-policy`,
      },

      {
        url: `https://${domainName}/terms-and-conditions`,
      },
    ];

    return [
      ...staticSitemap,
      ...blogsSitemap,
      ...doctorsSitemap,
      ...specialitiesSitemap,
      ...proceduresSitemap,
      ...customPagesSitemap,
    ];
  }
}
