"use client"
import React from "react";
import { Box, Typography, Rating } from "@mui/material";

import useStyles from "../../../styles";
import homepageStyles from "../styles";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";
import {getInitials, getTimeAgo} from "@/app/utils/getInitials";
const ReviewsSection = ({ testimonials = [] }) => {
  const commonClasses = useStyles();
  const classes = homepageStyles();

  return (
    <SectionLayout
      id="reviews"
      sx={{
          gap: {xs: "32px", md: "48px"}
      }}
      style={{ display: "flex", flexDirection: "column"}}
    >
      <Box
        style={{
          display: "flex",
          justifyContent: "space-between",
          gap: "4rem",
        }}
      >
        <Box className={classes.reviewsSectionHeadingBox}>
          <Typography variant="h5"
                      sx={{
                          textTransform: "uppercase",
                          color: "primary.main",
                      }}>
            Reviews
          </Typography>
          <Typography
            variant="h3"
            className={classes.homepageSectionSubHeading}
          >
            Healing Journeys Shared Experiences
          </Typography>
        </Box>
        {/* <Button
          variant="outlined"
          color="primary"
          className={classes.reviewsSectionButton}
        >
          Share experience
        </Button> */}
      </Box>
      <Box>
        <Swiper
          spaceBetween={36}
          breakpoints={{
            0: {
              slidesPerView: 1,
            },
            600: {
              slidesPerView: 2,
            },
            1200: {
              slidesPerView: 3,
            },
          }}
          navigation={true}
          modules={[Navigation]}
          className="mySwiper"
          style={{
            "--swiper-navigation-color": "black",
            "--swiper-navigation-size": "24px",
            "--swiper-navigation-sides-offset": "10px",
          }}
        >
          {testimonials.map((testimonial, idx) => {
            const {
              name = "",
              rating = "",
              text = "",
              updated_at: updatedAt = null,
            } = testimonial || {};
            const initials = getInitials(name || "");
            const getTimeOfReviewPosted = getTimeAgo(updatedAt);
            return (
              <SwiperSlide>
                <Box key={idx} className={classes.reviewsSectionCarousalBoxItem}>
                  <Box className={classes.reviewsSectionContentBox}>
                    <Box className={classes.reviewsSectionReviewBox}>
                      <Rating
                        name="simple-controlled"
                        value={Number(rating)}
                        readOnly
                        className={classes.reviewsSectionRating}
                        classes={{
                          iconFilled: classes.reviewsSectionRatingIconFilled,
                        }}
                      />
                      <Typography
                        variant="body1"
                        sx={{marginTop: "12px"}}
                        className={classes.reviewsSectionReviewBoxText}
                      >
                        {text || ""}
                      </Typography>
                    </Box>
                  </Box>
                  <Box className={classes.reviewsSectionProfileBox}>
                    <Box className={classes.reviewsSectionProfileImg}>
                      {initials}
                    </Box>
                    <Box className={classes.reviewsSectionProfileDetailsBox}>
                      <Typography className={classes.reviewsSectionProfileName}>
                        {name || ""}
                      </Typography>
                      {/* <Typography
                                className={classes.reviewsSectionReviewTime}
                              >
                                {getTimeOfReviewPosted}
                              </Typography> */}
                    </Box>
                  </Box>
                </Box>
              </SwiperSlide>
            );
          })}
        </Swiper>
      </Box>
    </SectionLayout>
  );
};

export default ReviewsSection;
