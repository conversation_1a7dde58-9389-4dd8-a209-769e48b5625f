"use client";

import { useContext } from "react";
import SectionLayout from "../styledComponents/SectionLayout";
import { AppContext } from "../../AppContextLayout";
import parse from "html-react-parser";

export default function AboutUs() {
  const { websiteData } = useContext(AppContext);
  const { about_us: aboutUs = "" } = websiteData || {};
  return <SectionLayout><div
      className="ck-content"
      dangerouslySetInnerHTML={{__html: aboutUs}} // Render HTML safely
  /></SectionLayout>;
}
