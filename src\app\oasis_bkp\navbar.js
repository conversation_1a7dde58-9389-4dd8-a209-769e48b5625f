"use client";

import Image from "next/image";
import Link from "next/link";
import {
  BottomNavigation,
  BottomNavigationAction,
  Box,
  Button,
  Divider,
  Drawer,
  Menu,
  MenuItem,
  Paper,
  Typography,
  alpha,
} from "@mui/material";
import PhoneCallbackOutlinedIcon from "@mui/icons-material/PhoneCallbackOutlined";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import BloodtypeOutlinedIcon from "@mui/icons-material/BloodtypeOutlined";
import NavbarDropdown from "./components/navbarDropdown";
import NavbarMenu from "./components/navbarMenu";
import CallUsButton from "./components/callUsButton";
import { useContext, useEffect, useState } from "react";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_NAVBAR,
} from "@/constants";
import axios from "axios";
import { AppContext } from "../AppContextLayout";
import { getThumborUrl } from "../utils/getThumborUrl";
import { useTheme } from "@emotion/react";
import BottomNavBar from "@/app/commoncomponents/bottomNavBar";

const Navbar = () => {
  const theme = useTheme();
  const [navbarItemsList, setNavbarItemsList] = useState([]);
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, logo_url: logoUrl = "" } =
    websiteData || {};

  // const sortByPriority = (arr) => {
  //   const sortedArr = arr.sort((a, b) => {
  //     return a?.priority - b?.priority;
  //   })
  //   setNavbarItemsList(sortedArr);
  // }

  const getNavbarItems = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_NAVBAR}${enterpriseCode}/`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        // sortByPriority(result);
        setNavbarItemsList(result);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    if (enterpriseCode) getNavbarItems();
  }, [enterpriseCode]);

  return (
    <>
      <Box
        sx={{
          position: "sticky",
          top: { xs: 0, md: "-36px" },
          zIndex: 10,
        }}
      >
        <Box
          sx={{
            alignItems: "center",
            justifyContent: "flex-end",
            boxShadow: "0px 1px 1px #00000014",
            padding: { xs: "8px 16px", md: "8px 128px", lg: "8px 200px" },
            borderBottom: "1px solid #00000014",
            display: { xs: "none", md: "flex" },
            // display: "none"
          }}
        >
          <Box
            sx={{
              color: "primary.main",
              fontSize: "14px",
              listStyle: "none",
              display: "flex",
              alignItems: "center",
              gap: "24px",
              zIndex: 10,
            }}
          >
            {(navbarItemsList[0]?.sections || []).map((item, index) => {
              const {
                displayName = "",
                redirection = {},
                sections = null,
                type = 1,
              } = item || {};
              const { redirectionUrl = "" } = redirection || {};
              if (type === 2)
                return (
                  <Box id={`navbarSection0Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                    <Link href={redirectionUrl} target="_blank">
                        <Typography
                            fontSize="12px"
                        >
                            {displayName || ""}
                        </Typography>
                    </Link>
                  </Box>
                );
              else if (sections)
                return (
                  <NavbarDropdown
                    key={`${displayName}${index}`}
                    navbarItem={item}
                    id={`navbarSection0Item${index}`}
                  />
                );
              else
                return (
                  <Box id={`navbarSection0Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                    <Link href={redirectionUrl}>
                        <Typography
                            fontSize="12px"
                        >
                            {displayName || ""}
                        </Typography>
                    </Link>
                  </Box>
                );
            })}
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            boxShadow: "0px 1px 1px #00000014",
            padding: { xs: "8px 16px", md: "8px 128px", lg: "8px 200px" },
            justifyContent: "space-between",
            background: "#fff",
            borderBottom: "1px solid #00000014",
          }}
        >
          <Link id="navbarLogo" href="/">
            {logoUrl && <Image
              alt="logo"
              src={getThumborUrl(logoUrl)}
              width={160}
              height={53}
            />}
          </Link>

          <Box sx={{ display: { xs: "none", md: "block" } }}>
            <Box
              sx={{
                color: "primary.main",
                listStyle: "none",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                gap: "24px",
              }}
            >
              {(navbarItemsList[1]?.sections || []).map((item, index) => {
                const {
                  displayName = "",
                  redirection = {},
                  sections = null,
                  type = 1,
                } = item || {};
                const { redirectionUrl = "" } = redirection || {};
                if (type === 2)
                  return (
                    <Box id={`navbarSection1Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                      <Link href={redirectionUrl} target="_blank">
                          <Typography
                              fontSize="14px"
                          >
                              {displayName || ""}
                          </Typography>
                      </Link>
                    </Box>
                  );
                else if (sections)
                  return (
                    <NavbarDropdown
                      key={`${displayName}${index}`}
                      navbarItem={item}
                      id={`navbarSection0Item${index}`}
                    />
                  );
                else
                  return (
                    <Box id={`navbarSection0Item${index}`} sx={{ "&:hover": { color: "secondary.main" } }}>
                      <Link href={redirectionUrl}>
                          <Typography
                              fontSize="14px"
                          >
                              {displayName || ""}
                          </Typography>
                      </Link>
                    </Box>
                  );
              })}
            </Box>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: "16px" }}>
            <CallUsButton />
            <Divider
              orientation="vertical"
              flexItem
              sx={{ display: { xs: "inline-block", md: "none" } }}
            />
            <NavbarMenu navbarItemsList={navbarItemsList} />
          </Box>
        </Box>
        <Box
          sx={{
            background: "#fff",
            padding: { xs: "8px 16px", md: "8px 128px", lg: "8px 200px" },
            boxShadow: "0px 3px 6px #00000029",
            display: { xs: "none", md: "block" },
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "8px",
            }}
          >
            <Link id="navbarRequestCallback" href="/contact-us">
              <Box
                sx={{
                  color: "#333",
                  fontSize: "12px",
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  cursor: "pointer",
                  padding: "7px 22px",
                  borderRadius: "50px",
                  transition: "all .3s",
                  "&:hover": { background: alpha(theme.palette.primary.main, 0.3) }
                }}
              >
                <PhoneCallbackOutlinedIcon style={{ color: "inherit" }} />
                  <Typography
                      fontSize="12px"
                  >
                      Request a Callback
                  </Typography>
              </Box>
            </Link>
            <Link id="navbarBookAppointment" href="/doctors">
              <Box
                sx={{
                  color: "#181515",
                  fontSize: "12px",
                  fontWeight: "500",
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  background: alpha(theme.palette.primary.main, 0.3),
                  padding: "7px 22px",
                  borderRadius: "50px",
                  cursor: "pointer",
                }}
              >
                <CalendarMonthOutlinedIcon />
                  <Typography
                      fontSize="12px"
                  >
                      Book an Appointment
                  </Typography>

              </Box>
            </Link>
            <Link id="navbarHealthCheckup" href="/contact-us">
              <Box
                sx={{
                  color: "#333",
                  fontSize: "12px",
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  cursor: "pointer",
                  padding: "7px 22px",
                  borderRadius: "50px",
                  transition: "all .3s",
                  "&:hover": { background: alpha(theme.palette.primary.main, 0.3) }
                }}
              >
                <BloodtypeOutlinedIcon style={{ color: "inherit" }} />
                  <Typography
                      fontSize="12px"
                  >
                      Get Health Checkup
                  </Typography>
              </Box>
            </Link>
          </div>
        </Box>
      </Box>
        <BottomNavBar/>
    </>
  );
};

export default Navbar;
