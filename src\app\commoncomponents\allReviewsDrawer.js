import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import Divider from "@mui/material/Divider";
import {
  Dialog,
  IconButton,
  Pagination,
  Rating,
  Typography,
} from "@mui/material";
import Image from "next/image";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import {
  API_ENDPOINT_AGGREGATED_REVIEWS,
  API_ENDPOINT_REVIEWS,
  API_SECTION_API,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_VERSION,
  HARBOR_API_DOCFYN_DOMAIN,
} from "@/constants";
import axios from "axios";
import { useEffect, useRef, useState } from "react";
import { format } from "date-fns";
import { getInitials } from "@/app/utils/getInitials";
import CloseIcon from "@mui/icons-material/Close";
import RatingBasedIcon from "./RatingBasedIcon";
import PhotoReview from "./photoReview";
import OutlinedButton from "../aspire/styledComponents/OutlinedButton";

const AllReviewsDrawer = ({
  enterpriseCode = "epsjkkm",
  entityCode = null,
  specialityCode = null,
  viewReviewsDrawer = false,
  setViewReviewsDrawer,
}) => {
  const [patientReviews, setPatientReviews] = useState([]);
  const [pagination, setPagination] = useState({});
  const { hasNextPage = false, currentPage = 0 } = pagination || {};

  const getReviews = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}/${API_ENDPOINT_AGGREGATED_REVIEWS}?list=true&page=${currentPage + 1}${entityCode ? `&entityCode=${entityCode}` : ""}${specialityCode ? `&specialityCode=${specialityCode}` : ""}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { data: reviews = [], pagination = {} } = result || {};
        setPatientReviews((prev) => [...patientReviews, ...reviews]);
        setPagination(pagination);
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  const handleLoadMore = () => {
    getReviews();
  };

  useEffect(() => {
    if (enterpriseCode) getReviews();
  }, [enterpriseCode]);

  return (
    <div>
      <Drawer
        anchor="right"
        open={viewReviewsDrawer}
        onClose={() => setViewReviewsDrawer(false)}
        sx={{ padding: 0 }}
      >
        <Box sx={{ width: { xs: "100vw", md: "60vw" }, padding: "0 16px" }}>
          <Box
            sx={{
              position: "fixed",
              backgroundColor: "white",
              overflow: "hidden",
              // height: "36px",
              width: "100%",
              padding: "16px 0",
              zIndex: 10,
            }}
          >
            <Box
              sx={{
                height: "36px",
                width: "36px",
                borderRadius: "50px",
                backgroundColor: "rgba(0, 0, 0, 0.04)",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
                overflow: "hidden",
              }}
              onClick={() => setViewReviewsDrawer(false)}
            >
              <CloseIcon />
            </Box>
          </Box>
          <Box sx={{ marginTop: "64px" }}>
            <Typography variant="h3" sx={{ fontSize: "20px", fontWeight: 700 }}>
              Patient Reviews
            </Typography>
            <Box
              sx={{
                mt: 4,
                display: "flex",
                flexDirection: "column",
                gap: "32px",
              }}
            >
              {patientReviews.map((review, index) => {
                const {
                  content = "",
                  givenByEntity = "",
                  title = "",
                  postingDate = "",
                  ratingType = 2,
                  rating = "",
                  reviewSource = {},
                  images = [],
                } = review || {};
                const { icon = "" } = reviewSource || {};
                return (
                  <Box key={index}>
                    <Box
                      sx={{
                        display: "grid",
                        gridTemplateColumns: ".3fr 1fr",
                        gap: "48px",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: "16px",
                        }}
                      >
                        <Box sx={{ display: "flex", gap: "8px" }}>
                          <Box
                            sx={{
                              height: "32px",
                              width: "32px",
                              borderRadius: "100px",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              fontSize: "1.2rem",
                              bgcolor: "primary.main",
                              color: "#ffffff",
                            }}
                          >
                            {getInitials(givenByEntity || "Anonymous")}
                          </Box>
                          <Typography
                            variant="h6"
                            sx={{
                              fontSize: "16px",
                              fontWeight: "600",
                              flex: 1,
                            }}
                          >
                            {givenByEntity || "Anonymous"}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: "4px",
                            alignItems: "center",
                          }}
                        >
                          {icon && (
                            <Image
                              src={getThumborUrl(icon || "")}
                              height={30}
                              width={60}
                              style={{ objectFit: "contain" }}
                            />
                          )}
                          <RatingBasedIcon
                            ratingType={ratingType}
                            rating={rating}
                          />
                        </Box>
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Box
                          sx={{
                            display: "flex",
                            gap: "24px",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{ fontSize: "16px", fontWeight: "600" }}
                          >
                            {title || ""}
                          </Typography>
                          {postingDate && (
                            <Typography
                              variant="body2"
                              sx={{ fontSize: "12px" }}
                            >
                              {format(new Date(postingDate || ""), "MMM yyyy")}
                            </Typography>
                          )}
                        </Box>
                        <Typography
                          variant="body1"
                          sx={{ fontSize: "14px", mt: 1 }}
                        >
                          {content || ""}
                        </Typography>
                        <Box
                          sx={{
                            display: "grid",
                            gridTemplateColumns:
                              "repeat(auto-fill, minmax(64px, 1fr))",
                            mt: 2,
                          }}
                        >
                          {images.map((image, index) => {
                            const { imageUrl = "" } = image || {};
                            return (
                              <PhotoReview key={index} imageUrl={imageUrl} />
                            );
                          })}
                        </Box>
                      </Box>
                    </Box>
                    {index < patientReviews.length - 1 && (
                      <Divider sx={{ mt: 4 }} />
                    )}
                  </Box>
                );
              })}
            </Box>
          </Box>
          <Box
            sx={{ display: "flex", justifyContent: "center", margin: "48px 0" }}
          >
            {hasNextPage && (
              <OutlinedButton onClick={handleLoadMore}>
                Load More
              </OutlinedButton>
            )}
          </Box>
        </Box>
      </Drawer>
    </div>
  );
};

export default AllReviewsDrawer;
