import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Radio,
  RadioGroup,
} from "@mui/material";
import Image from "next/image";
import Slide from "@mui/material/Slide";
import { forwardRef, useEffect, useState } from "react";
import OutlinedButton from "../styledComponents/OutlinedButton";
import PrimaryButton from "../styledComponents/PrimaryButton";
import { useRouter } from "next/navigation";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DoctorListItem = ({ doctorDetails, index }) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [selectedCenter, setSelectedCenter] = useState(null);
  const {
    name: doctorName = "",
    code = null,
    seoSlug = "",
    centers = [],
  } = doctorDetails || {};
  const { domain_slug: centerSlug = "" } = selectedCenter || {};

  const handleDoctorCardClick = (event) => {
    event.stopPropagation();
    if (centers.length === 1) router.push(`/doctors/${centerSlug}/${seoSlug}`);
    else {
      setOpen(true);
    }
  };

  const handleCentersModal = () => {
    setOpen(false);
  };

  const handleCenterChange = (event) => {
    const value = event.target.value;
    const selectedValue = centers.find((center) => center.code === value);
    setSelectedCenter(selectedValue);
  };

  const handleRedirection = () => {
    router.push(`/doctors/${centerSlug}/${seoSlug}`);
  };

  useEffect(() => {
    if (centers.length > 0) {
      setSelectedCenter(centers[0]);
    }
  }, [centers]);

  return (
    <>
      <ListItemButton
        id={`doctor${index}`}
        key={code}
        onClick={handleDoctorCardClick}
      >
        <ListItemIcon sx={{ minWidth: "32px", mr: 2 }}>
          <Image alt="doctor" src="/doctor-male.svg" height={32} width={32} />
        </ListItemIcon>
        <ListItemText primary={doctorName || ""} />
      </ListItemButton>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        onClose={handleCentersModal}
        keepMounted={false}
        aria-describedby="alert-dialog-slide-description"
        sx={{ ".MuiDialog-paper": { width: { xs: "100%", sm: "600px" } } }}
      >
        <DialogTitle>Select Location</DialogTitle>
        <DialogContent>
          <FormControl sx={{ padding: "8px 16px" }}>
            <RadioGroup
              aria-labelledby="demo-radio-buttons-group-label"
              name="radio-buttons-group"
              value={selectedCenter?.code}
              onChange={handleCenterChange}
            >
              {centers.map((center) => {
                const { code = null, name = "", area = {} } = center || {};
                const { name: areaName = "" } = area || {};
                return (
                  <FormControlLabel
                    value={code}
                    control={<Radio />}
                    label={`${name || ""} - ${areaName}`}
                  />
                );
              })}
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <OutlinedButton onClick={handleCentersModal}>Close</OutlinedButton>
          <PrimaryButton onClick={handleRedirection}>Confirm</PrimaryButton>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DoctorListItem;
