export const convertRatingIn1to5 = (review) => {
    const {rating, ratingType} = review;
    let convertedRating;

    switch (ratingType) {
        case 1:
            // Percentage rating (0-100)
            convertedRating = (rating * 5) / 100;
            break;
        case 2: // Rating out of 5
            convertedRating = rating;
            break;
        case 3:
            //TODO binary rating should come under one case - rating_type binary and value should be 0,1
            //Currently this case handle binary where value is 1
            convertedRating = 5;
            break;
        case 4:
            //TODO binary rating should come under one case - rating_type binary and value should be 0,1
            //Currently this case handle binary where value is 0
            convertedRating = 0;
            break;
        default:
            convertedRating = rating;
            break;
    }
    return convertedRating;
};

export const calculateAggregatedRating = (sources) => {
    let weightedSum = 0;
    let totalWeight = 0;

    sources.forEach((source) => {
        const {rating, ratingType, totalReviews} = source;
        let convertedRating;

        switch (ratingType) {
            case 1:
                // Percentage rating (0-100)
                convertedRating = (rating * 5) / 100;
                break;
            case 2: // Rating out of 5
                convertedRating = rating;
                break;
            case 3:
                //TODO binary rating should come under one case - rating_type binary and value should be 0,1
                //Currently this case handle binary where value is 1
                convertedRating = 5;
                break;
            case 4:
                //TODO binary rating should come under one case - rating_type binary and value should be 0,1
                //Currently this case handle binary where value is 0
                convertedRating = 0;
                break;
            default:
                convertedRating = rating;
                break;
        }

        weightedSum += convertedRating * totalReviews;
        totalWeight += totalReviews;
    });

    return totalWeight > 0 ? (weightedSum / totalWeight).toFixed(2) : null;
};
