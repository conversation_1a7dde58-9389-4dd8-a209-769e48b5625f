export const getThumborUrl = (imageUrl = "", height = 0, width = 0) => {
  if (imageUrl?.includes("ips.docfyn.com")) {
    // If the domain is ips.docfyn.com, dynamically inject height and width using eval
    return eval("`" + imageUrl + "`");
  } else if (imageUrl?.includes("cdn.docfyn.com")) {
    // If the domain is cdn.docfyn.com, return the imageUrl as it is
    return imageUrl;
  } else {
    // For any other domain, return the original URL (or handle it as needed)
    return imageUrl;
  }
};
