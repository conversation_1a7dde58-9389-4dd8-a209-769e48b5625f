"use client";

import {COUNTRIES, countryToFlag, FORM_PROCEDURES_ILLUMIS, FORM_SLOTS_SESSION_ILLUMIS, LEAD_SOURCES} from "@/constants";
import {Button, CircularProgress, InputBase, MenuItem, Select, TextField, Typography} from "@mui/material";
import Box from "@mui/material/Box";
import {useContext, useState} from "react";
import axios from "axios";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_ENTERPRISE,
    API_SECTION_LEADS,
    API_ENDPOINT_GENERATE,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";



const initialInput = {dialCode: "+91", type: 1};

const DoctorAppointmentForm2 = ({pageData = {}}) => {
    const {websiteData, setViewSnackbarMain, mobileView} =
        useContext(AppContext);
    const [input, setInput] = useState({...initialInput});
    const [inputLocation, setInputLocation] = useState();
    const [inputProcedures, setInputProcedures] = useState();
    const [inputSlots, setInputSlots] = useState();
    const [inputDate, setInputDate] = useState();
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState({
        phone: false
    });
    const {enterprise_code: enterpriseCode = null, centers = []} = websiteData || {};

    // Validate phone number - only allow numbers
    const validatePhoneNumber = (value) => {
        const numericRegex = /^[0-9]*$/;
        return numericRegex.test(value);
    };

    const handleInputChange = (e) => {
        setInput((prev) => ({...prev, [e.target.name]: e.target.value}));
    };

    // Handle phone number input with validation
    const handlePhoneChange = (e) => {
        const value = e.target.value;

        // Only update if the input is numeric or empty
        if (validatePhoneNumber(value)) {
            setInput((prev) => ({...prev, phone: value}));
            setErrors((prev) => ({...prev, phone: false}));
        } else {
            setErrors((prev) => ({...prev, phone: true}));
        }
    };

    const handleLeadGeneration = async () => {
        // Validate phone number before submission
        if (!input.phone || !validatePhoneNumber(input.phone)) {
            setErrors((prev) => ({...prev, phone: true}));
            setViewSnackbarMain({
                message: "Please enter a valid phone number",
                type: "error",
            });
            return;
        }

        setIsLoading(true);
        let eCode = enterpriseCode;

        if (inputLocation){
            eCode = inputLocation["epsCode"]
        }

        const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${eCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}${pageData?.productCode ? `?productCode=${extractCodeFromSlug(pageData.productCode)}` : ''}`;
        // const reqBody = {...input, leadSource};
        const reqBody = {
            phone: input.phone,
            firstName: input.firstName,
            dialCode: input.dialCode,
            leadSource: pageData.leadSource,
            type: 1,
            comments: [
                inputLocation && `Location: ${inputLocation["areaName"]} `,
                inputProcedures && `Procedure: ${inputProcedures["name"]} `,
                inputDate && `Slot Date: ${inputDate["date"]} `,
                inputSlots && `Slot Time: ${inputSlots["name"]} `,
                input["comments"] && `Comments  ${input["comments"]} `,
            ]
                .filter(Boolean) // Remove any undefined or falsey values
                .join(" | "), // Join the comments with a comma
        };
        if (pageData?.productCode){
            switch (pageData.leadSource){
                case LEAD_SOURCES.BLOG_DETAIL_PAGE:
                    reqBody.blogCode = pageData.productCode
                    break;
                case LEAD_SOURCES.PROCEDURE_PAGE:
                    reqBody.procedureCode = pageData.productCode
                    break;
                case LEAD_SOURCES.SPECIALITY_DETAIL_PAGE:
                    reqBody.specialityCode = pageData.productCode
                    break;
                case LEAD_SOURCES.CUSTOM_PAGE:
                    reqBody.customPageCode = pageData.productCode
                    break;
                case LEAD_SOURCES.DOCTOR_DETAIL_PAGE:
                    reqBody.doctorCode = pageData.productCode
                    break;
            }

        }
        if (pageData?.pageTitle){
            reqBody.pageTitle = pageData.pageTitle
        }

        try {
            const response = await axios.post(url, reqBody, {
                headers: {
                    "Content-Type": "application/json",
                    source: mobileView ? "mweb" : "website",
                },
            });
            const {data = {}, status = null} = response || {};
            if (status >= 200 && status < 300) {
                const {result = {}} = data || {};
                const {message = ""} = result || {};
                setViewSnackbarMain({
                    message: message,
                    type: "success",
                });
            }
        } catch (error) {
            setViewSnackbarMain({
                message: "Something went wrong. Please try again later!",
                type: "error",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const extractCodeFromSlug = (slug) => {
        const segments = slug.split('-');
        return segments[segments.length - 1];
    }

    return (
        <Box
            sx={{
                padding: "18px",
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                borderRadius: "8px",
                overflowY: "auto",
                bgcolor: "#fff",
                alignSelf: "start",
                width: "100%"
            }}
        >
            <Typography variant="h6" align="center" sx={{color: "#333"}}>
                Request a Call
            </Typography>
            <Box sx={{display: "flex", flexDirection: "column", gap: "16px"}}>
                <Box sx={{display: "flex", flexDirection: "column", gap: "12px"}}>

                    <Box sx={{display: "flex", gap: "8px"}}>
                        <Select
                            id="quickEnquiryCountryCode"
                            labelId="demo-customized-select-label"
                            value={input["dialCode"]}
                            onChange={(e) =>
                                setInput((prev) => ({...prev, dialCode: e.target.value}))
                            }
                            input={
                                <InputBase
                                    sx={{
                                        borderRadius: "4px",
                                        padding: "8px 12px",
                                        fontSize: "14px",
                                        width: "120px",
                                        transition: "all .3s",
                                        background: "#fff",
                                        color: "text.black",
                                        border: "1px solid #ccc", // Adds a border
                                        '&:hover': {
                                            borderColor: "text.black", // Changes border color on hover
                                        },
                                        '&:focus-within': {
                                            borderColor: "text.black", // Changes border color when focused
                                        },
                                    }}
                                    inputProps={{"aria-label": "enter your name"}}
                                />
                            }
                        >
                            {COUNTRIES.map((country) => {
                                const {
                                    dial_code: dialCode = "",
                                    code = "IN",
                                    value = "",
                                } = country || {};
                                return (
                                    <MenuItem sx={{color:"text.black"}} value={dialCode}>{`${countryToFlag(
                                        code
                                    )} ${value}`}</MenuItem>
                                );
                            })}
                        </Select>

                        <TextField
                            id="quickEnquiryPhone"
                            variant={"outlined"}
                            label="Enter Phone Number"
                            onChange={handlePhoneChange}
                            value={input.phone || ''}
                            inputMode={"tel"}
                            error={errors.phone}
                            helperText={errors.phone ? "Please enter numbers only" : ""}
                            inputProps={{
                                pattern: "[0-9]*",
                                inputMode: "numeric"
                            }}
                            sx={{
                                borderRadius: "4px",
                                width: "100%",
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: '4px',
                                    fontSize: '14px',
                                    color: 'text.black', // text color inside input
                                    '& fieldset': {
                                      borderColor: 'grey.400', // default border
                                    },
                                    '&:hover fieldset': {
                                      borderColor: 'grey.600', // on hover
                                    },
                                    '&.Mui-focused fieldset': {
                                      borderColor: 'black', // on focus
                                    },
                                  },
                                  '& .MuiInputLabel-root': {
                                    color: 'text.black', // label color
                                  },
                                  '& .MuiInputLabel-root.Mui-focused': {
                                    color: 'black', // label color on focus
                                  },
                            }}
                        />
                    </Box>

                    <TextField
                        id="quickEnquiryFirstName"
                        variant={"outlined"}
                        label="Enter Name"
                        name="firstName"
                        value={input.firstName || ''}
                        onChange={handleInputChange}
                        inputMode={"text"}
                        sx={{
                            borderRadius: "4px",
                            width: "100%",
                            '& .MuiOutlinedInput-root': {
                                    borderRadius: '4px',
                                    fontSize: '14px',
                                    color: 'text.black', // text color inside input
                                    '& fieldset': {
                                      borderColor: 'grey.400', // default border
                                    },
                                    '&:hover fieldset': {
                                      borderColor: 'grey.600', // on hover
                                    },
                                    '&.Mui-focused fieldset': {
                                      borderColor: 'black', // on focus
                                    },
                                  },
                                  '& .MuiInputLabel-root': {
                                    color: 'text.black', // label color
                                  },
                                  '& .MuiInputLabel-root.Mui-focused': {
                                    color: 'black', // label color on focus
                                  },
                        }}
                    />

                    <TextField
                        id="quickEnquiryComments"
                        variant={"outlined"}
                        label="Comments"
                        name="comments"
                        value={input.comments || ''}
                        onChange={handleInputChange}
                        multiline
                        minRows={2}
                        maxRows={4}
                        inputMode={"text"}
                        sx={{
                            borderRadius: "4px",
                            width: "100%",
                            '& .MuiOutlinedInput-root': {
                                    borderRadius: '4px',
                                    fontSize: '14px',
                                    color: 'text.black', // text color inside input
                                    '& fieldset': {
                                      borderColor: 'grey.400', // default border
                                    },
                                    '&:hover fieldset': {
                                      borderColor: 'grey.600', // on hover
                                    },
                                    '&.Mui-focused fieldset': {
                                      borderColor: 'black', // on focus
                                    },
                                  },
                                  '& .MuiInputLabel-root': {
                                    color: 'text.black', // label color
                                  },
                                  '& .MuiInputLabel-root.Mui-focused': {
                                    color: 'black', // label color on focus
                                  },
                        }}
                    />

                </Box>
                <Button
                    //   color="primary"
                    id="quickEnquirySubmit"
                    sx={{
                        bgcolor: "primary.main",
                        color: "text.primary",
                        "&:hover": {bgcolor: "primary.main"},
                    }}
                    disabled={isLoading}
                    onClick={handleLeadGeneration}
                >
                    {isLoading ? <CircularProgress size={24}/> : "Submit"}
                </Button>
            </Box>
        </Box>
    );
};

export default DoctorAppointmentForm2;
