import { alpha } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useContactPageStyles = makeStyles((theme) => ({
  contactUsSection: {
    minHeight: "calc(100vh - 72px)",
    // background: `-webkit-linear-gradient(45deg, ${theme.palette.secondary.main} 30%, ${theme.palette.primary.main} 90%)`,
    backgroundColor: alpha(theme.palette.primary.main, .05),
  },
  contactUsSectionBox: {
    width: "100%",
    height: "100%",
    background: "#fff",
    borderRadius: "2rem",
    boxShadow: `0 4px 45px ${alpha(theme.palette.primary.main, .05)}`,
    display: "flex",
    overflow: "hidden",
    [theme.breakpoints.down("sm")]: {
      flexDirection: "column-reverse",
    },
  },
  contactUsSectionInfo: {
    flex: 1,
    // backgroundColor: alpha(theme.palette.primary.main, .05),
    // padding: "2rem",
    display: "flex",
    flexDirection: "column",
    gap: "2rem",
  },
  contactUsSectionForm: {
    flex: 1,
  },
  contactUsSectionFormControl: {
    display: "grid",
    gap: "3rem",
    gridTemplateColumns: ".25fr 1fr",
    alignItems: "center",
    // justifyContent: "space-between",
    [theme.breakpoints.down("sm")]: {
      gridTemplateColumns: "1fr",
      gap: "1rem",
      alignItems: "start",
    },
  },
  contactUsSectionInputLabel: {
    position: "relative",
    fontSize: "16px",
    transform: "none",
    color: theme.palette.text.primary,
  },
  contactUsSectionFormInput: {
    width: "100% !important",
  },
  contactUsHeading: {
    fontSize: "2.2rem",
    textTransform: "uppercase",
    fontWeight: "500",
    color: theme.palette.primary.main,
  },
  contactUsInfoBox: {
    display: "grid",
    gridTemplateColumns: ".25fr 1fr",
    gap: "2rem",
    alignItems: "center",
    [theme.breakpoints.down("sm")]: {
      display: "grid",
      gridTemplateColumns: "1fr",
    },
  },
}));

export default useContactPageStyles;
