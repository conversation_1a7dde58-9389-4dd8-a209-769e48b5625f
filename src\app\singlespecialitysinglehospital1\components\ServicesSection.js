"use client";

import { useState, useEffect, useRef } from "react";
import { Box, Container, Typography, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import ServiceCard from "./ServiceCard";
import SectionLayoutSingleSpecialitySingleHospital from "../styledComponents/SectionLayoutSingleSpecialitySingleHospital";

const ServicesSection = ({ websiteServices }) => {
  const theme = useTheme();
  const [services, setServices] = useState([]);
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const cardsRef = useRef([]);

  useEffect(() => {
    if (websiteServices?.length > 0) {
      const Services = websiteServices.map((item) => item.service);
      setServices(Services);
    }
  }, [websiteServices]);

  useEffect(() => {
    if (isMobile) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("fade-in");
          }
        });
      },
      { threshold: 0.2 }
    );

    cardsRef.current.forEach((card) => {
      if (card) observer.observe(card);
    });

    return () => observer.disconnect();
  }, [services, isMobile]);

  return (
    <Box
    //  sx={{ paddingY: { xs: "24px", lg: "10px" } }}
     >
      <SectionLayoutSingleSpecialitySingleHospital>
        <Box
          sx={{
            textAlign: "center",
            mb: { xs: 4, sm: 5, md: 0 },
            maxWidth: "800px",
            mx: "auto",
          }}
        >
          <Typography
            variant="h5"
            sx={{
              color: "primary.main",
              mb: 1,
              display: "block",
              fontSize: { xs: "1.125rem", md: "1.25rem" },
            }}
          >
            OUR SERVICES
          </Typography>
          <Typography
            variant="h3"
            sx={{
              color: "#1a1a1a",
              fontSize: { xs: "1.875rem", sm: "2.25rem", md: "2.5rem" },
              lineHeight: 1.2,
            }}
          >
            {/* Experience the new dawn in Eye Care Services */}
            Restoring Vision with Precision Care & Technology
          </Typography>
        </Box>
        <Box
          component="section"
          sx={{
            position: "relative",
            pt: { lg: 4 },
            px: { lg: 2 },
            maxWidth: "100vw",
            display: "flex",
            justifyContent: "center",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              px: { xs: 0, sm: 2, md: 0 },
              width: "80%",
            }}
          >
            <Swiper
              modules={[Pagination, Autoplay]}
              spaceBetween={10}
              slidesPerView={1}
              loop={true}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              }}
              pagination={{
                dynamicBullets: true,
                clickable: true,
              }}
              breakpoints={{
                480: {
                  slidesPerView: 2,
                  spaceBetween: 15,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 20,
                },
                1024: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
                1280: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
              }}
              style={{
                paddingBottom: "30px",
                width: "100%",
              }}
            >
              {services.map((service, index) => (
                <SwiperSlide key={service.code}>
                  <div
                    ref={(el) => (cardsRef.current[index] = el)}
                    className="card-hidden"
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <ServiceCard
                      title={service.name}
                      description={service.short_description}
                      icon={service.image_url}
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>
        </Box>
      </SectionLayoutSingleSpecialitySingleHospital>
    </Box>
  );
};

export default ServicesSection;

// "use client";

// import { useState, useEffect, useCallback } from "react";
// import Image from "next/image";
// import { Swiper, SwiperSlide } from "swiper/react";
// import { Pagination, Autoplay } from "swiper/modules";
// import { styled, alpha, useTheme } from "@mui/material/styles";
// import {
//   Box,
//   Card,
//   CardContent,
//   Typography,
//   Dialog,
//   DialogTitle,
//   DialogContent,
//   IconButton,
//   Container,
// } from "@mui/material";
// import "swiper/css";
// import "swiper/css/pagination";

// const StyledCard = styled(Card)(({ theme }) => ({
//   height: "100%",
//   position: "relative",
//   display: "flex",
//   flexDirection: "column",
//   borderRadius: "16px",
//   backgroundColor: "rgb(255, 255, 255)",
//   border: "1px solid #eee",
//   transition: "all 0.3s ease-in-out",
//   cursor: "pointer",
//   minHeight: "300px",
//   width: "100%",
//   maxWidth: 350,
//   flexGrow: 1,
//   overflow: "hidden",
//   "&:hover": {
//     transform: "translateY(-8px)",
//     boxShadow: theme.shadows[8],
//     "& .overlay": {
//       top: "0%",
//     },
//   },
// }));

// // const services = [
// //   {
// //     code: "service1",
// //     name: "Comprehensive Eye Examination",
// //     short_description: "Complete evaluation of your eye health using state-of-the-art equipment",
// //     image_url: "https://images.unsplash.com/photo-1579684453423-f84349ef60b0?auto=format&fit=crop&q=80&w=2076"
// //   },
// //   {
// //     code: "service2",
// //     name: "Laser Eye Surgery",
// //     short_description: "Advanced laser treatments for various eye conditions with precision care",
// //     image_url: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&q=80&w=2070"
// //   },
// //   {
// //     code: "service3",
// //     name: "Pediatric Eye Care",
// //     short_description: "Specialized eye care services for children in a comfortable environment",
// //     image_url: "https://images.unsplash.com/photo-1616569214859-c0a469a3ef65?auto=format&fit=crop&q=80&w=2070"
// //   },
// //   {
// //     code: "service4",
// //     name: "Contact Lens Fitting",
// //     short_description: "Expert fitting of contact lenses with personalized recommendations",
// //     image_url: "https://images.unsplash.com/photo-1579684385127-1ef15d508118?auto=format&fit=crop&q=80&w=2940"
// //   }
// // ];

// const ServicesSection = ({ websiteServices }) => {
//   const theme = useTheme();
//   const [services, setServices] = useState([]);
//   const [selectedService, setSelectedService] = useState([]);
//   useEffect(() => {
//     if (websiteServices?.length > 0) {
//       const Services = websiteServices.map((item) => item.service);
//       setServices(Services);
//     }
//   }, [websiteServices]);

//   const handleOpenDialog = useCallback((service) => {
//     setSelectedService(service);
//   }, []);

//   const handleCloseDialog = useCallback(() => {
//     setSelectedService(null);
//   }, []);

//   return (
//     <Box
//       component="section"
//       sx={{
//         position: "relative",
//         py: { xs: 6, md: 0 },
//         px: { xs: 2, md: 0 },
//         maxWidth: "2150px",
//         mx: "auto",
//         width: "100%",
//         backgroundColor: "#f8f9fa",
//       }}
//     >
//       <Container maxWidth="lg">
//         <Box sx={{ textAlign: "center", mb: 6 }}>
//           <Typography
//             variant="overline"
//             sx={{
//               color: "primary.main",
//               fontWeight: 600,
//               mb: 1,
//               display: "block",
//             }}
//           >
//             OUR SERVICES
//           </Typography>
//           <Typography
//             variant="h3"
//             component="h2"
//             sx={{
//               fontWeight: 700,
//               color: "#1a1a1a",
//               mb: 2,
//             }}
//           >
//             Delhi Eye Care Services
//           </Typography>
//           <Typography
//             variant="body1"
//             sx={{
//               color: "#666",
//               maxWidth: "600px",
//               mx: "auto",
//             }}
//           >
//             Comprehensive eye care services delivered with expertise and
//             compassion
//           </Typography>
//         </Box>

//         <Box
//           sx={{
//             mt: 3,
//             px: { xs: 1, sm: 2, md: 0 },
//             width: "100%",
//             ".swiper-pagination-bullet": {
//               background: theme.palette.primary.main,
//             },
//           }}
//         >
//           <Swiper
//             modules={[Pagination, Autoplay]}
//             spaceBetween={24}
//             slidesPerView={1}
//             loop={true}
//             autoplay={{ delay: 3000, disableOnInteraction: false }}
//             pagination={{
//               dynamicBullets: true,
//               clickable: true,
//             }}
//             breakpoints={{
//               600: { slidesPerView: 2 },
//               1024: { slidesPerView: 3 },
//             }}
//             style={{
//               paddingBottom: "50px",
//               width: "100%",
//             }}
//           >
//             {services.map((service) => (
//               <SwiperSlide key={service.code}>
//                 <StyledCard onClick={() => handleOpenDialog(service)}>
//                   <Box
//                     className="overlay"
//                     sx={{
//                       position: "absolute",
//                       backgroundColor: alpha(theme.palette.primary.main, 0.1),
//                       height: "100%",
//                       width: "100%",
//                       top: "-100%",
//                       transition: "top 0.4s ease-in-out",
//                     }}
//                   />
//                   <CardContent
//                     sx={{
//                       display: "flex",
//                       flexDirection: "column",
//                       alignItems: "center",
//                       justifyContent: "center",
//                       flexGrow: 1,
//                       position: "relative",
//                       zIndex: 1,
//                       p: 4,
//                     }}
//                   >
//                     <Box
//                       sx={{
//                         backgroundColor: alpha(theme.palette.primary.main, 0.1),
//                         borderRadius: "50%",
//                         p: 3,
//                         mb: 3,
//                       }}
//                     >
//                       <Image
//                         src={service.image_url}
//                         width={60}
//                         height={60}
//                         alt={service.name}
//                         style={{ borderRadius: "50%" }}
//                       />
//                     </Box>

//                     <Typography
//                       variant="h5"
//                       component="h3"
//                       sx={{
//                         color: theme.palette.text.primary,
//                         textAlign: "center",
//                         fontWeight: 600,
//                         mb: 2,
//                       }}
//                     >
//                       {service.name}
//                     </Typography>
//                     <Typography
//                       variant="body1"
//                       sx={{
//                         color: theme.palette.text.secondary,
//                         textAlign: "center",
//                         textOverflow: "ellipsis",
//                         display: "-webkit-box",
//                         WebkitLineClamp: 3,
//                         WebkitBoxOrient: "vertical",
//                       }}
//                     >
//                       {service.short_description}
//                     </Typography>
//                   </CardContent>
//                 </StyledCard>
//               </SwiperSlide>
//             ))}
//           </Swiper>
//         </Box>
//       </Container>

//       <Dialog
//         open={!!selectedService}
//         onClose={handleCloseDialog}
//         maxWidth="sm"
//         fullWidth
//         PaperProps={{
//           sx: {
//             borderRadius: "16px",
//             p: 2,
//           },
//         }}
//       >
//         {selectedService && (
//           <>
//             <DialogTitle
//               sx={{
//                 display: "flex",
//                 alignItems: "center",
//                 justifyContent: "space-between",
//                 p: 2,
//               }}
//             >
//               <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
//                 {selectedService.name}
//               </Typography>
//               <IconButton
//                 onClick={handleCloseDialog}
//                 sx={{
//                   color: "text.secondary",
//                   "&:hover": { color: "primary.main" },
//                 }}
//               >
//                 X
//               </IconButton>
//             </DialogTitle>
//             <DialogContent dividers sx={{ p: 3 }}>
//               <Box
//                 sx={{
//                   display: "flex",
//                   flexDirection: "column",
//                   alignItems: "center",
//                   gap: 3,
//                 }}
//               >
//                 <Box
//                   sx={{
//                     width: "100%",
//                     height: 200,
//                     position: "relative",
//                     borderRadius: "12px",
//                     overflow: "hidden",
//                   }}
//                 >
//                   <Image
//                     src={selectedService.image_url}
//                     alt={selectedService.name}
//                     fill
//                     style={{ objectFit: "cover" }}
//                   />
//                 </Box>
//                 <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
//                   {selectedService.short_description}
//                 </Typography>
//               </Box>
//             </DialogContent>
//           </>
//         )}
//       </Dialog>
//     </Box>
//   );
// };

// export default ServicesSection;
