"use client";

import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { Box, Typography } from "@mui/material";

const BannerWidget = ({ value = [], title = "", setIsModalOpen = null}) => {
  const { imageUrl = "", redirection = {}, position = null } = value[0] || {};
  const { redirectionUrl = "" } = redirection || {};

  return (
    <Box

    sx={{maxWidth: "1400px", mx: "auto",py: { xs: 3, md: 4 },
    pb:{xs:2},
    px: { xs: 2, md: 4 },}}
    >
      {Boolean(title) && (
        <Typography
          variant="h3"
          sx={{
            marginBottom: {xs: "32px", md: "32px", lg: "48px" },
            color: "primary.main",
            fontSize: { xs: "2rem", sm: "2.5rem", md: "3rem" },
          }}
          align="center"
        >
          <Box
            sx={{
              display: "inline-block"
            }}
          >
            {title || ""}
          </Box>
        </Typography>
      )}
      <Box sx={{ width: "100%" }}>
        <img
          id={`bannerPos${position}`}
          alt="banner"
          src={getThumborUrl(imageUrl)}
          style={{
            height: "auto",
            borderRadius: "10px",
            width: "100%",
            cursor: redirectionUrl ? "pointer" : "auto",
          }}
          onClick={() => {
              if (!redirectionUrl || !setIsModalOpen) return;
              redirectionUrl.includes("pop1") ? setIsModalOpen(true): window.open(redirectionUrl || "#", "_blank")}
          }
        />
      </Box>
    </Box>
  );
};

export default BannerWidget;
