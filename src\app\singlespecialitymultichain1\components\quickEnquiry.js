"use client";

import {COUNTRIES, countryToFlag, LEAD_SOURCES} from "@/constants";
import {Button, CircularProgress, InputBase, MenuItem, Select, TextField, Typography} from "@mui/material";
import Box from "@mui/material/Box";
import {useContext, useState} from "react";
import axios from "axios";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_ENTERPRISE,
    API_SECTION_LEADS,
    API_ENDPOINT_GENERATE,
} from "@/constants";
import {AppContext} from "@/app/AppContextLayout";
import {
    FORM_PROCEDURES_32BY7,
    FORM_SLOTS_SESSION_32BY7
} from "@/app/singlespecialitymultichain1/constantsSingleSplMultiChain1";

const initialInput = {dialCode: "+91", type: 1};

const QuickEnquirySingleSplMultiCain = ({pageData = {}}) => {
    console.log(JSON.stringify(pageData))
    const {websiteData, setViewSnackbarMain, mobileView} =
        useContext(AppContext);
    const [input, setInput] = useState({...initialInput});
    const [inputLocation, setInputLocation] = useState();
    const [inputProcedures, setInputProcedures] = useState();
    const [inputDate, setInputDate] = useState();
    const [inputSlots, setInputSlots] = useState();
    const [isLoading, setIsLoading] = useState(false);
    const {enterprise_code: enterpriseCode = null, centers = []} = websiteData || {};

    const handleInputChange = (e) => {
        setInput((prev) => ({...prev, [e.target.name]: e.target.value}));
    };

    const handleLeadGeneration = async () => {
        setIsLoading(true);

        let eCode = enterpriseCode;
        if (inputLocation){
            eCode = inputLocation["epsCode"]
        }

        const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${eCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}${pageData?.productCode ? `?productCode=${extractCodeFromSlug(pageData.productCode)}` : ''}`;
        // const reqBody = {...input, leadSource};
        const reqBody = {
            phone: input.phone,
            firstName: input.firstName,
            dialCode: input.dialCode,
            leadSource: pageData.leadSource,
            type: 1,
            comments: [
                inputLocation && `Location: ${inputLocation["areaName"]} `,
                inputProcedures && `Procedure: ${inputProcedures["name"]} `,
                inputDate && `Slot Date: ${inputDate["date"]} `,
                inputSlots && `Slot Time: ${inputSlots["name"]} `,
                input["comments"] && `Comments  ${input["comments"]} `,
            ]
                .filter(Boolean) // Remove any undefined or falsey values
                .join(" | "), // Join the comments with a comma
        };
        if (pageData?.productCode){
            switch (pageData.leadSource){
                case LEAD_SOURCES.BLOG_DETAIL_PAGE:
                    reqBody.blogCode = pageData.productCode
                    break;
                case LEAD_SOURCES.PROCEDURE_PAGE:
                    reqBody.procedureCode = pageData.productCode
                    break;
                case LEAD_SOURCES.SPECIALITY_DETAIL_PAGE:
                    reqBody.specialityCode = pageData.productCode
                    break;
                case LEAD_SOURCES.CUSTOM_PAGE:
                    reqBody.customPageCode = pageData.productCode
                    break;
                case LEAD_SOURCES.DOCTOR_DETAIL_PAGE:
                    reqBody.doctorCode = pageData.productCode
                    break;
            }

        }
        if (pageData?.pageTitle){
            reqBody.pageTitle = pageData.pageTitle
        }

        try {
            const response = await axios.post(url, reqBody, {
                headers: {
                    "Content-Type": "application/json",
                    source: mobileView ? "mweb" : "website",
                },
            });
            const {data = {}, status = null} = response || {};
            if (status >= 200 && status < 300) {
                const {result = {}} = data || {};
                const {message = ""} = result || {};
                setViewSnackbarMain({
                    message: message,
                    type: "success",
                });

            }
        } catch (error) {
            setViewSnackbarMain({
                message: "Something went wrong. Please try again later!",
                type: "error",
            });
        } finally {
            setIsLoading(false);
        }
    };

    const extractCodeFromSlug = (slug) => {
        const segments = slug.split('-');
        return segments[segments.length - 1];
    }

    return (
        <Box
            sx={{
                padding: "18px",
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                borderRadius: "8px",
                bgcolor: "primary.main",
                alignSelf: "start",
                width: "100%"
            }}
        >
            <Typography variant="h6" align="center" sx={{color: "#fff"}}>
                Book Appointment
            </Typography>
            <Box sx={{display: "flex", flexDirection: "column", gap: "16px"}}>
                <Box sx={{display: "flex", flexDirection: "column", gap: "12px"}}>

                    <Box sx={{display: "flex", gap: "8px", flexDirection: {xs: "column", md: "row"}}}>
                        <TextField
                            id="quickEnquiryLocation"
                            select
                            size="small"
                            variant={"outlined"}
                            color="secondary"
                            onChange={(e) =>{
                                const { epsCode, areaName } = JSON.parse(e.target.value);
                                setInputLocation((prev) => ({ ...prev, epsCode: epsCode, areaName: areaName }))
                            }

                            }
                            label="Select Location"
                            sx={{
                                borderRadius: "4px",
                                fontSize: "14px",
                                flex: 1,
                                background: "#fff"
                            }}
                        >
                            {centers.map((center) => {
                                const { name = "", area = {}, code } = center || {};
                                const {name: areaName = ""} = area || {}
                                const fullName = name + " - " + areaName;
                                return (
                                    <MenuItem key={code}
                                              value={JSON.stringify({ epsCode: code, areaName: fullName })}>
                                        {fullName}
                                    </MenuItem>
                                );
                            })}
                        </TextField>


                        <TextField
                            id="quickEnquiryProcedure"
                            select
                            variant={"outlined"}
                            color="secondary"
                            size="small"
                            label="Select Procedure"
                            onChange={(e) =>
                                setInputProcedures((prev) => ({ ...prev, name: e.target.value }))
                            }
                            sx={{
                                borderRadius: "4px",
                                fontSize: "14px",
                                color: "secondary.main",
                                background: "#fff",
                                flex: 1,
                            }}
                        >
                            {FORM_PROCEDURES_32BY7.map((procedure) => {
                                const { name = "" } = procedure || {};
                                return (
                                    <MenuItem key={name} value={name}>
                                        {name}
                                    </MenuItem>
                                );
                            })}

                        </TextField>

                    </Box>
                    <Box sx={{display: "flex", gap: "8px", flexDirection: {xs: "column", md: "row"}}}>
                        <TextField
                            id="quickEnquiryDate"
                            type="date"
                            size="small"
                            variant="outlined"
                            color="secondary"
                            value={inputDate?.date || new Date().toISOString().split("T")[0]} // Default to today's date
                            onChange={(e) =>
                                setInputDate((prev) => ({ ...prev, date: e.target.value }))
                            } // Update state
                            inputProps={{
                                min: new Date().toISOString().split("T")[0], // Disable past dates
                            }}
                            sx={{
                                flex: 1,
                                minWidth: "150px",
                                "& .MuiInputBase-root": {
                                    borderRadius: "4px",
                                    fontSize: "16px",
                                    background: "#fff",
                                    cursor: "pointer", // Make the box clickable
                                },
                                "& .MuiOutlinedInput-notchedOutline": {
                                    borderColor: "#ccc",
                                },
                                "&:hover .MuiOutlinedInput-notchedOutline": {
                                    borderColor: "#888",
                                },
                                "&:focus-within .MuiOutlinedInput-notchedOutline": {
                                    borderColor: "#333",
                                },
                            }}
                        />

                        <TextField
                            id="quickEnquirySlots"
                            select
                            variant={"outlined"}
                            color="secondary"
                            size="small"
                            label="Select Slot Time"
                            onChange={(e) =>
                                setInputSlots((prev) => ({ ...prev, name: e.target.value }))
                            }
                            sx={{
                                borderRadius: "4px",
                                fontSize: "14px",
                                background: "#fff",
                                flex: 1,
                            }}
                        >
                            {FORM_SLOTS_SESSION_32BY7.map((session) => {
                                const { name = "" } = session || {};
                                return (
                                    <MenuItem key={name} value={name}>
                                        {name}
                                    </MenuItem>
                                );
                            })}
                        </TextField>
                    </Box>

                    <Box sx={{display: "flex", gap: "8px"}}>
                        <TextField
                            id="quickEnquiryCountryCode"
                            labelId="demo-customized-select-label"
                            value={input["dialCode"]}
                            size="small"
                            select
                            sx={{
                                borderRadius: "4px",
                                background: "#fff",
                            }}
                            onChange={(e) =>
                                setInput((prev) => ({...prev, dialCode: e.target.value}))
                            }
                        >
                            {COUNTRIES.map((country) => {
                                const {
                                    label = "",
                                    dial_code: dialCode = "",
                                    code = "IN",
                                    value = "",
                                } = country || {};
                                return (
                                    <MenuItem value={dialCode}>{`${countryToFlag(
                                        code
                                    )} ${value}`}</MenuItem>
                                );
                            })}
                        </TextField>

                        <TextField
                            id="quickEnquiryPhone"
                            variant={"outlined"}
                            color="secondary"
                            size="small"
                            label="Enter Phone Number"
                            onChange={(e) =>
                                setInput((prev) => ({...prev, phone: e.target.value}))
                            }
                            sx={{
                                borderRadius: "4px",
                                background: "#fff",
                                width: "100%"
                            }}
                        />
                    </Box>

                    <TextField
                        id="quickEnquiryFirstName"
                        variant={"outlined"}
                        color="secondary"
                        size="small"
                        label="Enter Name"
                        onChange={(e) =>
                            setInput((prev) => ({...prev, firstName: e.target.value}))
                        }
                        inputMode={"text"}
                        sx={{
                            borderRadius: "4px",
                            background: "#fff",
                            width: "100%",
                        }}
                    />

                    <TextField
                        id="quickEnquiryComments"
                        variant={"outlined"}
                        color="secondary"
                        label="Comments"
                        onChange={(e) =>
                            setInput((prev) => ({...prev, comments: e.target.value}))
                        }
                        multiline
                        minRows={2}
                        maxRows={4}
                        inputMode={"text"}
                        sx={{
                            borderRadius: "4px",
                            background: "#fff",
                            width: "100%"
                        }}
                    />

                </Box>
                <Button
                    //   color="primary"
                    id="quickEnquirySubmit"
                    sx={{
                        bgcolor: "secondary.main",
                        color: "#fff",
                        "&:hover": {bgcolor: "secondary.main"},
                    }}
                    disabled={isLoading}
                    onClick={handleLeadGeneration}
                >
                    {isLoading ? <CircularProgress size={24}/> : "Submit"}
                </Button>
            </Box>
        </Box>
    );
};

export default QuickEnquirySingleSplMultiCain;
