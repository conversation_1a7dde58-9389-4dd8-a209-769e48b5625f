import Script from 'next/script';
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import {getHostForServerComponent, getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";
import {
    API_ENDPOINT_AGGREGATED_REVIEWS,
    API_ENDPOINT_REVIEWS,
    API_SECTION_API,
    API_SECTION_PUBLIC,
    API_SECTION_WEBSITE,
    API_VERSION,
    HARBOR_API_DOCFYN_DOMAIN
} from "@/constants";
import {calculateAggregatedRating, convertRatingIn1to5} from "@/app/utils/reviewsUtils";
import {fetchWebsiteData} from "@/api/harbor.service";

const getWebsiteData = async () => {
    const domainName = getHostForServerComponent()
    try {
        const data = await fetchWebsiteData({domainName: domainName})

        if (data.code === 200){
            return data.result;
        }
    } catch (error) {
        console.log("getWebsiteData", error);
    }
};
const WebsiteStructureDataScript = async () => {
    const websiteData = await getWebsiteData();
    const {enterprise_code : enterpriseCode} = websiteData
    // Create structured data for the physician
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "Hospital",
        "description": websiteData.description,
        "url": `https://${getWebsiteHost()}/`
    };

    //Logo
    if (websiteData.logo_url) {
        structuredData.logo = getThumborUrl(websiteData.logo_url)
    }

    //Name
    if (websiteData.seoData && websiteData.seoData.length > 0) {
        if (websiteData.seoData[0].enterprise_name)
            structuredData.name = websiteData.seoData[0].enterprise_name
    }

    //Emails And Phones
    if ((websiteData.emails && websiteData.emails.length > 0)
        || (websiteData.phoneNumbers && websiteData.phoneNumbers.length > 0)) {
        structuredData.contactPoint = {
            "@type": "ContactPoint",
            "contactType": "Contact Us",
        }
        if (websiteData.emails && websiteData.emails.length > 0)
            structuredData.contactPoint.email = websiteData.emails[0].email

        if (websiteData.phoneNumbers && websiteData.phoneNumbers.length > 0)
            structuredData.contactPoint.telephone = websiteData.phoneNumbers[0].phone
    }

    //Social Media Links
    if (websiteData.socialMediaLinks && websiteData.socialMediaLinks.length > 0) {
        structuredData.sameAs = websiteData.socialMediaLinks.map(socialMediaLink => {
            return socialMediaLink.link
        })
    }

    // address
    if (websiteData.addresses && websiteData.addresses.length > 0) {
        structuredData.address = {
            "@type": "PostalAddress",
            "streetAddress": websiteData.addresses[0].line1 + websiteData.addresses[0].line2,
            "addressLocality": websiteData.addresses[0].city,
            "addressRegion": websiteData.addresses[0].state,
            "postalCode": websiteData.addresses[0].postal_code,
        };
    }

    //Services
    if (websiteData.addresses && websiteData.addresses.length > 0) {
        structuredData.service = websiteData.websiteServices.map(service => ({
            "@type": "Service",
            "serviceType": service.service.name,
        }))
    }

    //FAQs
    if (websiteData.faqs && websiteData.faqs.length > 0) {
        let name = "";
        if (websiteData.seoData && websiteData.seoData.length > 0) {
            if (websiteData.seoData[0].enterprise_name)
                name = websiteData.seoData[0].enterprise_name + " FAQs"
        }
        structuredData.mainEntity = {
            "@type": "FAQPage",
            "name": name,
            "mainEntity": websiteData.faqs.map(faq => ({
                "@type": "Question",
                "name": faq.question,
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": faq.answer
                }
            }))
        }
        if (websiteData.seoData && websiteData.seoData.length > 0) {
            if (websiteData.seoData[0].enterprise_name)
                structuredData.mainEntity.name = websiteData.seoData[0].enterprise_name + " FAQs"
        }
    }

    const getAggregatedRating = async () => {
        const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}/aggregatedOverallReviews?list=true&page=1`;
        try {
            // const response = await axios.get(url);
            // const { status = null, data = {} } = response || {};
            const response = await fetch(url);
            const status = response.status;
            const data = await response.json();
            if (status >= 200 && status < 300) {
                const {result = {}} = data || {};
                const {data: ratings = []} = result || {};
                return ratings;
            }
        } catch (error) {
            // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
        }
    };

    const getReviews = async () => {
        const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_REVIEWS}${enterpriseCode}/${API_ENDPOINT_AGGREGATED_REVIEWS}?list=true&page=1`;
        try {
            const response = await fetch(url);
            const status = response.status;
            const data = await response.json();
            // const { status = null, data = {} } = response || {};
            if (status >= 200 && status < 300) {
                const { result = {} } = data || {};
                const { data: reviews = [] } = result || {};
                return reviews;
            }
        } catch (error) {
        }
    };

    const overallRatingData = await getAggregatedRating()

    if (Object.keys(overallRatingData).length !== 0){
        const avgRating = calculateAggregatedRating(overallRatingData || []);
        const totalRatingCount = overallRatingData ?
            overallRatingData.reduce((acc, item) => acc + (item.totalReviews || 0), 0) : 0;

        structuredData.aggregateRating = {
            "@context": "https://schema.org",
            "@type": "AggregateRating",
            "ratingValue": avgRating || "0", // Default to "0" if avgRating is null
            "ratingCount": totalRatingCount,
        }

        const latestReviews = await getReviews();

        if (latestReviews && latestReviews.length > 0) {
            structuredData.reviews = latestReviews
                .filter(review => review && review.givenByEntity && review.postingDate && review.rating)
                .map(review => ({
                    "@type": "Review",
                    "author": {
                        "@type": "Person",
                        "name": review.givenByEntity || "Anonymous",
                    },
                    "datePublished": review.postingDate || null,
                    "reviewRating": {
                        "@type": "Rating",
                        "ratingValue": convertRatingIn1to5(review) || null,
                    },
                    ...(review.content ? { "reviewBody": review.content } : {})
                }));
        }
    }




    return (
        <Script
            type="application/ld+json"
            dangerouslySetInnerHTML={{__html: JSON.stringify(structuredData)}}
        />
    );
};
export default WebsiteStructureDataScript;
