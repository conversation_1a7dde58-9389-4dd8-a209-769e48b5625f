"use client";

import { useState } from "react";
import {Typography, Box} from "@mui/material";
import Drawer from "@mui/material/Drawer";
import MenuOpenOutlinedIcon from "@mui/icons-material/MenuOpenOutlined";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import NavbarDropdown from "./navbarDropdown";
import NavbarDropdownItem from "./navbarDropdownItem";
import Link from "next/link";
import BookAppointment from "./bookAppointment";
import PrimaryButton from "../styledComponents/PrimaryButton";
import Image from "next/image";

const NavbarMenu = ({ navbarItemsList = [] }) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  }

  return (
    <Box sx={{ display: { xs: "flex", md: "none" } }}>
      {/* <MenuOpenOutlinedIcon
        fontSize="large"
        onClick={() => setIsDrawerOpen(true)}
      /> */}
      <Image onClick={() => setIsDrawerOpen(true)} src={"/hamburger.svg"} alt="menu" width={30} height={30} />
      <Drawer
        open={isDrawerOpen}
        anchor="right"
        onClose={() => setIsDrawerOpen(false)}
      >
        <Box sx={{ width: "100vw", padding: "8px" }}>
          <Box sx={{color:"#1a1a1a"}}>
          <CloseIcon onClick={() => setIsDrawerOpen(false)} />
          </Box>
          <Box
            sx={{
              color: "primary.main",
              listStyle: "none",
              fontSize: "16px",
              display: "flex",
              flexDirection: "column",
              gap: "24px",
              padding: "16px 24px",
            }}
          >
            {(navbarItemsList[0]?.sections || []).map((item, index) => {
              const {
                displayName = "",
                redirection = {},
                sections = null,
                type = 1,
              } = item || {};
              const { redirectionUrl = "" } = redirection || {};
              if (type === 2)
                return (
                  <li key={index} id={`navbarSection1Item${index}`} onClick={handleCloseDrawer}>
                    <Link href={redirectionUrl} target="_blank">
                    <Typography
                    color={"#333"}
                    fontWeight={500}
                              fontSize="15px"
                          >
                              {displayName || ""}
                          </Typography>
                    </Link>
                  </li>
                );
              else if (sections)
                return (
                  <NavbarDropdown
                    id={`navbarSection0Item${index}`}
                    key={`${displayName}${index}`}
                    navbarItem={item}
                    isDrawerOpen={true}
                    handleCloseDrawer={handleCloseDrawer}
                  />
                );
              else
                return (
                  <li id={`navbarSection0Item${index}`} onClick={handleCloseDrawer}>
                    <Link href={redirectionUrl}>
                    <Typography
                    color={"#333"}
                    fontWeight={500}
                              fontSize="15px"
                          >
                              {displayName || ""}
                          </Typography>
                          </Link>
                  </li>
                );
            })}
          </Box>
          <Box sx={{marginLeft: "24px"}}>
          <PrimaryButton
            id="contactUsWidget"
            onClick={() => router.push("/appointment")}
          >
            Book Appointment
          </PrimaryButton>
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
};

export default NavbarMenu;
