"use client";

import { useTheme } from "@emotion/react";
import {
  Box,
  Typography,
} from "@mui/material";
import MedicationIcon from "@mui/icons-material/Medication";
import { alpha } from "@mui/material/styles";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Slide from "@mui/material/Slide";
import { forwardRef, useEffect, useState } from "react";
import {getThumborUrl} from "@/app/utils/getThumborUrl";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const SpecialityCard = ({
  speciality = {},
  id
}) => {
  const theme = useTheme();
  const router = useRouter();
  const [selectedCenter, setSelectedCenter] = useState(null);
  const {
    displayName: specialityName = "",
    shortDescription = "",
    iconUrl = "",
    code: specialityCode = null,
    seoSlug = "",
  } = speciality || {};

  const handleSpecialityClick = () => {
      router.push(`/specialities/${seoSlug}`);
  };

  return (
    <>
      <Box
        id={id}
        sx={{
          padding: "16px 24px",
          boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          alignItems: "center",
          borderRadius: "12px",
          cursor: "pointer",
        }}
        onClick={handleSpecialityClick}
      >
        <Box
          sx={{
            height: "64px",
            width: "64px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: `${alpha(theme.palette.primary.main, 0.5)}`,
            borderRadius: "10%",
          }}
        >
          {iconUrl ? (
            <Image
              alt="speciality"
              height={35}
              width={35}
              src={getThumborUrl(iconUrl, 35, 35)}
            />
          ) : (
            <MedicationIcon fontSize="large" sx={{ color: "#fff" }} />
          )}
        </Box>
        <Box>
          <Typography align="center" variant="h6" sx={{ fontSize: "18px" }}>
            {specialityName || ""}
          </Typography>
          <Typography
            variant="subtitle1"
            align="center"
            sx={{ fontSize: "14px", color: "rgba(0, 0, 0, 0.6)" }}
          >
            {shortDescription || ""}
          </Typography>
        </Box>
      </Box>
    </>
  );
};

export default SpecialityCard;
