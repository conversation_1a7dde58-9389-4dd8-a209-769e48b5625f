"use client";

import Box from "@mui/material/Box";
import SectionLayout from "../styledComponents/SectionLayout";
import FaqsSection from "../components/faqsSection";
import BlogCard from "../components/blogCard";
import {
  Button,
  Chip,
  FormControl,
  InputBase,
  MenuItem,
  Pagination,
  Select,
  Typography,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ReviewCard from "../components/reviewCard";
import { useContext } from "react";
import { AppContext } from "../../AppContextLayout";

export default function Reviews() {
  const handleClick = () => {};
  const { websiteData } = useContext(AppContext);
  const { testimonials = [] } = websiteData || {};

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayout>
        <Typography variant="h4">Patient Reviews</Typography>
        <Box
          sx={{ display: "flex", flexDirection: "column", gap: "48px", mt: 2 }}
        >
          {/* <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            <FormControl>
              <Select
                displayEmpty
                input={
                  <InputBase
                    sx={{
                      border: "1px solid #d3d4db",
                      padding: "8px 12px",
                      borderRadius: "6px",
                    }}
                  />
                }
                value="0"
                inputProps={{ "aria-label": "Without label" }}
              >
                <MenuItem disabled value="0">
                  Filter by speciality
                </MenuItem>
                <MenuItem value={"Cardiology"}>Cardiology</MenuItem>
              </Select>
            </FormControl>
          </Box> */}
          <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: {xs: "1fr", sm: "1fr 1fr", lg: "repeat(3, 1fr)"},
                columnGap: "32px",
                rowGap: "64px",
              }}
            >
              {testimonials.map((testimonial, index) => {
                return <ReviewCard key={index} testimonial={testimonial} />;
              })}
            </Box>
            {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
              <Pagination count={10} variant="outlined" shape="rounded" />
            </Box> */}
          </Box>
        </Box>
      </SectionLayout>
      {/* <FaqsSection /> */}
    </Box>
  );
}
