'use client';

import React from 'react';
import { Box } from '@mui/material';

const ProgressIndicator = ({ currentStep, totalSteps = 4, theme }) => {
  return (
    <Box sx={{ px: { xs: 2, sm: 3 }, pt: 2, pb: 0 }}>
      <Box sx={{ display: 'flex', width: '100%', mb: 2 }}>
        {Array.from({ length: totalSteps }).map((_, i) => (
          <Box
            key={i}
            sx={{
              height: 4,
              flex: 1,
              bgcolor: i < currentStep ? theme.palette.primary.main : 'rgba(0, 0, 0, 0.08)',
              borderRadius: 2,
              mx: 0.5,
              transition: 'all 0.3s ease'
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

export default ProgressIndicator;
