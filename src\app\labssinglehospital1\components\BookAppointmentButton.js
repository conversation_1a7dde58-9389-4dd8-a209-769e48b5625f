import { alpha, Box, Button, styled } from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import Link from "next/link";

const IconContainer = styled(Box)(({ theme }) => ({
  backgroundColor:(theme.palette.secondary.main),
  padding: theme.spacing(1.5),
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));
const TextContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  padding: theme.spacing(1, 2),
  "&:hover": {
    backgroundColor: theme.palette.primary.main,
  },
  transition: theme.transitions.create("background-color"),
}));

const StyledButton = styled(Button)({
  padding: 0,
  borderRadius: "8px",
  textTransform: "none",
  minWidth: "unset",
  "&:hover": {
    backgroundColor: "transparent",
  },
});

export function BookAppointmentButton() {
  return (
    <StyledButton  disableRipple>
    <Link href="/contact-us">
    <Box sx={{ display: "flex", overflow: "hidden", borderRadius: "8px" }}>
        <IconContainer  >
          <CalendarMonthIcon sx={{ fontSize: 20, color: "text.main" }} />
        </IconContainer>
        <TextContainer>
          <Box component="span" sx={{ color: "text.primary", fontWeight: 500, fontSize: 15 }}>
            Book Appointment
          </Box>
        </TextContainer>
      </Box>
      </Link>
    </StyledButton>
  );
}