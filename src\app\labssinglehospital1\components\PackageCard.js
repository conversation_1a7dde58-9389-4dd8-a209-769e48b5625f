'use client';

import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  alpha,
  useTheme,
  Divider,
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { addToCart, removeFromCart, selectIsItemInCart } from '../redux/slices/cartSlice';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const PackageCard = ({ packageData }) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const router = useRouter();

  // Check if this item is already in the cart (specifying it's a package)
  const isInCart = useSelector(selectIsItemInCart(packageData.id, 'package'));

  const handleAddToCart = (e) => {
    e.stopPropagation(); // Prevent navigation when clicking the Add button

    // Calculate discount percentage if not provided
    const discountPercentage = packageData.discountPercentage ||
      (packageData.originalPrice && packageData.discountedPrice ?
        Math.round(((packageData.originalPrice - packageData.discountedPrice) / packageData.originalPrice) * 100) : 0);

    dispatch(addToCart({
      ...packageData,
      itemType: 'package', // Specify this is a package, not a test
      title: packageData.name || packageData.title || 'Package',
      description: packageData.description || packageData.shortDescription || packageData.alternativeNames,
      totalTests: packageData.totalTestsIncluded || packageData.labTests?.length || 1,
      discount: packageData.discount || `${discountPercentage}% off`,
      discountPercentage: discountPercentage
    }));
  };

  const handleRemoveFromCart = (e) => {
    e.stopPropagation(); // Prevent navigation when clicking the Remove button
    dispatch(removeFromCart({ id: packageData.id, itemType: 'package' }));
  };

  const handleCardClick = () => {
    // Use seoSlug if available, otherwise generate a slug from the name/title
    const slug = packageData.seoSlug || (packageData.name || packageData.title).toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    router.push(`/tests/${slug}`);
  };

  return (
    <Box
      onClick={handleCardClick}
      sx={{
        p: { xs: 2, md: 2 },
        cursor: 'pointer',
        height: '100%',
        minHeight: '250px',
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        transition: 'all 0.2s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        },
      }}
    >
      <Box>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            mb: 2,
          }}
        >
          <Box
            sx={{
              width: { xs: 60, md: 80 },
              height: { xs: 60, md: 80 },
              minWidth: { xs: 60, md: 80 },
              borderRadius: '12px',
              color: theme.palette.primary.main,
              border: `1px solid ${theme.palette.primary.main}`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: { xs: '32px', md: '48px' }
            }}
          >
            {packageData.iconUrl ? (
              <Image
                src={packageData.iconUrl}
                alt={`${packageData.name || packageData.title || 'Package'} icon`}
                width={80}
                height={80}
                style={{ objectFit: "contain" }}
              />
            ) : (
              <Image
                src="/package.png"
                alt={`${packageData.name || packageData.title || 'Package'} icon`}
                width={50}
                height={50}
                style={{ objectFit: "contain" }}
              />
            )}
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="subtitle1"
              fontWeight="medium"
              sx={{
                mb: 0.5,
                color: "text.black",
                height: "2.5rem",
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: 1.25
              }}
            >
              {packageData.name || packageData.title || 'Package'}
            </Typography>
            {packageData.description && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mb: 1,
                  fontSize: '0.8rem',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  lineHeight: 1.3
                }}
              >
                {packageData.description}
              </Typography>
            )}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {/* Show tests included tag if there are 1 or more tests included */}
              {(packageData.totalTestsIncluded >= 1 || (packageData.packageItems && packageData.packageItems.length >= 1)) && (
                <Typography
                  variant="caption"
                  sx={{
                    backgroundColor: theme.palette.primary.main,
                    px: 1,
                    py: 0.5,
                    borderRadius: '4px',
                    color: theme.palette.text.primary,
                    fontSize: '0.7rem',
                  }}
                >
                  {`${packageData.totalTestsIncluded || packageData.packageItems?.length} ${(packageData.totalTestsIncluded || packageData.packageItems?.length) === 1 ? 'Test' : 'Tests'} included`}
                </Typography>
              )}
              <Typography
                variant="caption"
                sx={{
                  color: 'white',
                  backgroundColor: '#9c27b0',
                  px: 1,
                  py: 0.5,
                  borderRadius: '4px',
                  fontSize: '0.7rem',
                }}
              >
                Package
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box>
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            {/* If discount percentage is null or 0, show only original price */}
            {(!packageData.discountPercentage || packageData.discountPercentage <= 0) ? (
              <Typography
                variant="h6"
                component="span"
                sx={{
                  fontSize: { xs: '1.1rem', md: '1.25rem' },
                  fontWeight: 'bold',
                  color: 'text.black',
                  mr: 1,
                }}
              >
                {packageData.currency?.symbol || '₹'}{packageData.originalPrice}
              </Typography>
            ) : (
              // Otherwise show discounted price with strikethrough original price
              <>
                <Typography
                  variant="h6"
                  component="span"
                  sx={{
                    fontSize: { xs: '1.1rem', md: '1.25rem' },
                    fontWeight: 'bold',
                    color: 'text.black',
                    mr: 1,
                  }}
                >
                  {packageData.currency?.symbol || '₹'}{packageData.discountedPrice}
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    textDecoration: 'line-through',
                    color: 'text.secondary',
                    fontSize: '0.8rem',
                  }}
                >
                  {packageData.currency?.symbol || '₹'}{packageData.originalPrice}
                </Typography>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    color: 'success.main',
                    fontSize: '0.8rem',
                    fontWeight: 'medium',
                  }}
                >
                  {packageData.discount || `${packageData.discountPercentage}% off`}
                </Typography>
              </>
            )}
          </Box>
          {isInCart ? (
            <Button
              variant="outlined"
              color="error"
              onClick={handleRemoveFromCart}
              sx={{
                borderRadius: '6px',
                textTransform: 'none',
                minWidth: '80px',
                py: 0.75,
                fontSize: '0.9rem',
              }}
            >
              Remove
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleAddToCart}
              sx={{
                borderRadius: '6px',
                textTransform: 'none',
                minWidth: '80px',
                py: 0.75,
                fontSize: '0.9rem',
                color: theme.palette.text.primary,
                bgcolor: theme.palette.primary.main,
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.8),
                },
              }}
            >
              Add
            </Button>
          )}
        </Box>

        {(packageData.turnaroundTime || packageData.reportHours) && (
          <>
            <Divider sx={{ my: 1.5, borderColor: '#e0e0e0' }} />
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <AccessTimeIcon sx={{ color: 'text.secondary', fontSize: '0.9rem' }} />
              <Typography
                variant="body2"
                sx={{
                  color: 'text.secondary',
                  fontSize: '0.8rem',
                  fontWeight: 'medium',
                  textAlign: 'left'
                }}
              >
                {packageData.turnaroundTime || `Reports within ${packageData.reportHours} hours`}
              </Typography>
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};

export default PackageCard;
