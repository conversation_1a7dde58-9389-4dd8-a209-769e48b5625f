import dynamic from "next/dynamic";
const BlogsSection = dynamic(() => import("./components/blogsSection"));
const FaqsSection = dynamic(() => import("./components/faqsSection"));
const ReviewsSection = dynamic(
  () => import("../commoncomponents/reviewsSection")
);
const VideosSection = dynamic(() => import("./components/videosSection"));
const ServicesSection = dynamic(() => import("./components/ServicesSection"));
const ProcedureSection = dynamic(() => import("./components/procedureSection"));
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
} from "@/constants";
import WebsiteStructureDataScript from "@/app/commoncomponents/websiteStructureData";
import { isEmptyObject } from "@/app/utils/isEmptyObject";
import { getWebsiteHost } from "@/app/utils/serverOnly/serverUtils";
import DoctorsSection from "./components/DoctorsSection";
import HomeCarouselWidget from "./components/home/<USER>";
import WhyChooseUsSection from "./components/WhyChooseUs";
import GallerySection from "./components/GallerySection";
import FAQSection from "./components/Faq";
import BlogSection from "./components/Blogs";
import TestimonialSlider from "./components/Testimonials";
import CounterSection from "../commoncomponents/CounterSection";
import Carousal from "./Carousal";

const getWebsiteData = async () => {
  const domainName = getWebsiteHost();
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function Home() {
  const websiteData = await getWebsiteData();
  const {
    banners = [],
    multiMedia = [],
    testimonials = [],
    blogs = [],
    faqs = [],
    websiteServices = [],
  } = websiteData || {};
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  return (
    <main>
      {!isEmptyObject(websiteData) && (
        <WebsiteStructureDataScript websiteData={websiteData} />
      )}
      {/* <HomeCarouselWidget banners={banners} /> */}
      <Carousal banners={banners} />
      {websiteServices && websiteServices.length > 0 && (
        <ServicesSection websiteServices={websiteServices} />
      )}
      <ProcedureSection />
      <DoctorsSection />
      {multiMedia && multiMedia.length > 0 && (
        <VideosSection multiMedia={multiMedia} />
      )}
      {multiMedia && multiMedia.length > 0 && (
        <GallerySection multiMedia={multiMedia} />
      )}
      {/* <GallerySection /> */}
      {/* <SectionLayoutSingleSpecialitySingleHospital>
        <ReviewsSection
          enterpriseCode={enterpriseCode}
          testimonials={testimonials}
          showDefaultReviews={true}
        />
        <HealthTipSection />
        </SectionLayoutSingleSpecialitySingleHospital> */}
      <WhyChooseUsSection />
      <CounterSection />
      <TestimonialSlider />
      {blogs && blogs.length > 0 && <BlogSection blogs={blogs} />}
      {faqs && faqs.length > 0 && <FAQSection faqs={faqs} />}
    </main>
  );
}
