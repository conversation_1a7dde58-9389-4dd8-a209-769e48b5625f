import { headers } from "next/headers";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_ENDPOINT_SPECIALITY,
  API_SECTION_ENTERPRISE,
} from "@/constants";
import axios from "axios";
import { getEnterpriseCode } from "@/app/oasis/blogs/[blogCode]/layout";

const getSpecialityDetails = async (enterpriseCode, specialityCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?code=${specialityCode}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { specialities = [] } = result || {};
      const { seoTitle = "", seoDescription = "" } = specialities[0] || {};
      return {
        title: seoTitle || "",
        description: seoDescription || "",
      };
    }
  } catch (error) {
    console.log(error);
  }
};

export const generateMetadata = async ({ params }) => {
  const { specialityCode = null } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode();
    const metaData = await getSpecialityDetails(enterpriseCode, specialityCode);
    return metaData;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
