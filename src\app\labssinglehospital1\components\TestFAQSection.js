'use client';

import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

// Default FAQs as fallback
const defaultFaqs = [
  {
    question: "What is the purpose of this test?",
    answer: "This test is designed to identify the underlying cause of fever by checking for various infections and conditions that can cause elevated body temperature."
  },
  {
    question: "Do I need to fast before this test?",
    answer: "For most components of this test package, fasting is not required. However, if the package includes blood glucose testing, you may need to fast for 8-12 hours before the test. Please confirm with your healthcare provider."
  },
  {
    question: "How long will it take to get the results?",
    answer: "Results are typically available within 10-36 hours after sample collection, depending on the specific tests included in the package."
  }
];

const TestFAQSection = ({ faqs = defaultFaqs }) => {
  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: '12px', border: '1px solid #e0e0e0' }}>
      <Typography variant="h6" fontWeight="medium" sx={{ mb: 3 }} color="text.black">
        Frequently Asked Questions
      </Typography>

      <Box sx={{ mb: 2 }}>
        {faqs.map((faq, index) => {
          // Handle both API format and our internal format
          const question = faq.question || '';
          const answer = faq.answer || '';

          return (
            <Accordion
              key={index}
              elevation={0}
              disableGutters
              sx={{
                '&:before': { display: 'none' },
                borderBottom: index < faqs.length - 1 ? '1px solid #f0f0f0' : 'none',
                mb: 0
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{ px: 1 }}
              >
                <Typography fontWeight="medium" color="text.black">
                  {question}
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ px: 1, pt: 0, pb: 2 }}>
                {/* Use dangerouslySetInnerHTML in case the answer contains HTML */}
                <Typography
                  variant="body2"
                  color="text.secondary"
                  dangerouslySetInnerHTML={{ __html: answer }}
                />
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Box>
    </Paper>
  );
};

export default TestFAQSection;
