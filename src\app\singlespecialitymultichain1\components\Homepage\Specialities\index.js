import {Box, Typography} from "@mui/material";
import homepageStyles from "../styles";
import useStyles from "../../../styles";
import axios from "axios";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_ENTERPRISE,
    API_ENDPOINT_SPECIALITY,
} from "@/constants";
import {useContext, useEffect, useState} from "react";
import {AppContext} from "@/app/AppContextLayout";
import SpecialityCard from "./specialityCard";
import SectionLayout from "@/app/singlespecialitymultichain1/components/SectionLayout";

const Specialities = () => {
    const commonClasses = useStyles();
    const classes = homepageStyles();
    const [specialities, setSpecialities] = useState([]);
    const {websiteData} = useContext(AppContext);
    const {enterprise_code: enterpriseCode = null} = websiteData || {};

    const getSpecialities = async () => {
        const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}?list=true`;
        try {
            const response = await axios.get(url);
            const {status = null, data = {}} = response || {};
            if (status >= 200 && status < 300) {
                const {result = {}} = data || {};
                const {specialities = []} = result || {};
                setSpecialities(specialities);
            }
        } catch (error) {
            //   setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
        }
    };

    useEffect(() => {
        if (enterpriseCode) {
            getSpecialities();
        }
    }, [enterpriseCode]);

    if (specialities.length <= 1) return <></>;
    return (
        <SectionLayout
            id="specialities">
            <Box sx={{
                display: "flex", flexDirection: {
                    xs: "column", // Stack on mobile (xs)
                    md: "row",    // Side-by-side on desktop (md and up)
                }, gap: "64px"
            }}>
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "16px",
                    }}
                >
                    <Typography
                        variant="h5"
                        sx={{
                            textTransform: "uppercase",
                            color: "primary.main",
                        }}
                    >
                        Specialities
                    </Typography>
                    <Typography
                        variant="h3"
                        align="start"
                        sx={{color: "#333333"}}
                    >
                        Exceptional Care Tailored to Your Needs
                    </Typography>
                </Box>
                <Box
                    sx={{
                        display: "grid",
                        gridTemplateColumns: {
                            xs: "1fr",
                            sm: "1fr 1fr",
                            lg: specialities.length === 2 ? "repeat(2, 1fr)" : "repeat(3, 1fr)",
                        },
                        columnGap: "16px",
                        rowGap: "32px",
                    }}
                >
                    {specialities.map((speciality, index) => {
                        const {code = null} = speciality || {};
                        return (
                            <SpecialityCard

                                id={`speciality${index}`}
                                key={code}
                                speciality={speciality}
                            />
                        );
                    })}
                </Box>
            </Box>
        </SectionLayout>
    );
};

export default Specialities;
