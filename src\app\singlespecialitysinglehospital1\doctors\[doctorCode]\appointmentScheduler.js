"use client";

import { createContext, useContext, useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import Typography from "@mui/material/Typography";
import Image from "next/image";
import AppointmentTabs from "./appointmentTabs";
import RequestOtp from "./requestOtp";
import VerifyOtp from "./verifyOtp";
import PatientDetails from "./patientDetails";
import BookingConfirm from "./bookingConfirm";
import { isEmptyObject } from "@/app/utils/isEmptyObject";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_SECTION_APPOINTMENT,
  API_ENDPOINT_SLOTS,
} from "@/constants";
import { AppContext } from "@/app/AppContextLayout";
import OutlinedButton from "@/app/oasis/styledComponents/OutlinedButton";
import { useRouter } from "next/navigation";

export const AppointmentSchedulerContext = createContext();

const AppointmentScheduler = ({
  doctorCode = null,
  consultationFee = null,
  isNativeAppointmentModuleEnabled,
  connect2ClinicRedirectionUrl,
}) => {
  const router = useRouter();
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const [slots, setSlots] = useState([]);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [bookingInfo, setBookingInfo] = useState({ code: null, status: null });
  const [isOpenComponent, setIsOpenComponent] = useState({
    0: true,
    1: false,
    2: false,
    3: false,
    4: false,
  });
  const [dialCode, setDialCode] = useState("+91");
  const [phone, setPhone] = useState(null);
  const [patientDetails, setPatientDetails] = useState({});

  const getAppointmentSlots = async () => {
    if (!doctorCode || !isNativeAppointmentModuleEnabled) return;
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_SECTION_APPOINTMENT}${API_ENDPOINT_SLOTS}?code=${doctorCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { slots = [] } = result || {};
        setSlots(slots || []);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  const handleComponentDisplay = (index) => {
    const copyOfIsOpenComponent = { ...isOpenComponent };
    for (let i in copyOfIsOpenComponent) {
      if (index == i) copyOfIsOpenComponent[i] = true;
      else copyOfIsOpenComponent[i] = false;
    }
    setIsOpenComponent(copyOfIsOpenComponent);
  };

  const contextValue = {
    dialCode,
    setDialCode,
    phone,
    setPhone,
    handleComponentDisplay,
    selectedAppointment,
    setSelectedAppointment,
    doctorCode,
    bookingInfo,
    setBookingInfo,
    patientDetails,
    setPatientDetails,
    getAppointmentSlots,
    consultationFee,
  };

  useEffect(() => {
    getAppointmentSlots();
  }, [doctorCode]);

  if (!isNativeAppointmentModuleEnabled && connect2ClinicRedirectionUrl) return <></>

  return (
    <Box
      sx={{
        padding: "16px 0",
        // height: "500px",
        background: "#fff",
        boxShadow: "0 1px 6px rgba(0,0,0,.15)",
        borderRadius: "8px",
        maxHeight: "540px",
        overflowY: "auto",
        alignSelf: "start",
      }}
    >
      <Typography
        variant="h6"
        align="center"
        sx={{
          padding: "0 16px",
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: "8px",
          justifyContent: "center",
        }}
      >
        <Image
          alt="appointment"
          src="/appointment.svg"
          height={32}
          width={32}
        />
        Book Appointment
      </Typography>
      <Divider />
      {!isNativeAppointmentModuleEnabled || slots.length === 0 ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: "24px",
            padding: "24px 32px",
          }}
        >
          <Typography variant="h5" sx={{ fontSize: "20px" }} align="center">
            Call us to book appointment
          </Typography>
          <OutlinedButton id="doctorAppointmentContactUs" onClick={() => router.push("/contact-us")}>
            Contact us
          </OutlinedButton>
        </Box>
      ) : (
        <AppointmentSchedulerContext.Provider value={contextValue}>
          {isOpenComponent[0] && <AppointmentTabs slots={slots} />}
          {isOpenComponent[1] && !isEmptyObject(selectedAppointment) && (
            <RequestOtp enterpriseCode={enterpriseCode}/>
          )}
          {isOpenComponent[2] && !isEmptyObject(selectedAppointment) && (
            <PatientDetails />
          )}
          {isOpenComponent[3] && !isEmptyObject(selectedAppointment) && (
            <BookingConfirm />
          )}
        </AppointmentSchedulerContext.Provider>
      )}
    </Box>
  );
};

export default AppointmentScheduler;
