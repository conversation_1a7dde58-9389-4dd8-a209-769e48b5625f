"use client"

import SectionLayoutChainTemp2 from "@/app/hospitalchaintemplate2apollo/styledComponents/SectionLayoutChainTemp2";
import ReviewsSection from "@/app/commoncomponents/reviewsSection";
import {useContext} from "react";
import {AppContext} from "@/app/AppContextLayout";

const HomepageReviewWidget = () => {
    const {websiteData} = useContext(AppContext);
    const {
        banners = [],
        multiMedia = [],
        testimonials = [],
        blogs = [],
        faqs = [],
        websiteServices = []
    } = websiteData || {};
    const {enterprise_code: enterpriseCode = null} = websiteData || {};
    return (

        <SectionLayoutChainTemp2>
            <ReviewsSection enterpriseCode={enterpriseCode} testimonials={testimonials} showDefaultReviews={true} />
        </SectionLayoutChainTemp2>
    )
}

export default HomepageReviewWidget;