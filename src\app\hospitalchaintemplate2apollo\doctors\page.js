"use client";

import Box from "@mui/material/Box";
import SectionLayoutAspire from "../styledComponents/SectionLayoutChainTemp2";
import InputBase from "@mui/material/InputBase";
import SearchIcon from "@mui/icons-material/Search";
import {
  FormControl,
  InputLabel,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import Pagination from "@mui/material/Pagination";
import DoctorsList from "./doctorsList";
import FaqsSection from "../components/faqsSection";
import { useContext, useEffect, useState } from "react";
import Image from "next/image";
import axios from "axios";
import { AppContext } from "../../AppContextLayout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_ENDPOINT_DOCTORS,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_SEARCH,
  API_ENDPOINT_SPECIALITY,
} from "@/constants";
import { useRouter } from "next/navigation";
import DoctorListItem from "./doctorListItem";

export default function Doctors() {
  const router = useRouter();
  const { setViewSnackbarMain, websiteData } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, centers = [] } =
    websiteData || {};
  const [doctors, setDoctors] = useState([]);
  const [specialities, setSpecialities] = useState([]);
  const [selectedSpeciality, setSelectedSpeciality] = useState();
  const [searchInput, setSearchInput] = useState("");
  const [filteredDoctors, setFilteredDoctors] = useState([]);
  const [filteredBySpecialityDoctors, setFilteredBySpecialityDoctors] =
    useState([]);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const handleSearchInput = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    const searchedDoctors = doctors.filter((doctor) => {
      const { doctorDetails = {} } = doctor || {};
      const { name: doctorName = "" } = doctorDetails || {};
      return doctorName.toLowerCase().includes(value.toLowerCase());
    });
    setFilteredDoctors(searchedDoctors);
  };

  const getDoctors = async (locationCode, speciality) => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_ENDPOINT_DOCTORS}?enterpriseCode=${locationCode || enterpriseCode}&list=true${(speciality && speciality !== "all" ) ? `&medicalSpeciality=${speciality}` : ""}`;
    try {
      const response = await axios.get(url);
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { doctors = [] } = result || {};
        setDoctors(doctors);
        if (locationCode) handleFilters(locationCode);
        else handleFilters("all");
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const getSpecialities = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_SPECIALITY}filter-options`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { specialities = [] } = result || {};
        setSpecialities(specialities);
      }
    } catch (error) {
      setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  // const handleFilters = (value) => {
  //   if (value === "all") setFilteredBySpecialityDoctors(doctors);
  //   else {
  //     const filteredBySpecialities = doctors.filter((doctor) => {
  //       const { doctorDetails = {} } = doctor || {};
  //       const { medicalSpecialities = [] } = doctorDetails || {};
  //       // let doctorWithSpeciality = null;
  //       for (let i = 0; i < medicalSpecialities.length; i++) {
  //         const { code = null } = medicalSpecialities[i];
  //         if (code === value) {
  //           return doctor;
  //         }
  //       }
  //     });
  //     setFilteredBySpecialityDoctors(filteredBySpecialities);
  //   }
  // };

  const handleSpecialityChange = (event) => {
    const value = event.target.value;
    getDoctors(selectedLocation, value === "all" ? null : value);
    setSelectedSpeciality(value);
  };

  const handleLocationChange = (event) => {
    const value = event.target.value;
    getDoctors(value, selectedSpeciality);
    setSelectedLocation(value);
  };

  useEffect(() => {
    if (enterpriseCode) {
      getDoctors();
      getSpecialities();
    }
  }, [enterpriseCode]);

  useEffect(() => {
    setFilteredDoctors(doctors);
    setFilteredBySpecialityDoctors(doctors);
  }, [doctors]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
      <SectionLayoutAspire>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "48px" }}>
          <Box
            sx={{
              display: "flex",
              alignItems: { xs: "start", md: "center" },
              // justifyContent: "space-between",
              gap: { xs: "24px", lg: "32px" },
              flexDirection: { xs: "column", md: "row" },
            }}
          >
            <Box
              sx={{
                position: "relative",
                zIndex: 1,
                flex: 1,
                width: "100%",
              }}
              // onBlur={handleOnBlur}
            >
              <Box
                sx={{
                  backgroundColor: "#fff",
                  border: "1px solid rgba(0, 0, 0, 0.23)",
                  padding: "11px 12px",
                  borderRadius: "4px",
                  display: "flex",
                  alignItems: "center",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <InputBase
                  id="doctorSearch"
                  placeholder="Search doctor"
                  value={searchInput}
                  onChange={handleSearchInput}
                  sx={{
                    width: {
                      xs: "100%",
                      // sm: "300px",
                      ".MuiInputBase-input::placeholder": {
                        color: "rgba(0, 0, 0, 0.6)",
                      },
                    },
                  }}
                />
                <SearchIcon />
              </Box>
              {/* {filteredDoctors.length > 0 && (
                <Box
                  sx={{
                    padding: "0 4px",
                    background: "#fff",
                    position: "absolute",
                    width: "100%",
                    maxHeight: "400px",
                    overflowY: "auto",
                    boxShadow: "0 2px 20px rgba(0, 0, 0, .1)",
                  }}
                >
                  <Box>
                    <List
                      subheader={
                        <ListSubheader
                          component="div"
                          id="nested-list-subheader"
                        >
                          Doctors
                        </ListSubheader>
                      }
                    >
                      {filteredDoctors.map((doctor, index) => {
                        const { doctorDetails = {} } = doctor || {};
                        return (
                          <DoctorListItem
                            doctorDetails={doctorDetails}
                            index={index}
                          />
                        );
                      })}
                    </List>
                  </Box>
                </Box>
              )} */}
            </Box>
            <FormControl
              sx={{
                flex: 1,
                width: "100%",
              }}
            >
              <InputLabel id="demo-simple-select-label">
                Filter By Speciality
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={selectedSpeciality}
                label="Filter By Speciality"
                onChange={handleSpecialityChange}
                // input={<SpecialityFilterInput />}
              >
                <MenuItem value={"all"}>All</MenuItem>
                {specialities.map((speciality) => {
                  const { medicalSpeciality: specialityCode = null, displayName = "" } =
                    speciality || {};
                  return (
                    <MenuItem key={specialityCode} value={specialityCode}>
                      {displayName || ""}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
            <FormControl
              sx={{
                flex: 1,
                width: {
                  xs: "100%", // 100% width on mobile
                  md: "30%", // 30% width on desktop
                },
              }}
            >
              <InputLabel id="demo-simple-select-label">
                Filter By Location
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={selectedLocation}
                label="Filter By Location"
                onChange={handleLocationChange}
                // input={<SpecialityFilterInput />}
              >
                <MenuItem value={enterpriseCode}>All</MenuItem>
                {centers.map((center) => {
                  const { code, name = "", area = {} } = center || {};
                  const { name: areaName = "" } = area || {};
                  return (
                    <MenuItem
                      key={code}
                      value={code}
                    >{`${name || ""} - ${areaName || ""}`}</MenuItem>
                  );
                })}
              </Select>
            </FormControl>
            {/* <FormControl>
              <Select
                displayEmpty
                // input={<InputBase variant="outlined" />}
                value="0"
                inputProps={{ "aria-label": "Without label" }}
              >
                <MenuItem disabled value="0">
                  Filter by speciality
                </MenuItem>
                <MenuItem value={"Cardiology"}>Cardiology</MenuItem>
              </Select>
            </FormControl> */}
          </Box>
          {!isLoading && filteredBySpecialityDoctors.length === 0 ? (
            <Typography variant="h4" align="center">
              No Doctors Available
            </Typography>
          ) : (
            <DoctorsList
              doctors={filteredDoctors}
              selectedLocation={selectedLocation}
            />
          )}
          {/* <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Pagination count={10} variant="outlined" shape="rounded" />
          </Box> */}
        </Box>
      </SectionLayoutAspire>
      {/* <FaqsSection /> */}
    </Box>
  );
}
