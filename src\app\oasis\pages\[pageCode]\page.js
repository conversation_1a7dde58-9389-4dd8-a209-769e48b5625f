"use client";

import QuickEnquiry from "@/app/commoncomponents/quickEnquiry";
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PAGE,
  LEAD_SOURCES,
} from "@/constants";
import { alpha, Box, Typography } from "@mui/material";
import axios from "axios";
import Image from "next/image";
import parse from "html-react-parser";
import { getParsedHTML } from "@/app/utils/getParsedHTML";
import { useContext, useEffect, useState } from "react";
import { AppContext } from "@/app/AppContextLayout";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import SectionLayoutOasis from "../../styledComponents/SectionLayoutOasis";

const CustomPage = ({ params }) => {
  const { pageCode = null } = params || {};
  const router = useRouter();
  const theme = useTheme();
  const [pageDetails, setPageDetails] = useState({});
  const { websiteData, setViewSnackbarMain } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  const {
    title = "",
    subtitle = "",
    bannerUrl = "",
    content = "",
    quickEnquiryFormEnabled = "",
  } = pageDetails || {};

  const getCustomPageDetails = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PAGE}?code=${pageCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        const { seoSlug = "" } = result[0] || {};
        if (pageCode !== seoSlug) {
          router.replace(`/pages/${seoSlug}`);
        }
        setPageDetails(result[0] || {});
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getCustomPageDetails();
  }, [enterpriseCode]);

  return (
    <Box>
      <Box
        sx={{
          minHeight: "200px",
          position: "relative",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {bannerUrl ? (
          <Image
            alt={title}
            src={getThumborUrl(bannerUrl)}
            fill
            style={{ objectFit: "cover" }}
          />
        ) : (
          <Box
            sx={{
              height: "100%",
              bgcolor: alpha(theme.palette.primary.main, 0.7),
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
            }}
          ></Box>
        )}
        <SectionLayoutOasis
          style={{
            display: "flex",
            justifyContent: "center",
            background: "transparent",
            height: "100%",
            width: "100%",
            alignItems: "center",
          }}
        >
          <Box
            sx={{
              // width: "fit-content",
              padding: "16px",
              //   background: "rgba(255,255,255,.5)",
              borderRadius: "8px",
              margin: "auto",
              //   border: "1px solid #fff",
              width: { xs: "100%", md: "75%" },
              zIndex: 1,
              color: "#fff",
            }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{
                fontWeight: "400",
                fontSize: { xs: "2rem", sm: "40px", color: "#fff" },
              }}
            >
              {title || ""}
            </Typography>
            <Typography
              variant="h6"
              align="center"
              sx={{ fontWeight: 300, color: "#fff", fontSize: "18px" }}
            >
              {subtitle || ""}
            </Typography>
          </Box>
        </SectionLayoutOasis>
      </Box>
      <SectionLayoutOasis>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", lg: "row" }, // Stack on small screens, side-by-side on large screens
            gap: { xs: "24px", lg: "24px" },
          }}
        >
          <Box
            sx={{
              flex: { lg: "7", xs: "1" }, // 70% width for large screens, full width for small screens
              flexBasis: { lg: "70%", xs: "100%" }, // Explicitly set the percentage width
              maxWidth: { lg: "70%", xs: "100%" }, // Prevent overflow on small screens
            }}
          >
            <div
              className="ck-content"
              dangerouslySetInnerHTML={{ __html: content }} // Render HTML safely
            />
          </Box>
          <Box
            sx={{
              flex: { lg: "3", xs: "1" }, // 30% width for large screens, full width for small screens
              flexBasis: { lg: "30%", xs: "100%" }, // Explicitly set the percentage width
              maxWidth: { lg: "30%", xs: "100%" }, // Prevent overflow on small screens
              position: "sticky", // Sticky positioning for larger screens
              top: "200px", // Adjust how far from the top it sticks
              alignSelf: { xs: "center", lg: "flex-start" }, // Center on small screens, top align on large
              flexShrink: 0, // Prevent shrinking of the box
            }}
          >
            {/*{quickEnquiryFormEnabled && <QuickEnquiry leadSource={LEAD_SOURCES.CUSTOM_PAGE} productCode={pageCode}/>}*/}
            {quickEnquiryFormEnabled && (
              <QuickEnquiry
                pageData={{
                  leadSource: LEAD_SOURCES.CUSTOM_PAGE,
                  productCode: pageCode,
                  pageTitle: title,
                }}
              />
            )}
          </Box>
        </Box>
      </SectionLayoutOasis>
    </Box>
  );
};

export default CustomPage;
