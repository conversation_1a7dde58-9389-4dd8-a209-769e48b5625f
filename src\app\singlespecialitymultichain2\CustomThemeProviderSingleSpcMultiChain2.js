"use client";

import { createTheme, ThemeProvider } from "@mui/material";

const CustomThemeProvider = ({ children, template, fontFamily }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: template.primary_color || template.primaryColor || "#EE4266",
      },
      secondary: {
        main: template.secondary_color || template.secondaryColor || "#E78895",
      },
      text: {
        primary: template.primary_text_color || template.primaryTextColor || "#333333",
        secondary: template.secondary_text_color || template.secondaryTextColor || "#333333",
        paragraph: "rgba(0, 0, 0, 0.54)",
        disabled: "rgba(0, 0, 0, 0.38)",
        hint: "rgba(0, 0, 0, 0.54)",
        white: "#fff",
        black: "#333333",
      },
    },
    typography: {
      fontFamily: 'var(--font-figtree)',
    },
    breakpoints: {
      values: {
        xs: 0,
        sm: 600,
        md: 900,
        lg: 1200,
        xl: 1536,
      },
    },
  });

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

export default CustomThemeProvider;
