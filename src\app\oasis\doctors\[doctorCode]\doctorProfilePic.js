"use client";

import { AppContext } from "@/app/AppContextLayout";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import Image from "next/image";
import React, { useContext } from "react";

const DoctorProfilePic = ({ profilePicture = "" }) => {
  const { desktopView } = useContext(AppContext);
  return desktopView ? (
      <div style={{
          width: "250px",
          height: "250px"
      }}>
          <Image
              alt="doctor-profile"
              src={
                  profilePicture
                      ? getThumborUrl(profilePicture, 250, 250)
                      : "/doctor-profile-icon.png"
              }
              height={250}
              width={250}
              priority
              objectFit={"cover"}
              placeholder={"empty"}
              style={{
                  borderRadius: "100%",
                  outline: "4px solid #fff",
                  outlineOffset: "4px",
              }}
          />
      </div>
  ) : (
    <Image
      alt="doctor-profile"
      src={
        profilePicture
          ? getThumborUrl(profilePicture, 100, 100)
          : "/doctor-profile-icon.png"
      }
      height={100}
      width={100}
      placeholder={"empty"}
      style={{
        borderRadius: "100%",
        outline: "4px solid #fff",
        outlineOffset: "4px",
      }}
    />
  );
};

export default DoctorProfilePic;
