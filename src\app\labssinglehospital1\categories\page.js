import CategoriesClient from './client';
import { getEnterpriseCode } from "../blogs/location/[locationCode]/[blogCode]/layout";
import { getLabCategories } from "@/api/harbor.service";

export default async function CategoriesPage({ searchParams }) {
  // Get page from search params
  const page = searchParams?.page ? parseInt(searchParams.page) : 1;
  const perPage = 12; // Items per page

  const enterpriseCode = await getEnterpriseCode();
  const categories = await getLabCategories(enterpriseCode, { page, perPage });

  return <CategoriesClient
    categories={categories}
    currentPage={page}
    itemsPerPage={perPage}
  />;
}
