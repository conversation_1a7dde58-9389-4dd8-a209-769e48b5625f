"use client"
import React, { useContext } from "react";
import parse from "html-react-parser";

import { Box, Button, Typography } from "@mui/material";

import useStyles from "../../../styles";
import homepageStyles from "../styles";
import { useRouter } from "next/navigation";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import {AppContext} from "@/app/AppContextLayout";
const BlogsSection = ({ blogs = [] }) => {
  const commonClasses = useStyles();
  const classes = homepageStyles();
  const router = useRouter();
  const { mobileView } = useContext(AppContext);

  const handleReadMoreBlog = (blog) => {
    const { title = "", seoSlug = "" } = blog || {};
    router.push(`/blogs/${seoSlug}`);
  };

  return (
    <Box
      id="blogs"
      className={`${commonClasses.layoutPadding} ${classes.blogsSection}`}
      sx={{
          gap: {xs: "32px", md: "48px"},
      }}
      style={{
        // backgroundColor: THEME_WEBSITE_TERNARY_BG_COLOR,
        display: "flex",
        flexDirection: "column",
        position: "relative",
      }}
    >
      {/* <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1440 320"
        className={classes.blogsSectionWaves}
        style={{
          position: "absolute",
          top: 0,
          width: "100vw",
          left: 0,
          zIndex: 0,
          // transform: 'rotateY(180deg)'
        }}
      >
        <path
          // fill={alpha('#435B66', 0.2)}
          fill-opacity="0.2"
          d="M0,288L9.6,245.3C19.2,203,38,117,58,112C76.8,107,96,181,115,213.3C134.4,245,154,235,173,202.7C192,171,211,117,230,96C249.6,75,269,85,288,117.3C307.2,149,326,203,346,234.7C364.8,267,384,277,403,245.3C422.4,213,442,139,461,144C480,149,499,235,518,266.7C537.6,299,557,277,576,261.3C595.2,245,614,235,634,213.3C652.8,192,672,160,691,149.3C710.4,139,730,149,749,176C768,203,787,245,806,224C825.6,203,845,117,864,80C883.2,43,902,53,922,85.3C940.8,117,960,171,979,165.3C998.4,160,1018,96,1037,64C1056,32,1075,32,1094,48C1113.6,64,1133,96,1152,122.7C1171.2,149,1190,171,1210,192C1228.8,213,1248,235,1267,234.7C1286.4,235,1306,213,1325,218.7C1344,224,1363,256,1382,250.7C1401.6,245,1421,203,1430,181.3L1440,160L1440,0L1430.4,0C1420.8,0,1402,0,1382,0C1363.2,0,1344,0,1325,0C1305.6,0,1286,0,1267,0C1248,0,1229,0,1210,0C1190.4,0,1171,0,1152,0C1132.8,0,1114,0,1094,0C1075.2,0,1056,0,1037,0C1017.6,0,998,0,979,0C960,0,941,0,922,0C902.4,0,883,0,864,0C844.8,0,826,0,806,0C787.2,0,768,0,749,0C729.6,0,710,0,691,0C672,0,653,0,634,0C614.4,0,595,0,576,0C556.8,0,538,0,518,0C499.2,0,480,0,461,0C441.6,0,422,0,403,0C384,0,365,0,346,0C326.4,0,307,0,288,0C268.8,0,250,0,230,0C211.2,0,192,0,173,0C153.6,0,134,0,115,0C96,0,77,0,58,0C38.4,0,19,0,10,0L0,0Z"
        ></path>
      </svg>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 1440 320"
        className={classes.blogsSectionWaves}
        style={{
          position: "absolute",
          bottom: 0,
          width: "100vw",
          left: 0,
          zIndex: 0,
          // transform: 'rotateY(180deg)'
        }}
      >
        <path
          fill="#6059FF"
          fill-opacity="0.2"
          d="M0,288L9.6,245.3C19.2,203,38,117,58,112C76.8,107,96,181,115,213.3C134.4,245,154,235,173,202.7C192,171,211,117,230,96C249.6,75,269,85,288,117.3C307.2,149,326,203,346,234.7C364.8,267,384,277,403,245.3C422.4,213,442,139,461,144C480,149,499,235,518,266.7C537.6,299,557,277,576,261.3C595.2,245,614,235,634,213.3C652.8,192,672,160,691,149.3C710.4,139,730,149,749,176C768,203,787,245,806,224C825.6,203,845,117,864,80C883.2,43,902,53,922,85.3C940.8,117,960,171,979,165.3C998.4,160,1018,96,1037,64C1056,32,1075,32,1094,48C1113.6,64,1133,96,1152,122.7C1171.2,149,1190,171,1210,192C1228.8,213,1248,235,1267,234.7C1286.4,235,1306,213,1325,218.7C1344,224,1363,256,1382,250.7C1401.6,245,1421,203,1430,181.3L1440,160L1440,320L1430.4,320C1420.8,320,1402,320,1382,320C1363.2,320,1344,320,1325,320C1305.6,320,1286,320,1267,320C1248,320,1229,320,1210,320C1190.4,320,1171,320,1152,320C1132.8,320,1114,320,1094,320C1075.2,320,1056,320,1037,320C1017.6,320,998,320,979,320C960,320,941,320,922,320C902.4,320,883,320,864,320C844.8,320,826,320,806,320C787.2,320,768,320,749,320C729.6,320,710,320,691,320C672,320,653,320,634,320C614.4,320,595,320,576,320C556.8,320,538,320,518,320C499.2,320,480,320,461,320C441.6,320,422,320,403,320C384,320,365,320,346,320C326.4,320,307,320,288,320C268.8,320,250,320,230,320C211.2,320,192,320,173,320C153.6,320,134,320,115,320C96,320,77,320,58,320C38.4,320,19,320,10,320L0,320Z"
        ></path>
      </svg> */}
      <Typography
        variant="h3"
        className={`${classes.homepageSectionSubHeading} ${classes.sectionHeading}`}
        align="center"
        style={{ zIndex: 1 , color: "#333333"}}
      >
        Latest Blogs
      </Typography>
      <Box>
        <Swiper
          spaceBetween={36}
          breakpoints={{
            0: {
              slidesPerView: 1,
            },
            600: {
              slidesPerView: 2,
            },
            1200: {
              slidesPerView: 3,
            },
          }}
          navigation={true}
          modules={[Navigation]}
          className="mySwiper"
          style={{
            "--swiper-navigation-color": "white",
            "--swiper-navigation-size": "24px",
            "--swiper-navigation-sides-offset": "10px",
          }}
        >
          {blogs.map((blog, index) => {
            const {
              content = "",
              imageUrl: blogImage = "",
              title: blogTitle = "",
            } = blog || {};
            return (
              <SwiperSlide>
                <Box
                  key={index}
                  className={classes.blogsSectionItem}
                  style={{
                    backgroundImage: `url(${getThumborUrl(blogImage, 0, 0)})`,
                  }}
                  onClick={() => handleReadMoreBlog(blog)}
                >
                  <Box
                    className={`${classes.blogsSectionItemGradient} ${
                      !blogImage ? classes.blogSectionItemDarkGradient : ""
                    }`}
                  ></Box>
                  <Box className={classes.blogSectionItemContentBox}>
                    <Typography className={classes.blogsSectionCarousalItem}>
                      {blogTitle || ""}
                    </Typography>
                    {/*<Typography*/}
                    {/*  variant="body1"*/}
                    {/*  noWrap*/}
                    {/*  className={`${classes.blogSectionItemContent}`}*/}
                    {/*>*/}
                    {/*  {parse(content || "") || ""}*/}
                    {/*</Typography>*/}
                    <Button
                      variant="outlined"
                      className={classes.blogSectionItemReadMore}
                      onClick={() => handleReadMoreBlog(blog)}
                    >
                      Read More
                    </Button>
                  </Box>
                </Box>
              </SwiperSlide>
            );
          })}
        </Swiper>
      </Box>
    </Box>
  );
};

export default BlogsSection;
