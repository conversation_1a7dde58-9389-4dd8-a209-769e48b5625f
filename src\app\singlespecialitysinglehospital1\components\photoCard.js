import { getThumborUrl } from "../../utils/getThumborUrl";
import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

const PhotoCard = ({ code, imgUrl }) => {
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <React.Fragment>
      <div key={code} onClick={handleClickOpen} style={{ cursor: "pointer" }}>
        <div
          style={{
            display: "flex",
            padding: "8px",
            justifyContent: "center",
            height: "300px",
          }}
        >
          <img
            alt="slider1"
            src={getThumborUrl(imgUrl)}
            style={{
              height: "100%",
              width: "100%",
              objectFit: "cover",
              borderRadius: "8px",
            }}
          />
        </div>
      </div>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{
          ".MuiDialog-paper": {
            maxHeight: "calc(100vh - 64px)",
            maxWidth: "calc(100vw - 64px)",
            padding: 0,
            borderRadius: "8px",
            overflow: "hidden"
          },
        }}
      >
        <img
          alt="slider1"
          src={getThumborUrl(imgUrl)}
          style={{
            height: "100%",
            width: "100%",
            objectFit: "contain",
            borderRadius: "8px",
            objectPosition: "center"
          }}
        />
      </Dialog>
    </React.Fragment>
  );
};

export default PhotoCard;
