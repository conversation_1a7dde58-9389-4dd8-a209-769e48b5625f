'use client';

import { useLocalStorage } from '../redux/hooks/useLocalStorage';
import { selectCartItems } from '../redux/slices/cartSlice';

/**
 * Component that persists cart state to localStorage
 * This is a "headless" component that doesn't render anything
 */
const CartPersistence = () => {
  // Use our custom hook to persist cart items to localStorage
  useLocalStorage('cart', state => state.cart);
  
  // This component doesn't render anything
  return null;
};

export default CartPersistence;
