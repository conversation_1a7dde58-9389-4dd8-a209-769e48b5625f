"use client";
import { useEffect, useRef, useState } from "react";
import { Box, Typography, Container, useTheme } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import VideosFilters from "./VideosFilters";

// const treatments = [
//     "Laser Hair Removal",
//     "Skin Whitening Treatment",
//     "Hair Loss Treatment",
//     "Hair Transplant",
//     "Acne Treatment",
//     "Scar Removal",
//     "Skin Pigmentation",
//     "Dark Circles",
//   ];

const getYouTubeVideoId = (url) => {
  try {
    // If it's already just an ID (11 characters)
    if (url.length === 11 && !url.includes("/")) {
      return url;
    }

    // Try to extract ID from full URL
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : null;
  } catch (error) {
    console.error("Error extracting YouTube ID:", error);
    return null;
  }
};

const formatVideoUrl = (url) => {
  const videoId = getYouTubeVideoId(url);
  return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
};

const getThumbnailUrl = (url) => {
  const videoId = getYouTubeVideoId(url);
  return videoId
    ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
    : null;
};
const VideosClient = ({ videos, tags, headings = [] }) => {
  const [filteredVideos, setFilteredVideos] = useState([]);
  const [selectedTag, setSelectedTag] = useState(null);
  // const [activeTreatment, setActiveTreatment] = useState(treatments[0]);
  const theme = useTheme();
  const [playingVideoId, setPlayingVideoId] = useState(null);
  const [autoplay, setAutoplay] = useState(true);
  const swiperRef = useRef(null);

  useEffect(() => {
    if (selectedTag) {
      const filtered = videos.filter((video) =>
        video.tags.some((tag) => tag.name === selectedTag)
      );
      setFilteredVideos(filtered);
    } else {
      setFilteredVideos(videos);
    }
  }, [selectedTag, videos]);

  const handleVideoClick = (videoId) => {
    if (playingVideoId === videoId) {
      setPlayingVideoId(null); // Pause the video if it's already playing
      setAutoplay(true); // Enable autoplay when video is paused
      if (swiperRef.current) swiperRef.current.swiper.autoplay.start(); // Restart autoplay
    } else {
      setPlayingVideoId(videoId); // Start playing the clicked video
      setAutoplay(false); // Disable autoplay to stop swiping
      if (swiperRef.current) swiperRef.current.swiper.autoplay.stop(); // Stop autoplay
    }
  };

  const heading = headings[0]?.heading;
  //    const subHeading = headings[0]?.subHeading

  return (
    <Box
      sx={{
        background: `linear-gradient(to bottom, ${theme.palette.primary.main}05, ${theme.palette.primary.main}10)`,
        py: "32px",
        overflow: "hidden",
        position: "relative",
      }}
    >
      {/* <StarPattern/> */}
      <Container maxWidth="lg">
        <Typography
          variant="h4"
          align="center"
          fontWeight={400}
          sx={{
            mb: 2,
            fontSize: {
              xs: "24px",
              md: "36px",
            },
            color: "text.black",
          }}
        >
          {heading || "Health Related Videos"}
        </Typography>
        {tags && tags.length > 0 && (
          <VideosFilters
            // aggregatedTags={aggregatedTags}
            tags={tags}
            onTagSelect={(tag) => setSelectedTag(tag)}
          />
        )}

        <Box sx={{ mt: 4 }}>
          <Swiper
            ref={swiperRef}
            style={{ padding: "5px 5px", paddingBottom: "50px" }}
            modules={[Autoplay, Pagination]}
            loop={false}
            spaceBetween={18}
            slidesPerView={1}
            pagination={{ dynamicBullets: true, clickable: true }}
            autoplay={
              autoplay ? { delay: 3000, disableOnInteraction: false } : false
            }
            breakpoints={{
              768: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
            }}
          >
            {filteredVideos.map((video) => (
              <SwiperSlide key={video.id}>
                <Box
                  sx={{
                    height: "100%",
                    boxShadow: 2,
                    borderRadius: "10px",
                    overflow: "hidden",
                    position: "relative",
                  }}
                >
                  {playingVideoId === video.id ? (
                    <Box
                      component="iframe"
                      src={`${formatVideoUrl(video.video_url)}?autoplay=1`}
                      title={video.title}
                      sx={{
                        width: "100%",
                        height: 240,
                        border: 0,
                      }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  ) : (
                    <Box
                      onClick={() => handleVideoClick(video.id)}
                      sx={{
                        position: "relative",
                        cursor: "pointer",
                        "&:hover": {
                          "& .play-overlay": {
                            opacity: 1,
                          },
                        },
                      }}
                    >
                      <Box
                        component="img"
                        src={
                          getThumbnailUrl(video.video_url) || video.image_url
                        }
                        alt={video.title}
                        sx={{
                          width: "100%",
                          height: 240,
                          transition: "transform 0.3s ease-in-out",
                          objectFit: "cover",
                          "&:hover": { transform: "scale(1.02)" },
                        }}
                        onError={(e) => {
                          // Fallback to default image if thumbnail fails to load
                          e.currentTarget.src = video.image_url;
                        }}
                      />
                      <Box
                        className="play-overlay"
                        sx={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          background: "rgba(0,0,0,0.3)",
                          opacity: 0,
                          transition: "opacity 0.3s ease",
                        }}
                      >
                        <Box
                          sx={{
                            width: 60,
                            height: 60,
                            borderRadius: "50%",
                            background: "rgba(255,255,255,0.9)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Box
                            sx={{
                              width: 0,
                              height: 0,
                              borderTop: "15px solid transparent",
                              borderBottom: "15px solid transparent",
                              borderLeft: "25px solid #000",
                              marginLeft: "5px",
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  )}
                  <Box sx={{ p: 1 }}>
                    <Typography
                      variant="h6"
                      fontSize="18px"
                      sx={{
                        display: "-webkit-box",
                        WebkitBoxOrient: "vertical",
                        WebkitLineClamp: 2,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        color: "text.black",
                      }}
                    >
                      {video.title || ""}
                    </Typography>
                  </Box>
                </Box>
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
      </Container>
    </Box>
  );
};

export default VideosClient;
