'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Collapse
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

const TestAboutSection = ({ testData }) => {
  const [expanded, setExpanded] = useState(false);
  const [truncatedHtml, setTruncatedHtml] = useState('');
  const hasLongContent = testData.description && testData.description.length > 420;
  const hasAdditionalInfo = testData.locations || testData.additionalInfo;
  const shouldShowButton = hasLongContent || hasAdditionalInfo;

  // Function to safely truncate HTML content
  useEffect(() => {
    if (testData.description && testData.description.length > 420) {
      // Simple truncation - this works for basic HTML but might break complex HTML
      // For production, consider using a proper HTML parser library
      setTruncatedHtml(testData.description.substring(0, 420) + '...');
    }
  }, [testData.description]);

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: '12px', border: '1px solid #e0e0e0' }}>
      <Typography variant="h6" fontWeight="medium" sx={{ mb: 3 }} color="text.black">
        About
      </Typography>

      <Box sx={{ color: "text.black" }}>
        {testData.description ? (
          <Box sx={{ mb: 2 }}>
            {/* Show either truncated or full content based on expanded state */}
            <div
              className="ck-content"
              style={{
                wordWrap: 'break-word',
                overflowWrap: 'break-word',
                wordBreak: 'break-word',
                maxWidth: '100%'
              }}
              dangerouslySetInnerHTML={{
                __html: (!expanded && hasLongContent) ? truncatedHtml : testData.description
              }}
            />
          </Box>
        ) : (
          <Typography variant="body2" paragraph sx={{ mb: 1 }}>
            {testData.alternativeNames || 'No detailed description available for this test.'}
          </Typography>
        )}
        {/* Read More/Less button */}
        {shouldShowButton && (
          <Button
            onClick={toggleExpand}
            endIcon={expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            sx={{
              textTransform: 'none',
              color: 'primary.main',
              fontWeight: 'medium',
              p: 0,
              mt: 1,
              '&:hover': {
                backgroundColor: 'transparent',
              }
            }}
          >
            {expanded ? 'Read Less' : 'Read More'}
          </Button>
        )}
      </Box>
    </Paper>
  );
};

export default TestAboutSection;
