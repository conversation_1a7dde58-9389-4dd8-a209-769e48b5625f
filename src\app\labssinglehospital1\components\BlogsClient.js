'use client'
import React from 'react'
import { useState, useEffect, useRef } from "react";
import { ArrowRight } from "@mui/icons-material";
import {
  Box,
  Typography,
  CardContent,
  CardActions,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { Swiper, SwiperSlide } from "swiper/react";
import {Autoplay, Pagination} from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { useRouter } from "next/navigation";
const useObserver = () => {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );

    if (ref.current) observer.observe(ref.current);

    return () => observer.disconnect();
  }, []);

  return [ref, isVisible];
};

const StyledBox = styled(Box)(({ theme }) => ({
  cursor: "pointer",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  transition: "transform 0.2s ease, box-shadow 0.2s ease",
  "&:hover": {
    transform: "translateY(-2px)",
    boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
    "& .arrow-icon": {
      transform: "translateX(4px)",
    },
    "& .read-more-text": {
      color: theme.palette.primary.main,
    },
  },
  borderRadius: "12px",
  overflow: "hidden",
  backgroundColor: "white",
  boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
}));

const StyledMedia = styled(Box)(({ theme }) => ({
  cursor: "pointer",
  paddingTop: "50%", // Reduced height
  position: "relative",
  backgroundSize: "cover",
  backgroundPosition: "center",
  "&::after": {
    content: '""',
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "20%", // Reduced gradient
    background:
      "linear-gradient(to top, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 100%)",
  },
}));

const StyledButton = styled(Typography)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: "5px",
  cursor: "pointer",
  transition: "all 0.3s ease",
  "& .read-more-text": {
    fontSize: "0.875rem",
    color: theme.palette.primary.main,
    transition: "color 0.3s ease",
  },
  "& .arrow-icon": {
    fontSize: "1.2rem",
    transition: "transform 0.3s ease",
    color: theme.palette.primary.main,
  },
}));

const BlogsClient = ({blogs =[]}) => {
  const [cardsRef, cardsVisible] = useObserver();
  const router = useRouter();
  return (
    <Box
          ref={cardsRef}
          sx={{
            opacity: cardsVisible ? 1 : 0,
            transform: cardsVisible ? "translateY(0)" : "translateY(20px)",
            transition: "opacity 0.6s ease-out, transform 0.6s ease-out",
          }}
        >
          <Swiper
           modules={[Pagination, Autoplay]}
            spaceBetween={20}
            slidesPerView={1}
            loop={true}
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 16,
              },
              600: {
                slidesPerView: 2,
                spaceBetween: 16,
              },
              960: {
                slidesPerView: 3,
                spaceBetween: 20,
              },
              1280: {
                slidesPerView: 4,
                spaceBetween: 20,
              },
            }}
            style={{ padding: "10px 5px 40px 5px" }}
          >

              {blogs.map((blog, index) => {
                  const {
                      title = "",
                      imageUrl = "",
                      seoSlug,
                  } = blog;
                return (
                  <SwiperSlide>
                  <Box
                    key={index}
                    onClick={() => {
                      if (seoSlug) router.push(`/blogs/${seoSlug}`);
                    }}
                  >
                    <StyledBox>
                      <StyledMedia
                        sx={{
                          backgroundImage: `url(${imageUrl || "/blogs-default.avif"})`,
                        }}
                        role="img"
                        aria-label={`Blog image for ${title || 'Blog post'}`}
                      />

                      <CardContent sx={{ flexGrow: 1, p: 2, pb: 0 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            color: "text.black",
                            fontWeight: 500,
                            fontSize: { xs: "0.9rem", md: "1.2rem" },
                            lineHeight: 1.3,
                            WebkitLineClamp: 2,
                            display: "-webkit-box",
                            overflow: "hidden",
                            WebkitBoxOrient: "vertical",
                            height: "2.6em",
                            mb: 1
                          }}
                        >
                          {title}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2, pt: 1 }}>
                        <StyledButton>
                          <span className="read-more-text" style={{ fontSize: "0.8rem",color: "primary.main" }}>
                            Continue Reading
                          </span>
                          <ArrowRight className="arrow-icon" style={{ fontSize: "1rem", color: "primary.main" }} />
                        </StyledButton>
                      </CardActions>
                    </StyledBox>
                  </Box>
            </SwiperSlide>
                );
              })}
          </Swiper>
        </Box>
  )
}

export default BlogsClient