"use client";

import { Box, Typography } from "@mui/material";
import axios from "axios";
import Image from "next/image";
import { useContext, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  API_ENDPOINT_BLOGS,
  API_SECTION_API,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_VERSION,
  HARBOR_API_DOCFYN_DOMAIN, LEAD_SOURCES, PAGE_NAMES,
} from "@/constants";
import {getThumborUrl} from "@/app/utils/getThumborUrl";

import SectionLayout from "@/app/labssinglehospital1/components/SectionLayout";
import QuickEnquirySingleSplMultiCain2 from "@/app/labssinglehospital1/components/quickEnquiry";
import { AppContext } from "@/app/AppContextLayout";
import styles from "./blogDetail.module.css";
export default function BlogDetail({ params }) {
  const { id: blogCode = null } = params || {};
  const router = useRouter();
  const [blogDetail, setBlogDetail] = useState({});
  const { websiteData } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null } = websiteData || {};
  const { title = "", content = "", imageUrl = "" } = blogDetail || {};

  const getBlogDetails = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?code=${blogCode}`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        const { seoSlug = "" } = result[0] || {};
        if (blogCode !== seoSlug) {
          router.replace(`/blogs/${seoSlug}`);
        }
        setBlogDetail(result[0] || {});
      }
    } catch (error) {
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    }
  };

  useEffect(() => {
    if (enterpriseCode) getBlogDetails();
  }, [enterpriseCode]);

  // Enhanced effect to handle tables after content is loaded
  useEffect(() => {
    if (content) {
      // Process tables after content is rendered with a more robust approach
      const processTablesWithRetry = (retryCount = 0, maxRetries = 3) => {
        const tables = document.querySelectorAll('.ck-content table');

        if (tables.length === 0 && retryCount < maxRetries) {
          // If tables aren't found yet, retry after a delay
          setTimeout(() => processTablesWithRetry(retryCount + 1), 300);
          return;
        }

        tables.forEach(table => {
          // Get accurate measurements
          const containerWidth = table.parentElement.clientWidth;
          const tableWidth = table.scrollWidth;

          // Check if table has many columns (more than 4)
          const columnCount = table.querySelector('tr')?.children?.length || 0;
          const hasManyColumns = columnCount > 4;

          // Determine if table needs horizontal scrolling
          const needsScrolling = tableWidth > containerWidth || hasManyColumns;

          if (needsScrolling) {
            table.classList.add('wide-table');
          } else {
            table.classList.remove('wide-table');
          }

          // Add event listener for window resize to recheck table width
          const handleResize = () => {
            const updatedContainerWidth = table.parentElement.clientWidth;
            const updatedTableWidth = table.scrollWidth;

            if (updatedTableWidth > updatedContainerWidth) {
              table.classList.add('wide-table');
            } else if (!hasManyColumns) {
              table.classList.remove('wide-table');
            }
          };

          window.addEventListener('resize', handleResize);

          // Clean up event listener on component unmount
          return () => {
            window.removeEventListener('resize', handleResize);
          };
        });
      };

      // Start processing tables with a small initial delay
      setTimeout(() => processTablesWithRetry(), 300);
    }
  }, [content]);

  return (
    <SectionLayout>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "48px", }}>
        {imageUrl && (
            <Box
                sx={{
                  minHeight: { xs: "240px", sm: "320px", md: "400px" },
                  position: "relative",
                }}
            >
              <Image
                  alt="blog-banner"
                  fill
                  src={getThumborUrl(imageUrl)}
                  style={{
                    height: "100%",
                    width: "100%",
                    position: "absolute",
                    top: 0,
                    left: 0,
                    objectFit: "contain",
                    borderRadius: "10px",
                  }}
              />
            </Box>
        )}
        <Typography
            variant="h3"
            align="center"
            sx={{
              fontWeight: "500",
              color: "primary.main",
              fontSize: { xs: "1.75rem", sm: "40px" },
            }}
        >
          {title || ""}
        </Typography>
        <Box
           sx={{
            display: "flex",
            // gridTemplateColumns: quickEnquiryFormEnabled
            //   ? { xs: "1fr", md: "1fr 1fr" }
            //   : "1fr",
            flexDirection: { xs: "column", lg: "row" },
            gap: { xs: "24px", lg: "24px" },
          }}
        >
          <Box
            sx={{
              flex: { lg: "7", xs: "1" }, // 70% width for large screens, full width for small screens
              flexBasis: { lg: "70%", xs: "100%" }, // Explicitly set the percentage width
              maxWidth: { lg: "70%", xs: "100%" }, // Prevent overflow on small screens
            }}
            className={styles.blogContent}
          >
            <div
              className="ck-content"
              dangerouslySetInnerHTML={{ __html: content }} // Render HTML safely
            />
          </Box>
          <Box
           sx={{
            flex: { lg: "3", xs: "1" }, // 30% width for large screens, full width for small screens
            flexBasis: { lg: "30%", xs: "100%" }, // Explicitly set the percentage width
            maxWidth: { lg: "30%", xs: "100%" }, // Prevent overflow on small screens
            position: "sticky", // Sticky positioning for larger screens
            top: "200px", // Adjust how far from the top it sticks
            alignSelf: { xs: "center", lg: "flex-start" }, // Center on small screens, top align on large
            flexShrink: 0, // Prevent shrinking of the box
          }}>

          <QuickEnquirySingleSplMultiCain2 pageData={{leadSource: LEAD_SOURCES.BLOG_DETAIL_PAGE, productCode: blogCode, pageTitle: title}}/>
          </Box>
          {/* <Box
              sx={{
                position: "sticky",
                top: "200px", // Adjust to control how far from the top it sticks
                alignSelf: "start", // Ensures it doesn't stretch vertically in the grid
              }}
          >
          </Box> */}
        </Box>
      </Box>
    </SectionLayout>
  );
}
