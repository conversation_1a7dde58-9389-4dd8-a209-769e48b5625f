"use client"
import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
const BlogPage = () => {

    const [content, setContent] = useState(''); // State to hold CKEditor content
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [blog, setBlog] = useState(null);

    useEffect(() => {
        const fetchBlog = async () => {
            try {
                setLoading(true)
                const response = await fetch(`https://staging-richtext-poc.mydocsite.com/api/blogs/1/`);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const blogData = await response.json();
                setBlog(blogData);
                setContent(blogData.content); // Set initial content for CKEditor
                setLoading(false)
            } catch (error) {
                setError(error.message);
            }
        };

        fetchBlog();
    }, []);

    const handlePostContent = async () => {
        try {
            setLoading(true);
            const response = await fetch(`https://staging-richtext-poc.mydocsite.com/api/blogs/1/content/`, {
                cache: "no-store",
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({content}), // Use the state variable directly
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const responseData = await response.json();
            alert('Content updated successfully!');
        } catch (error) {
            console.error('Error posting blog content:', error);
            alert('Failed to update content.');
        } finally {
            setLoading(false);
        }
    };

    if (error) {
        return <div>Error: {error}</div>;
    }

    if (loading) {
        return <div>Loading...</div>;
    }

    return (
        <SectionLayout>
            <div
                className="ck-content"
                dangerouslySetInnerHTML={{__html: content}} // Render HTML safely
            />
        </SectionLayout>
    );
};

import {useEffect, useState} from 'react';

export default BlogPage;