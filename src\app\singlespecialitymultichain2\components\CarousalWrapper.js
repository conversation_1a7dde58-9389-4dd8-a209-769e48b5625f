import React from 'react'
import Carousal from '../Carousal'
import { getWebsiteHost } from '@/app/utils/serverOnly/serverUtils'
import { HOME_WIDGET_TYPE } from '@/constants'
import { getHomeComponentsData } from '@/api/harbor.service'

const getBanners = async () => {
    const domainName = getWebsiteHost()
    try {
        const data = await getHomeComponentsData(
            { domainName: domainName },
            HOME_WIDGET_TYPE.BANNER
          );
          return data?.code === 200 ? data.result.banners || [] : [];
    } catch (error) {
        console.log("error fethcing banner", error)
    }
}

const CarousalWrapper = async () => {
    const banners = await getBanners()
    if(!banners.length) return null
  return (
    <Carousal banners={banners}/>
  )
}

export default CarousalWrapper