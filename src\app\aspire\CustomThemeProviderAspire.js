"use client";

import { ThemeProvider } from "@emotion/react";
import { createTheme } from "@mui/material";
const CustomThemeProvider = ({ children, template, fontFamily }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: template.primary_color || template.primaryColor || "#EE4266",
      },
      secondary: {
        main: template.secondary_color || template.secondaryColor || "#E78895",
      },
      text: {
        primary: template.primary_text_color || template.primaryTextColor || "#000000"
      }
    },
    typography: {
      fontFamily: 'var(--font-inter)',
    },
    breakpoints: {
      values: {
        xs: 0,
        sm: 600,
        md: 900,
        lg: 1200,
        xl: 1536,
      },
    },
  });

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

export default CustomThemeProvider;
