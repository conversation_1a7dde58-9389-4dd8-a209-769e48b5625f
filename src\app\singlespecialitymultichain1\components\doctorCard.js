"use client";

import { useTheme } from "@emotion/react";
import {
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import { alpha } from "@mui/material/styles";
import Image from "next/image";
import WorkHistoryOutlinedIcon from "@mui/icons-material/WorkHistoryOutlined";
import CurrencyRupeeIcon from "@mui/icons-material/CurrencyRupee";
import { useRouter } from "next/navigation";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Slide from "@mui/material/Slide";
import { Fragment, forwardRef, useContext, useEffect, useState } from "react";
import PlaceIcon from "@mui/icons-material/Place";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import OutlinedButton from "@/app/singlespecialitymultichain1/styledComponents/outlinedButton";
import PrimaryButton from "@/app/singlespecialitymultichain1/styledComponents/primaryButton";
import {AppContext} from "@/app/AppContextLayout";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props}  />;
});

const DoctorCard = ({
  doctorDetails = {},
  id = null,
  locationCode = null,
  selectedLocation = null,
}) => {
  const theme = useTheme();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [selectedCenter, setSelectedCenter] = useState(null);
  const { websiteData = {} } = useContext(AppContext);
  const { enterprise_code: chainEnterpriseCode = null } = websiteData || {};

  const {
    code = null,
    name = "",
    profilePicture = "",
    medicalSpecialities = [],
    additionalDetails = null,
    seoSlug = "",
    centers = [],
  } = doctorDetails || {};
  const { domain_slug: centerSlug = "" } = selectedCenter || {};

  const { workExperience = "", consultationFee = "" } = additionalDetails || {};

  const handleDoctorCardClick = () => {
    if (
      locationCode ||
      (selectedLocation && selectedLocation !== chainEnterpriseCode)
    )
      router.push(`/doctors/${locationCode || centerSlug}/${seoSlug}`);
    else setOpen(true);
  };

  const handleCentersModal = () => setOpen(false);

  const handleCenterChange = (event) => {
    const value = event.target.value;
    const selectedValue = centers.find(
      (center) => center.code === event.target.value
    );
    setSelectedCenter(selectedValue);
  };

  const handleRedirection = () => {
    router.push(`/doctors/${centerSlug}/${seoSlug}`);
  };

  useEffect(() => {
    if (centers.length > 0) {
      setSelectedCenter(centers[0]);
    }
  }, [centers]);

  useEffect(() => {
    if (selectedLocation) {
      const selectedValue = centers.find(
        (center) => center.code === selectedLocation
      );
      setSelectedCenter(selectedValue);
    }
  }, [selectedLocation]);

  return (
    <>
      <Box
        id={id}
        sx={{
          boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderRadius: "8px",
          cursor: "pointer",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          minHeight: "200px",
        }}
        onClick={handleDoctorCardClick}
      >
        <Box sx={{ padding: "20px 16px", display: "flex", gap: "16px" }}>
          <Box
            sx={{
              height: "64px",
              width: "64px",
              borderRadius: "50px",
              overflow: "hidden",
            }}
          >
            <Image
              alt="profilePic"
              src={
                profilePicture
                  ? getThumborUrl(profilePicture, 64, 64)
                  : "/doctor-profile-icon.png"
              }
              height={64}
              width={64}
              style={{
                objectFit: "cover",
                objectPosition: "center",
                border: "1px solid #dfe2f2",
                borderRadius: "100%",
              }}
            />
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
              {name || ""}
            </Typography>
            <Typography
              variant="subtitle2"
              sx={{
                color: "#7c7c7c",
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: "2",
                WebkitBoxOrient: "vertical",
              }}
            >
              {medicalSpecialities?.length > 0 &&
                `Specialised in ${medicalSpecialities.map(
                  (speciality, index) => {
                    const { name = "" } = speciality || {};
                    return `${name} ${
                      index < medicalSpecialities.length - 1 ? "|" : ""
                    }`;
                  }
                )}`}
            </Typography>
            {centers.length > 0 && (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                  mt: 1,
                }}
              >
                <PlaceIcon sx={{ color: "#7c7c7c", fontSize: "1.2rem" }} />
                <Typography
                  variant="subtitle2"
                  sx={{
                    color: "#7c7c7c",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    display: "-webkit-box",
                    WebkitLineClamp: "2",
                    WebkitBoxOrient: "vertical",
                  }}
                >
                  {centers.length > 0 &&
                    centers.map((center, index) => {
                      const { area = {} } = center || {};
                      const { name: areaName = "" } = area || {};
                      return index > 0 ? `, ${areaName}` : areaName;
                    })}
                </Typography>
              </Box>
            )}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                gap: "16px",
                mt: 2,
              }}
            >
              {Boolean(workExperience) && (
                <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <WorkHistoryOutlinedIcon />
                  <Typography variant="subtitle2">
                    {workExperience} years
                  </Typography>
                </Box>
              )}
              {Boolean(consultationFee) && (
                <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <CurrencyRupeeIcon />
                  <Typography variant="subtitle2">{consultationFee}</Typography>
                </Box>
              )}
            </Box>
            <Box></Box>
          </Box>
        </Box>
        <Box sx={{ display: "flex" }}>
          <OutlinedButton
            id="doctorViewProfile"
            sx={{ width: "50%", borderRadius: "0", textTransform: "none" }}
          >
            View full profile
          </OutlinedButton>
          <PrimaryButton
            id="doctorBookAppointment"
            sx={{ width: "50%", borderRadius: "0", textTransform: "none" }}
          >
            Book appointment
          </PrimaryButton>
        </Box>
      </Box>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        onClose={handleCentersModal}
        keepMounted={false}
        aria-describedby="alert-dialog-slide-description"
        sx={{ ".MuiDialog-paper": { width: { xs: "100%", sm: "600px" } } }}
      >
        <DialogTitle>Select Location</DialogTitle>
        <DialogContent>
          <FormControl sx={{ padding: "8px 16px" }}>
            <RadioGroup
              aria-labelledby="demo-radio-buttons-group-label"
              name="radio-buttons-group"
              value={selectedCenter?.code}
              onChange={handleCenterChange}
            >
              {centers.map((center) => {
                const { code = null, name = "", area = {} } = center || {};
                const { name: areaName = "" } = area || {};
                return (
                  <FormControlLabel
                    value={code}
                    control={<Radio />}
                    label={`${name || ""} - ${areaName}`}
                  />
                );
              })}
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <OutlinedButton onClick={handleCentersModal}>Close</OutlinedButton>
          <PrimaryButton onClick={handleRedirection}>Confirm</PrimaryButton>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DoctorCard;
