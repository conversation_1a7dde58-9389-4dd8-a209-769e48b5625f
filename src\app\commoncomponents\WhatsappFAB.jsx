import {Box, Fab} from '@mui/material';
import {useContext} from "react";
import {AppContext} from "@/app/AppContextLayout";
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import formatForWhatsApp from "@/app/utils/phoneUtils";

export const WhatsappFAB = () => {
    const {websiteData} = useContext(AppContext);
    const {phoneNumbers = []} = websiteData || {};
    const primaryWhatsappNo = phoneNumbers.filter((number) => number.is_whatsapp) || {};
    const handleSendMessage = () => {
        if (primaryWhatsappNo) {
            const msg = `Hi, I have an inquiry. Please assist!`;
            const encodedMessage = encodeURIComponent(msg);
            const phoneNo = formatForWhatsApp(primaryWhatsappNo[0].phone);
            const url = `https://wa.me/${phoneNo}/?text=${encodedMessage}`;
            window.open(url, "_blank");
        }
    };

    if (primaryWhatsappNo.length === 0)
        return <></>
    return (
        <Box sx={{
            display: {xs: "none", md: "block"},
            position: "fixed",
            bottom: 150,
            zIndex: 1000,
            right: 22,
            '& > :not(style)': {m: 1}
        }}>
            <Fab size="medium"
                 sx={{
                     color: 'common.white',
                     bgcolor: "#25D366",
                     '&:hover': {
                         bgcolor: "#1c9948",
                     },
                 }}
                 onClick={handleSendMessage}

            >
                <WhatsAppIcon sx={{color: "#FFFFFF"}}/>
            </Fab>
        </Box>
    );
}

export default WhatsappFAB;