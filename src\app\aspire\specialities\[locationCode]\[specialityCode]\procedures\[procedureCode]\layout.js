import { headers } from "next/headers";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_ENDPOINT_PROCEDURE,
} from "@/constants";
import axios from "axios";
import {getEnterpriseCode} from "@/app/aspire/blogs/location/[locationCode]/[blogCode]/layout";

const getProcedureDetails = async (enterpriseCode, procedureCode) => {
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${enterpriseCode}/${API_ENDPOINT_PROCEDURE}?code=${procedureCode}`;
  try {
    const response = await axios.get(url);
    const { status = null, data = {} } = response || {};
    if (status >= 200 && status < 300) {
      const { result = {} } = data || {};
      const { procedures = [] } = result || {};
      const { seoTitle = "", seoDescription = "" } = procedures[0] || {};
      return {
        title: seoTitle || "",
        description: seoDescription || "",
      };
    }
  } catch (error) {
    console.log(error);
  }
};

export const generateMetadata = async ({ params }) => {
  const { procedureCode = null, locationCode = null } = params || {};
  try {
    const enterpriseCode = await getEnterpriseCode(locationCode);
    const metaData = await getProcedureDetails(enterpriseCode, procedureCode);
    return metaData;
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
