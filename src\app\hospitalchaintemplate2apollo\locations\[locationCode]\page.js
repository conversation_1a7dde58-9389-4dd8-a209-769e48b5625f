"use client";
import HomepageBanner from "../../components/homepageBanner";
import WidgetsSection from "../../components/widgetsSection";
import PhotosSection from "../../components/photosSection";
import VideosSection from "../../components/videosSection";
import BlogsSection from "../../components/blogsSection";
import FaqsSection from "../../components/faqsSection";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  APOLLO_BELIAGHATA,
} from "@/constants";
import HomepageLocationBanner from "../../components/homepageLocationBanner";
import { getWebsiteHost } from "@/app/utils/clientOnly/clientUtils";
import ReviewsSection from "@/app/commoncomponents/reviewsSection";
import SectionLayoutChainTemp2 from "@/app/hospitalchaintemplate2apollo/styledComponents/SectionLayoutChainTemp2";
import { useEffect, useState } from "react";
import Loader from "@/app/commoncomponents/loader";
import { AppContext } from "@/app/AppContextLayout";
import Footer from "../../footer";

const getWebsiteData = async (locationCode) => {
  const domainName = getWebsiteHost();
  if (!locationCode) return;
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&domainSlug=${locationCode}`;
  try {
    const res = await fetch(url, {
      cache: "no-store",
    });
    const jsonRes = await res.json();
    return jsonRes.result || {};
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default function Home({ params }) {
  const { locationCode = null } = params || {};
  const [websiteData, setWebsiteData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const data = await getWebsiteData(locationCode);
      setWebsiteData(data);
    };
    fetchData();
  }, [locationCode]);

  if (!websiteData) return <Loader />;
  const {
    banners = [],
    multiMedia = [],
    testimonials = [],
    blogs = [],
    faqs = [],
  } = websiteData || {};
  const { enterprise_code: enterpriseCode = null } = websiteData || {};

  return (
    <main>
      <AppContext.Provider value={{ websiteData }}>
        <HomepageLocationBanner
          banners={banners}
          enterpriseCode={enterpriseCode}
          locationCode={locationCode}
        />
        <WidgetsSection />
        <PhotosSection multiMedia={multiMedia} />
        <VideosSection multiMedia={multiMedia} />
        <SectionLayoutChainTemp2>
          <ReviewsSection
            enterpriseCode={enterpriseCode}
            showDefaultReviews={true}
            testimonials={testimonials}
          />
        </SectionLayoutChainTemp2>
        {blogs.length > 0 && <BlogsSection blogs={blogs} />}
        {faqs.length > 0 && <FaqsSection faqs={faqs} />}
        <Footer />
      </AppContext.Provider>
    </main>
  );
}
