"use client";
import React, { useRef, useState, useEffect } from "react";
import { Box, Typography, Icon<PERSON>utton, Container, useMediaQuery, useTheme, CircularProgress } from "@mui/material";
import Link from "next/link";
import PackageCard from "./PackageCard";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
// Import Swiper components and styles
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { mapPackageDataForCard } from "../utils/dataMappers";
import { getLabPackages } from "@/api/harbor.service";
import "swiper/css";
import "swiper/css/navigation";

export default function PopularPackages({ packagesData, isLoading = false, enterpriseCode }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigationPrevRef = useRef(null);
  const navigationNextRef = useRef(null);

  // State for managing packages data and pagination
  const [packages, setPackages] = useState(packagesData.data || []);
  const [currentPage, setCurrentPage] = useState(packagesData.currentPage || 1);
  const [totalPages, setTotalPages] = useState(packagesData.totalPages || 1);
  const [totalCount, setTotalCount] = useState(packagesData.totalCount || 0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(
    (packagesData.currentPage || 1) < (packagesData.totalPages || 1)
  );

  const ANIMATION_CONFIG = {
    PEEK_ANIMATION_DELAY: 2000,
    PEEK_INTERVAL: 3000,
    PEEK_DURATION: 800,
    RETURN_DURATION: 600,
    RETURN_DELAY: 1000,
    PEEK_AMOUNT_RATIO: 0.3
  };

  const swiperRef = useRef(null);
  const [userInteracted, setUserInteracted] = useState(false);
  const peekIntervalRef = useRef(null);
  const returnTimeoutRef = useRef(null);

  const performPeekAnimation = () => {
    if (!swiperRef.current?.swiper || userInteracted) return;

    const swiper = swiperRef.current.swiper;
    const peekAmount = swiper.width * ANIMATION_CONFIG.PEEK_AMOUNT_RATIO;

    swiper.translateTo(-peekAmount, ANIMATION_CONFIG.PEEK_DURATION);

    returnTimeoutRef.current = setTimeout(() => {
      if (!userInteracted && swiperRef.current?.swiper) {
        swiperRef.current.swiper.translateTo(0, ANIMATION_CONFIG.RETURN_DURATION);
      }
    }, ANIMATION_CONFIG.RETURN_DELAY);
  };

  const cleanupAnimations = () => {
    if (peekIntervalRef.current) {
      clearInterval(peekIntervalRef.current);
      peekIntervalRef.current = null;
    }

    if (returnTimeoutRef.current) {
      clearTimeout(returnTimeoutRef.current);
      returnTimeoutRef.current = null;
    }

    if (swiperRef.current?.swiper) {
      swiperRef.current.swiper.translateTo(0, 0);
    }
  };

  const handleSwiperInteraction = () => {
    if (!userInteracted) {
      setUserInteracted(true);
      cleanupAnimations();
    }
  };

  // Function to load more data
  const loadMoreData = async () => {
    if (isLoadingMore || !hasMoreData || !enterpriseCode) return;

    try {
      setIsLoadingMore(true);
      const nextPage = currentPage + 1;

      // Use the API function from harbor.service
      const newData = await getLabPackages(enterpriseCode, { page: nextPage, perPage: 10 });

      if (newData && newData.data && newData.data.length > 0) {
        // Append new data to existing data
        setPackages(prevPackages => [...prevPackages, ...newData.data]);
        setCurrentPage(nextPage);
        setHasMoreData(nextPage < newData.totalPages);
      } else {
        setHasMoreData(false);
      }
    } catch (error) {
      console.error("Error loading more packages:", error);
      setHasMoreData(false);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Initialize state from props
  useEffect(() => {
    if (packagesData) {
      setPackages(packagesData.data || []);
      setCurrentPage(packagesData.currentPage || 1);
      setTotalPages(packagesData.totalPages || 1);
      setTotalCount(packagesData.totalCount || 0);
      setHasMoreData((packagesData.currentPage || 1) < (packagesData.totalPages || 1));
    }
  }, [packagesData]);

  // Handle peek animation
  useEffect(() => {
    let peekAnimationTimer;

    if (isMobile && !userInteracted) {
      peekAnimationTimer = setTimeout(() => {
        performPeekAnimation();

        peekIntervalRef.current = setInterval(
          performPeekAnimation,
          ANIMATION_CONFIG.PEEK_INTERVAL
        );
      }, ANIMATION_CONFIG.PEEK_ANIMATION_DELAY);
    } else {
      cleanupAnimations();
    }

    return () => {
      if (peekAnimationTimer) clearTimeout(peekAnimationTimer);
      cleanupAnimations();
    };
  }, [isMobile, userInteracted]);

  if (isLoading) {
    return (
      <Container maxWidth="xl" sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
          <CircularProgress size={40} />
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Loading popular packages...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
          position: "relative",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="h2" component="h2" fontWeight="bold" color="text.black" sx={{ fontSize: { xs: '1.5rem', md: '1.8rem' } }}>
            Popular Health Checkup Packages ({totalCount || packages.length})
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: { xs: "start", md: "center" }, gap: 2 }}>
          <IconButton
            ref={navigationPrevRef}
            aria-label="Previous packages"
            sx={{
              bgcolor: "white",
              boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
              "&:hover": { bgcolor: "white" },
              width: 32,
              height: 32,
              display: { xs: "none", md: "flex" },
            }}
          >
            <ArrowBackIosNewIcon fontSize="small" />
          </IconButton>
          <IconButton
            ref={navigationNextRef}
            aria-label="Next packages"
            sx={{
              bgcolor: "white",
              boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
              "&:hover": { bgcolor: "white" },
              width: 32,
              height: 32,
              display: { xs: "none", md: "flex" },
            }}
          >
            <ArrowForwardIosIcon fontSize="small" />
          </IconButton>
            <Link href="/tests?type=package" passHref aria-label="View all packages">
          <Typography
            component="span"
            sx={{
                textWrap: "nowrap",
              color: theme.palette.primary.main,
              cursor: "pointer",
              fontWeight: 500,
              fontSize: "0.9rem",
            }}
          >
            View All
          </Typography>
          </Link>
        </Box>
      </Box>
      <Box
        sx={{
          position: "relative",
          overflow: "visible",
        }}
      >
        <Swiper
          ref={swiperRef}
          modules={[Navigation]}
          loop={false} // Explicitly set to false to avoid loop warning
          loopAdditionalSlides={0} // Disable loop additional slides
          rewind={false} // Use rewind instead of loop for better behavior with few slides
          onTouchStart={handleSwiperInteraction}
          onTouchMove={handleSwiperInteraction}
          onClick={handleSwiperInteraction}
          navigation={{
            prevEl: navigationPrevRef.current,
            nextEl: navigationNextRef.current,
          }}
          onBeforeInit={(swiper) => {
            // Set swiper instances on navigation elements
            if (typeof swiper.params.navigation !== "boolean") {
              if (swiper.params.navigation) {
                swiper.params.navigation.prevEl = navigationPrevRef.current;
                swiper.params.navigation.nextEl = navigationNextRef.current;
              }
            }
            swiper.navigation.init();
            swiper.navigation.update();
          }}
          // Detect when user is near the end of slides to load more data
          onSlideChange={(swiper) => {
            const isNearEnd = swiper.isEnd ||
              (swiper.activeIndex >= swiper.slides.length - 3);

            if (isNearEnd && hasMoreData && !isLoadingMore) {
              loadMoreData();
            }
          }}
          spaceBetween={16}
          slidesPerView={1}
          grabCursor={true}
          breakpoints={{
            600: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            960: {
              slidesPerView: 3,
              spaceBetween: 24,
            },
            1280: {
              slidesPerView: 4,
              spaceBetween: 30,
            },
          }}
          style={{ padding: "8px 4px" }}
        >
          {/* Use the state variable instead of packagesData.data */}
          {packages.map((packageItem) => (
            <SwiperSlide key={packageItem.code}>
              <PackageCard packageData={mapPackageDataForCard(packageItem)} />
            </SwiperSlide>
          ))}

          {/* Loading indicator slide */}
          {isLoadingMore && (
            <SwiperSlide key="loading">
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                minHeight: 200
              }}>
                <CircularProgress size={30} />
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Loading more...
                </Typography>
              </Box>
            </SwiperSlide>
          )}
        </Swiper>
      </Box>
    </Container>
  );
}
