import { getEnterpriseCode } from "@/app/aspire/blogs/location/[locationCode]/[blogCode]/layout";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_ENDPOINT_PROFILE, API_SECTION_WEBSITE,
} from "@/constants";
import axios from "axios";
import {getWebsiteHost} from "@/app/utils/serverOnly/serverUtils";

export const generateMetadata = async ({ params }) => {
  const { locationCode = null } = params || {};
  const domainName = getWebsiteHost()
  const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}?domainName=${domainName}&domainSlug=${locationCode}`;
  try {
    const res = await fetch(url, {
      cache: "no-store"
    });
    const jsonRes = await res.json();
    const { seoData = [] } = jsonRes?.result || {};
    const { title = "", meta_description: metaDescription = "" } =
    seoData[0] || {};
    return {
      title: title || "Your Trusted Partner in Personalized Patient Care",
      description:
          metaDescription ||
          "A trusted platform for personalized patient care, offering tailored solutions and comprehensive services, prioritising comfort and satisfaction.",
    };
  } catch (error) {
    console.log("getWebsiteData", error);
  }
};

export default async function RootLayout({ children }) {
  return <>{children}</>;
}
