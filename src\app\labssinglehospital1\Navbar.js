"use client";

import { useContext, useEffect, useState } from "react";
import {
  Box,
  Typography,
  Container,
} from "@mui/material";
import Link from "next/link";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_WEBSITE,
  API_ENDPOINT_NAVBAR,
} from "@/constants";
import NavbarDropdown from "./components/navbarDropdown";
import { useTheme } from "@emotion/react";
import useStyles from "./styles";
import MobileNav from "./components/MobileNav";
import TopNavbar from "./components/TopNavbar";
import { AppContext } from "@/app/AppContextLayout";

const Navbar = () => {
  const classes = useStyles();
  const theme = useTheme();
  const { desktopView, websiteData } = useContext(AppContext) || {};
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [navbarItemsList, setNavbarItemsList] = useState([]);
  const {
    logo_url: logoUrl = null,
    phoneNumbers = [],
    enterprise_code: enterpriseCode = null,
  } = websiteData || {};
   const primaryPhone = phoneNumbers?.[0]?.phone || ""

  const handleCloseMenu = () => {
    setIsMenuOpen(false);
  };

  const getNavbarItems = async () => {
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_NAVBAR}${enterpriseCode}/`;
    try {
      const response = await axios.get(url);
      const { status = null, data = {} } = response || {};
      if (status >= 200 && status < 300) {
        const { result = [] } = data || {};
        setNavbarItemsList(result);
      }
    } catch (error) {
      // Error handling
      console.error(error);
    }
  };

  useEffect(() => {
    if (enterpriseCode) getNavbarItems();
  }, [enterpriseCode]);

  return (
    <>
    <Box
      sx={{
        boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
        position: "sticky",
        top: 0,
        zIndex: 1000,
        backgroundColor: "white",
      }}
      >
      <TopNavbar websiteData={websiteData}/>
      <Container
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
        maxWidth="xl"
      >
        {/* <Box>
          {logoUrl ? (
            <Link href="/">
              <Image
                alt="logo"
                src={getThumborUrl(logoUrl)}
                width={210}
                height={70}
                style={{ cursor: "pointer", }}
              />
            </Link>
          ) : (
            <Link href="/">
              <Typography
                variant="h4"
                sx={{
                  color: "#00a99d",
                  fontWeight: "bold",
                  cursor: "pointer",
                  fontSize: "28px",
                }}
              >
                LOGO
              </Typography>
            </Link>
          )}
        </Box> */}
      <Box
        sx={{
          py: 0,
        }}
      >
        {desktopView ? (
          <Box sx={{ display: "flex", alignItems: "center", gap: 3 }}>
            <ul className={classes.navbarList}>
              {(navbarItemsList[0]?.sections || []).map((item, index) => {
                const {
                  displayName = "",
                  redirection = {},
                  sections = null,
                  type = 1,
                } = item || {};
                const { redirectionUrl = "" } = redirection || {};
                if (type === 2)
                  return (
                    <Box
                      id={`navbarSection0Item${index}`}
                      sx={{
                        "&:hover": { color: "text.black" },
                        color: "text.black",
                      }}
                    >
                      <Link href={redirectionUrl} target="_blank">
                        <Typography fontSize="16px">
                          {displayName || ""}
                        </Typography>
                      </Link>
                    </Box>
                  );
                else if (sections)
                  return (
                    <NavbarDropdown
                      key={`${displayName}${index}`}
                      navbarItem={item}
                      id={`navbarSection0Item${index}`}
                    />
                  );
                else
                  return (
                    <Box
                      id={`navbarSection0Item${index}`}
                      sx={{
                        "&:hover": { color: "text.black" },
                        color: "text.black",
                      }}
                    >
                      <Link href={redirectionUrl}>
                        <Typography fontSize="16px">
                          {displayName || ""}
                        </Typography>
                      </Link>
                    </Box>
                  );
              })}
            </ul>
            {/* <BookAppointmentButton /> */}
          </Box>
        ) : (
          <MobileNav
            primaryPhone={primaryPhone}
            isMenuOpen={isMenuOpen}
            setIsMenuOpen={setIsMenuOpen}
            navbarItemsList={navbarItemsList}
            logoUrl={logoUrl}
            classes={classes}
            handleCloseMenu={handleCloseMenu}
            enterpriseCode={enterpriseCode}
          />
        )}
      </Box>
      </Container>
    </Box>
    </>
  );
};

export default Navbar;