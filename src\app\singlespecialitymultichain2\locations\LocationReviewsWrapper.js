import { Box, Container, Typography } from '@mui/material'
import React from 'react'
import ReviewsSlider from '../components/ReviewsSlider'

const LocationReviewsWrapper = ({testimonials =[] ,heading= "What our patients have to say about Us"}) => {
  return (
    <Box
    sx={{
      width: "100%",
      backgroundColor: "#f8f8f8",
      py: "24px",
      // px: { xs: 2, md: 0 },
      position: "relative",
    }}
  >
    {/* <StarPattern/> */}
    <Container maxWidth="lg">
      <Box
        sx={{
          textAlign: "center",
          mb: { xs: 4, sm: 5, md: 3 },
          maxWidth: "800px",
          mx: "auto",
        }}
      >
        <Typography
          variant="h4"
          align="center"
          fontWeight={400}
          sx={{
            mb: 2,
            fontSize: {
              xs: "24px",
              md: "36px",
            },
            color:"text.black"
          }}
        >
          {heading || "What our patients have to say about Us"}
        </Typography>
      </Box>
      <ReviewsSlider testimonials={testimonials} />
    </Container>
  </Box>
  )
}

export default LocationReviewsWrapper