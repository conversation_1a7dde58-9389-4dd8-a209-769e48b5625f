import { useState } from "react";
import Box from "@mui/material/Box";
import HealthAndSafetyOutlinedIcon from "@mui/icons-material/HealthAndSafetyOutlined";
import KeyboardArrowDownOutlinedIcon from "@mui/icons-material/KeyboardArrowDownOutlined";
import { useRouter } from "next/navigation";
import {Typography} from "@mui/material";

const NavbarDropdownItem = ({ section = {}, setAnchorEl, isDrawerOpen = false, handleCloseDrawer }) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const {
    displayName = "",
    sections = [],
    redirection = {},
    type = 1,
  } = section || {};

  const handleClick = () => {
    if (!sections) {
      const { redirectionUrl = "" } = redirection || {};
      if (isDrawerOpen) handleCloseDrawer();
      setAnchorEl(null);
      if (type === 2) {
        window.open(redirectionUrl, "_blank");
      } else router.push(redirectionUrl);
    } else setIsOpen((prev) => !prev);
  };

  return (
    <Box sx={{ padding: "8px 16px" }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
          justifyContent: "space-between",
          fontSize: "14px",
          cursor: "pointer",
          transition: "all .3s",
          "&:hover": { color: "primary.main" }
        }}
        onClick={handleClick}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <HealthAndSafetyOutlinedIcon
            fontSize="large"
            sx={{ color: "primary.main" }}
          />{" "}
            <Typography
                fontSize="14px"
            >
                {displayName || ""}
            </Typography>
        </Box>
        {sections !== null && <KeyboardArrowDownOutlinedIcon />}
      </Box>
      {sections !== null &&
        isOpen &&
        sections.map((section, index) => {
          const { displayName = "" } = section || {};
          return (
            <NavbarDropdownItem
              key={`${displayName}${index}`}
              section={section}
              setAnchorEl={setAnchorEl}
            />
          );
        })}
    </Box>
  );
};

export default NavbarDropdownItem;
