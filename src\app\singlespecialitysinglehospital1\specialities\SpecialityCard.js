"use client";

import { useTheme } from "@emotion/react";
import { Box, Typography } from "@mui/material";
import MedicationIcon from "@mui/icons-material/Medication";
import { alpha } from "@mui/material/styles";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { getThumborUrl } from "../../utils/getThumborUrl";

const SpecialityCard = ({ speciality = {}, id = null }) => {
  const theme = useTheme();
  const router = useRouter();
  const {
    displayName: specialityName = "",
    shortDescription = "",
    iconUrl = "",
    code: specialityCode = null,
    seoSlug = ""
  } = speciality || {};

  const handleSpecialityClick = () => {
    router.push(`/specialities/${seoSlug}`);
  };

  return (
    <Box
      id={id}
      sx={{
        padding: "16px 24px",
        boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.1)}`,
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        alignItems: "center",
        borderRadius: "12px",
        cursor: "pointer",
      }}
      onClick={handleSpecialityClick}
    >
      <Box
        sx={{
          height: "64px",
          width: "64px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          background: theme.palette.primary.main,
          borderRadius: "50%",
        }}
      >
        {iconUrl ? (
          <Image
            alt="speciality"
            height={35}
            width={35}
            src={getThumborUrl(iconUrl, 35, 35)}
          />
        ) : (
          <MedicationIcon fontSize="large" sx={{ color: "#fff" }} />
        )}
      </Box>
      <Box>
        <Typography align="center" variant="h6" sx={{ fontSize: "18px" }}>
          {specialityName || ""}
        </Typography>
        <Typography
          variant="subtitle1"
          align="center"
          sx={{ fontSize: "14px", color: "rgba(0, 0, 0, 0.6)" }}
        >
          {shortDescription || ""}
        </Typography>
      </Box>
    </Box>
  );
};

export default SpecialityCard;
