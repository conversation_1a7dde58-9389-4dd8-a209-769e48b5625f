"use client";

import {
  COUNTRIES,
  countryToFlag,
  LEAD_SOURCES,
  PAGE_NAMES,
} from "@/constants";
import {
  Button,
  CircularProgress,
  InputBase,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import Box from "@mui/material/Box";
import EmailIcon from "@mui/icons-material/Email";
import React, { useContext, useState } from "react";
import axios from "axios";
import {
  HARBOR_API_DOCFYN_DOMAIN,
  API_SECTION_API,
  API_VERSION,
  API_SECTION_PUBLIC,
  API_SECTION_ENTERPRISE,
  API_SECTION_LEADS,
  API_ENDPOINT_GENERATE,
} from "@/constants";
import { AppContext } from "../AppContextLayout";
import DoctorAppointmentForm from "@/app/singlespecialitymultichain1/components/doctorAppointmentForm";

const initialInput = { dialCode: "+91", type: 1 };

const QuickEnquiry = ({ pageData = {} }) => {
  const { websiteData, setViewSnackbarMain, mobileView } =
    useContext(AppContext);
  const [input, setInput] = useState({ ...initialInput });
  const [isLoading, setIsLoading] = useState(false);
  // const {enterprise_code: enterpriseCode = null} = websiteData || {};
  const handleInputChange = (e) => {
    setInput((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  const handleLeadGeneration = async () => {
    setIsLoading(true);
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_ENTERPRISE}${pageData.enterpriseCode}/${API_SECTION_LEADS}${API_ENDPOINT_GENERATE}${pageData.productCode ? `?productCode=${extractCodeFromSlug(pageData.productCode)}` : ""}`;
    // const reqBody = {...input, leadSource};
    const reqBody = {
      phone: input.phone,
      firstName: input.firstName,
      dialCode: input.dialCode,
      leadSource: pageData.leadSource,
      type: 1,
    };

    if (pageData?.productCode) {
      switch (pageData.leadSource) {
        case LEAD_SOURCES.BLOG_DETAIL_PAGE:
          reqBody.blogCode = extractCodeFromSlug(pageData.productCode);
          console.log("blogCode ", reqBody.blogCode);
          break;
        case LEAD_SOURCES.PROCEDURE_PAGE:
          reqBody.procedureCode = extractCodeFromSlug(pageData.productCode);
          console.log("procedureCode ", reqBody.procedureCode);
          break;
        case LEAD_SOURCES.SPECIALITY_DETAIL_PAGE:
          reqBody.specialityCode = extractCodeFromSlug(pageData.productCode);
          console.log("specialityCode ", reqBody.specialityCode);
          break;
        case LEAD_SOURCES.CUSTOM_PAGE:
          reqBody.customPageCode = extractCodeFromSlug(pageData.productCode);
          console.log("customPageCode ", reqBody.customPageCode);
          break;
        case LEAD_SOURCES.DOCTOR_DETAIL_PAGE:
          reqBody.doctorCode = extractCodeFromSlug(pageData.productCode);
          console.log("doctorCode ", reqBody.doctorCode);
          break;
      }
    }
    if (input?.["comments"]) {
      reqBody.comments = input["comments"];
    }
    if (pageData?.pageTitle) {
      reqBody.pageTitle = pageData.pageTitle;
    }
    if (pageData?.specialityCode) {
      reqBody.specialityCode = extractCodeFromSlug(pageData.specialityCode);
      console.log("specialityCode ", reqBody.specialityCode);
    }

    try {
      const response = await axios.post(url, reqBody, {
        headers: {
          "Content-Type": "application/json",
          source: mobileView ? "mweb" : "website",
        },
      });
      const { data = {}, status = null } = response || {};
      if (status >= 200 && status < 300) {
        const { result = {} } = data || {};
        const { message = "" } = result || {};
        setViewSnackbarMain({
          message: message,
          type: "success",
        });
        setInput({ ...initialInput });
      }
    } catch (error) {
      setViewSnackbarMain({
        message: "Something went wrong. Please try again later!",
        type: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const extractCodeFromSlug = (slug) => {
    const segments = slug.split("-");
    return segments[segments.length - 1];
  };

  return (
    <Box
      sx={{
        padding: "18px",
        display: "flex",
        flexDirection: "column",
        gap: "16px",
        borderRadius: "8px",
        bgcolor: "primary.main",
        alignSelf: "start",
        width: "100%",
      }}
    >
      <Typography variant="h6" align="center" sx={{ color: "#fff" }}>
        Request a Callback
      </Typography>
      <Box sx={{ display: "flex", flexDirection: "column", gap: "16px" }}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          <Box sx={{ display: "flex", gap: "8px" }}>
            <Select
              id="quickEnquiryCountryCode"
              labelId="demo-customized-select-label"
              value={input["dialCode"]}
              onChange={(e) =>
                setInput((prev) => ({ ...prev, dialCode: e.target.value }))
              }
              input={
                <InputBase
                  sx={{
                    color: "#1a1a1a",
                    borderRadius: "4px",
                    padding: "8px 12px",
                    fontSize: "14px",
                    width: "70px",
                    transition: "all .3s",
                    background: "#fff",
                  }}
                  inputProps={{ "aria-label": "enter your name" }}
                />
              }
            >
              {COUNTRIES.map((country) => {
                const {
                  label = "",
                  dial_code: dialCode = "",
                  code = "IN",
                  value = "",
                } = country || {};
                return (
                  <MenuItem
                    sx={{ color: "#1a1a1a" }}
                    value={dialCode}
                  >{`${countryToFlag(code)} ${value}`}</MenuItem>
                );
              })}
            </Select>
            <InputBase
              name="phone"
              id="quickEnquiryPhone"
              sx={{
                color: "#1a1a1a",
                borderRadius: "4px",
                padding: "8px 12px",
                fontSize: "14px",
                width: "100%",
                transition: "all .3s",
                background: "#fff",
              }}
              value={input["phone"] || ""}
              onChange={handleInputChange}
              placeholder="Enter Phone Number"
              inputProps={{ "aria-label": "enter your name" }}
            />
          </Box>
          {/*<Box*/}
          {/*    // sx={{display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px"}}*/}
          {/*>*/}
          <InputBase
            name="firstName"
            id="quickEnquiryFirstName"
            sx={{
              color: "#1a1a1a",

              borderRadius: "4px",
              padding: "8px 12px",
              fontSize: "14px",
              width: "100%",
              transition: "all .3s",
              background: "#fff",
            }}
            value={input["firstName"] || ""}
            onChange={handleInputChange}
            placeholder="Enter Name"
            inputProps={{ "aria-label": "enter your name" }}
          />
          {/*<InputBase*/}
          {/*    name="lastName"*/}
          {/*    id="quickEnquiryLastName"*/}
          {/*    sx={{*/}
          {/*        borderRadius: "4px",*/}
          {/*        padding: "8px 12px",*/}
          {/*        fontSize: "14px",*/}
          {/*        width: "100%",*/}
          {/*        transition: "all .3s",*/}
          {/*        background: "#fff",*/}
          {/*    }}*/}
          {/*    value={input["lastName"] || ""}*/}
          {/*    onChange={handleInputChange}*/}
          {/*    placeholder="Enter Last Name"*/}
          {/*    inputProps={{"aria-label": "enter your name"}}*/}
          {/*/>*/}
          {/*</Box>*/}
          {/*<InputBase*/}
          {/*    name="email"*/}
          {/*    id="quickEnquiryEmail"*/}
          {/*    sx={{*/}
          {/*        borderRadius: "4px",*/}
          {/*        padding: "8px 12px",*/}
          {/*        fontSize: "14px",*/}
          {/*        width: "100%",*/}
          {/*        transition: "all .3s",*/}
          {/*        background: "#fff",*/}
          {/*    }}*/}
          {/*    value={input["email"] || ""}*/}
          {/*    onChange={handleInputChange}*/}
          {/*    placeholder="Enter Email"*/}
          {/*    inputProps={{"aria-label": "enter your name"}}*/}
          {/*/>*/}
          <InputBase
            name="comments"
            id="quickEnquiryComments"
            sx={{
              color: "#1a1a1a",
              borderRadius: "4px",
              padding: "8px 12px",
              fontSize: "14px",
              width: "100%",
              transition: "all .3s",
              background: "#fff",
            }}
            multiline
            minRows={1.5}
            maxRows={4}
            value={input["comments"] || ""}
            onChange={handleInputChange}
            placeholder="Comments (Optional)"
            inputProps={{ "aria-label": "enter your name" }}
          />
        </Box>
        <Button
          //   color="primary"
          id="quickEnquirySubmit"
          sx={{
            bgcolor: "secondary.main",
            color: "#fff",
            "&:hover": { bgcolor: "secondary.main" },
          }}
          disabled={isLoading}
          onClick={handleLeadGeneration}
        >
          {isLoading ? <CircularProgress size={24} /> : "Submit"}
        </Button>
        {/* <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: "4px",
            color: "#fff",
          }}
        >
          <EmailIcon />
          <Typography
            variant="subtitle2"
            align="center"
            sx={{ fontSize: "14px", color: "#fff" }}
          >
            <EMAIL>
          </Typography>
        </Box> */}
      </Box>
    </Box>
  );
};

export default QuickEnquiry;
