"use client";

import Image from "next/image";
import { useState, useCallback } from "react";
import { alpha, useTheme } from "@mui/material/styles";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Box,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const ServiceCard = ({ icon, title, description }) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [hover, setHover] = useState(false);

  const handleOpen = useCallback(() => setOpen(true), []);
  const handleClose = useCallback(() => setOpen(false), []);

  return (
    <>
      <Box
        onClick={handleOpen}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        sx={{
          height: { xs: '220px', sm: '240px' },
          position: "relative",
          display: "flex",
          flexDirection: "column",
          borderRadius: "20px",
          backgroundColor: "rgb(255, 255, 255)",
          border: "1px solid #eee",
          transition: "all 0.3s ease-in-out",
          cursor: "pointer",
          width: "100%",
          maxWidth: { xs: '280px', sm: '250px' },
          overflow: "hidden",
          boxShadow: hover ? '0 8px 16px rgba(0,0,0,0.1)' : '0 4px 8px rgba(0,0,0,0.05)',
          transform: hover ? 'translateY(-5px)' : 'translateY(0)',
          '&:active': {
            transform: 'scale(0.98)',
          }
        }}
      >
        <Box
          sx={{
            position: "absolute",
            backgroundColor: alpha(theme.palette.secondary.main, 0.1),
            height: "100%",
            width: "100%",
            top: hover ? "0%" : "-100%",
            transition: "top 0.4s ease-in-out",
            zIndex: 1,
          }}
        />

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "flex-start",
            height: "100%",
            padding: { xs: 2, sm: 3 },
            position: "relative",
            zIndex: 2,
          }}
        >
          <Box
            sx={{
              backgroundColor: alpha(theme.palette.secondary.main, 0.1),
              borderRadius: "50%",
              padding: { xs: 1.5, sm: 2 },
              mb: 2,
            }}
          >
            <Image 
              src={icon || "/services.svg"} 
              width={40} 
              height={35} 
              alt={title}
              style={{
                objectFit: 'contain',
                transition: 'transform 0.3s ease',
                transform: hover ? 'scale(1.1)' : 'scale(1)',
              }}
            />
          </Box>

          <Typography
            variant="h6"
            component="h3"
            sx={{
              color: "#1a1a1a",
              textAlign: "center",
              fontSize: { xs: '1rem', sm: '1.125rem' },
              mb: 1.5,
              height: '2.5em',
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              lineHeight: 1.2,
            }}
          >
            {title}
          </Typography>

          <Typography
            variant="body2"
            sx={{
              color: "rgb(75, 75, 75)",
              textAlign: "center",
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
              textOverflow: "ellipsis",
              fontSize: { xs: '0.813rem', sm: '0.875rem' },
              lineHeight: 1.5,
              opacity: 0.85,
            }}
          >
            {description}
          </Typography>
        </Box>
      </Box>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            padding: { xs: 1, sm: 2 },
          }
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            pb: 2,
          }}
        >
          <Box
            sx={{
              backgroundColor: alpha(theme.palette.secondary.main, 0.1),
              borderRadius: "50%",
              padding: 1.5,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Image src={icon} width={30} height={30} alt={title} />
          </Box>
        <Typography variant="h6" sx={{color:"#1a1a1a"}} component="span">
            {title}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Typography variant="body1" sx={{ lineHeight: 1.6,color:"rgb(75,75,75)" }}>
            {description}
          </Typography>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ServiceCard;