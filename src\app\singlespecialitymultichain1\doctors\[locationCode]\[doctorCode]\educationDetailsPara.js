import { Typography } from "@mui/material";

const EducationDetailsPara = ({educationDetails = []}) => {
    return (
        <Typography variant="subtitle1" sx={{ fontSize: "14px" }}>
            {educationDetails.map((detail, index) => {
                const { degree = {} } = detail || {};
                const { code = null, name: degreeName = "" } = degree || {};
                return (
                    <span key={code}>{`${index > 0 ? ` ${degreeName || ""}` : (degreeName || "")
                        } ${index < educationDetails.length - 1 ? "," : ""}`}</span>
                );
            })}
        </Typography>
    )
}

export default EducationDetailsPara