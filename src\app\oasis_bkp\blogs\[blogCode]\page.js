import SectionLayout from "@/app/oasis/styledComponents/SectionLayout";
import {Box, Typography} from "@mui/material";
import Image from "next/image";
import {
    HARBOR_API_DOCFYN_DOMAIN,
    API_SECTION_API,
    API_VERSION,
    API_SECTION_PUBLIC,
    API_SECTION_WEBSITE,
    API_ENDPOINT_BLOGS,
} from "@/constants";
import {getThumborUrl} from "@/app/utils/getThumborUrl";
import {getEnterpriseCode} from "@/app/oasis/blogs/[blogCode]/layout";
import dynamic from "next/dynamic";


const QuickEnquiry = dynamic(() => import('@/app/commoncomponents/quickEnquiry'), {
    ssr: false
})

const BlogUrlReplacer = dynamic(() => import('./BlogUrlReplacer'), {
    ssr: false
})

const getBlogDetails = async (blogCode, enterpriseCode,) => {
    if (!blogCode || !enterpriseCode) return;
    const url = `${HARBOR_API_DOCFYN_DOMAIN}${API_SECTION_API}${API_VERSION}/${API_SECTION_PUBLIC}${API_SECTION_WEBSITE}${API_ENDPOINT_BLOGS}${enterpriseCode}/?code=${blogCode}`;
    try {
        const response = await fetch(url);
        const status = response.status;
        const data = await response.json();
        if (status === 200) {
            const {result = []} = data || {};
            console.log("check here result "+ result)
            return result;
        }
    } catch (error) {
        console.log(error)
    }
};
const BlogDetail = async ({params}) => {

    const {blogCode = null} = params || {};
    const enterpriseCode = await getEnterpriseCode();
    const blogDetail = await getBlogDetails(blogCode, enterpriseCode)
    if (blogDetail.length === 0)
        return null

    const {
        title = "",
        content = "",
        imageUrl = "",
        seoSlug = ""
    } = blogDetail[0] || {};

    return (
        <Box>
            <SectionLayout>
                <Box sx={{display: "flex", flexDirection: "column", gap: "48px"}}>
                    {imageUrl && (
                        <Box
                            sx={{
                                minHeight: {xs: "240px", sm: "320px", md: "400px"},
                                position: "relative"
                            }}
                        >
                            <Image
                                alt="blog-banner"
                                fill
                                sizes="(max-width: 768px) 100vw, 700px"
                                priority
                                src={getThumborUrl(imageUrl)}
                                style={{
                                    height: "100%",
                                    width: "100%",
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    objectFit: "contain",
                                    borderRadius: "10px",
                                }}
                            />
                        </Box>
                    )}
                    <Typography
                        variant="h3"
                        align="center"
                        sx={{
                            fontWeight: "500",
                            color: "primary.main",
                            fontSize: {xs: "1.75rem", sm: "40px"},
                        }}
                    >
                        {title || ""}
                    </Typography>
                    <Box sx={{display: "grid", gridTemplateColumns: {xs: "1fr", lg: "1fr .5fr"}, gap: "24px"}}>
                        <div
                            className="ck-content"
                            dangerouslySetInnerHTML={{__html: content}} // Render HTML safely
                        />
                        <QuickEnquiry leadSource={7} productCode={blogCode}/>
                    </Box>
                </Box>
            </SectionLayout>
          <BlogUrlReplacer blogCode={blogCode} seoSlug={seoSlug}/>
        </Box>
    );
}

export default BlogDetail;